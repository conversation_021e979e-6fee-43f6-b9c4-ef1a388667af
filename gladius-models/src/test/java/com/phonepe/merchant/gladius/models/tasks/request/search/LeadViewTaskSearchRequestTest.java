package com.phonepe.merchant.gladius.models.tasks.request.search;

import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class LeadViewTaskSearchRequestTest {

    @Test
    void testDefaultConstructor() {
        LeadViewTaskSearchRequest request = new LeadViewTaskSearchRequest();

        assertNotNull(request);
        assertEquals(TaskSearchRequestType.LEAD_VIEW, request.getTaskSearchRequestType());
        assertNull(request.getAssignedTo());
        assertFalse(request.isNeedScheduledTasks());
        assertNull(request.getStartDate());
        assertNull(request.getEndDate());
    }

    @Test
    void testParameterizedConstructor() {
        String assignedTo = "agent123";
        Boolean needScheduledTasks = true;
        Long startDate = 1633024800000L;
        Long endDate = 1633111200000L;

        LeadViewTaskSearchRequest request = new LeadViewTaskSearchRequest(
                TaskSearchRequestType.LEAD_VIEW, assignedTo, needScheduledTasks, startDate, endDate);

        assertNotNull(request);
        assertEquals(TaskSearchRequestType.LEAD_VIEW, request.getTaskSearchRequestType());
        assertEquals(assignedTo, request.getAssignedTo());
        assertTrue(request.isNeedScheduledTasks());
        assertEquals(startDate, request.getStartDate());
        assertEquals(endDate, request.getEndDate());
    }

    @Test
    void testBuilderInitialization() {
        String assignedTo = "agent123";
        Boolean needScheduledTasks = false;
        Long startDate = 1633024800000L;
        Long endDate = 1633111200000L;

        LeadViewTaskSearchRequest request = LeadViewTaskSearchRequest.builder()
                .taskSearchRequestType(TaskSearchRequestType.LEAD_VIEW)
                .assignedTo(assignedTo)
                .needScheduledTasks(needScheduledTasks)
                .startDate(startDate)
                .endDate(endDate)
                .build();

        assertNotNull(request);
        assertEquals(TaskSearchRequestType.LEAD_VIEW, request.getTaskSearchRequestType());
        assertEquals(assignedTo, request.getAssignedTo());
        assertFalse(request.isNeedScheduledTasks());
        assertEquals(startDate, request.getStartDate());
        assertEquals(endDate, request.getEndDate());
    }

    @Test
    void testSettersAndGetters() {
        LeadViewTaskSearchRequest request = new LeadViewTaskSearchRequest();

        String assignedTo = "agent456";
        Boolean needScheduledTasks = true;
        Long startDate = 1633024800000L;
        Long endDate = 1633111200000L;

        request.setAssignedTo(assignedTo);
        request.setNeedScheduledTasks(needScheduledTasks);
        request.setStartDate(startDate);
        request.setEndDate(endDate);

        assertEquals(assignedTo, request.getAssignedTo());
        assertTrue(request.isNeedScheduledTasks());
        assertEquals(startDate, request.getStartDate());
        assertEquals(endDate, request.getEndDate());
    }
}