package com.phonepe.merchant.gladius.models.tasks.response;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;


class TaskMetaInformationTest {

    private final ObjectMapper mapper = new ObjectMapper();

    @Test
    void testConstructorAndInitialize() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.MID, "12345", true);

        assertEquals(TaskMetaType.MID, info.getType());
        assertEquals("12345", info.getValue());
        assertEquals(TaskMetaType.MID.getDisplayText(), info.getDisplayText());
        assertTrue(info.getDisplayInformation());
    }

    @Test
    void testWorkflowIdForcesDisplayInformationFalse() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.WORKFLOW_ID, "wf-1", true);

        assertEquals(TaskMetaType.WORKFLOW_ID, info.getType());
        assertFalse(info.getDisplayInformation(), "displayInformation should always be false for WORKFLOW_ID");
    }

    @Test
    void testSetTypeUpdatesDisplayText() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.MID, "12345", true);
        info.setType(TaskMetaType.SID);

        assertEquals(TaskMetaType.SID, info.getType());
        assertEquals(TaskMetaType.SID.getDisplayText(), info.getDisplayText());
    }

    @Test
    void testGetValueForString() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.MID, "value123", true);

        Object value = info.getValue();
        assertEquals("value123", value);
    }

    @Test
    void testGetValueForDouble() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.LAST_MONTH_TPAM, "45.67", true);

        Object value = info.getValue();
        assertEquals(45.67, (Double) value);
    }

    @Test
    void testGetValueForInvalidDouble() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.LAST_MONTH_TPV, "notANumber", true);

        Object value = info.getValue();
        assertNull(value, "Invalid double string should return null");
    }

    @Test
    void testGetValueForList() {
        List<String> list = List.of("a", "b");
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.COMP_VPA, list, true);

        Object value = info.getValue();
        assertTrue(value instanceof List);
        assertEquals(2, ((List<?>) value).size());
    }

    @Test
    void testGetValueForListWhenNotList() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.COMP_VPA, "notList", true);

        Object value = info.getValue();
        assertTrue(value instanceof List);
        assertTrue(((List<?>) value).isEmpty());
    }

    @Test
    void testGetValueForBooleanTrue() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.REDIRECT_TO_NEW_LMS_FLOW, true, true);

        Object value = info.getValue();
        assertTrue((Boolean) value);
    }

    @Test
    void testGetValueForBooleanFromString() {
        TaskMetaInformation info = new TaskMetaInformation(TaskMetaType.REDIRECT_TO_NEW_LMS_FLOW, "true", true);

        Object value = info.getValue();
        assertTrue((Boolean) value);
    }

    @Test
    void testJsonDeserialization() throws Exception {
        String json = """
                {
                  "type": "MID",
                  "value": "merchant123",
                  "displayInformation": true
                }
                """;

        TaskMetaInformation info = mapper.readValue(json, TaskMetaInformation.class);

        assertEquals(TaskMetaType.MID, info.getType());
        assertEquals("merchant123", info.getValue());
        assertTrue(info.getDisplayInformation());
    }
}
