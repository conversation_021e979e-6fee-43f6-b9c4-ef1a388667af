package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionTenants;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TaskDefinitionInstanceTest {

    @Test
    void testIsLeadCreationAllowed_WhenLeadCreationIsNotEmpty_ShouldReturnTrue() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        TaskDefinitionAttributes definitionAttributes = new TaskDefinitionAttributes();
        LeadConfig leadConfig = new LeadConfig();
        leadConfig.setLeadCreation(List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder().intent("test").build())).build()));
        definitionAttributes.setLeadConfig(leadConfig);
        taskDefinitionInstance.setDefinitionAttributes(definitionAttributes);
        boolean result = taskDefinitionInstance.isLeadCreationAllowed();
        assertTrue(result);
    }

    @Test
    void testIsLeadCreationAllowed_WhenLeadCreationIsEmpty_ShouldReturnFalse() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        TaskDefinitionAttributes definitionAttributes = new TaskDefinitionAttributes();
        LeadConfig leadConfig = new LeadConfig();
        leadConfig.setLeadCreation(List.of());
        definitionAttributes.setLeadConfig(leadConfig);
        taskDefinitionInstance.setDefinitionAttributes(definitionAttributes);
        boolean result = taskDefinitionInstance.isLeadCreationAllowed();
        assertFalse(result);
    }

    @Test
    void testIsLeadCreationAllowed_WhenLeadCreationIsNull_ShouldReturnFalse() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        TaskDefinitionAttributes definitionAttributes = new TaskDefinitionAttributes();
        LeadConfig leadConfig = new LeadConfig();
        leadConfig.setLeadCreation(null);
        definitionAttributes.setLeadConfig(leadConfig);
        taskDefinitionInstance.setDefinitionAttributes(definitionAttributes);
        boolean result = taskDefinitionInstance.isLeadCreationAllowed();
        assertFalse(result);
    }


    @Test
    void testIsLeadCreationAllowed_WhenLeadConfigIsNull_ShouldReturnFalse() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        TaskDefinitionAttributes definitionAttributes = new TaskDefinitionAttributes();
        definitionAttributes.setLeadConfig(null);
        taskDefinitionInstance.setDefinitionAttributes(definitionAttributes);
        boolean result = taskDefinitionInstance.isLeadCreationAllowed();
        assertFalse(result);
    }

    @Test
    void testIsLeadUpdationAllowed_WhenDefinitionAttributesIsNull_ShouldReturnFalse() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        taskDefinitionInstance.setDefinitionAttributes(null);
        boolean result = taskDefinitionInstance.isLeadUpdationAllowed();
        assertFalse(result);
    }

    @Test
    void testIsLeadUpdationAllowed_WhenLeadUpdationIsNotEmpty_ShouldReturnTrue() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        TaskDefinitionAttributes definitionAttributes = new TaskDefinitionAttributes();
        LeadConfig leadConfig = new LeadConfig();
        leadConfig.setLeadUpdation(List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder().build())).build())); // Non-empty set
        definitionAttributes.setLeadConfig(leadConfig);
        taskDefinitionInstance.setDefinitionAttributes(definitionAttributes);
        boolean result = taskDefinitionInstance.isLeadUpdationAllowed();
        assertTrue(result);
    }

    @Test
    void testIsLeadUpdationAllowed_WhenLeadUpdationIsEmpty_ShouldReturnFalse() {
        TaskDefinitionInstance taskDefinitionInstance = new TaskDefinitionInstance();
        TaskDefinitionAttributes definitionAttributes = new TaskDefinitionAttributes();
        LeadConfig leadConfig = new LeadConfig();
        leadConfig.setLeadUpdation(List.of());
        definitionAttributes.setLeadConfig(leadConfig);
        taskDefinitionInstance.setDefinitionAttributes(definitionAttributes);
        boolean result = taskDefinitionInstance.isLeadUpdationAllowed();
        assertFalse(result);
    }

    @Test
    void testGetAllClients_WhenTenantsPresent_ShouldReturnTenantsSet() {
        TaskDefinitionInstance instance = new TaskDefinitionInstance();
        TaskDefinitionAttributes attributes = new TaskDefinitionAttributes();
        // Assuming Tenants class has a setTenants and getTenants method
        TaskDefinitionTenants tenants = new TaskDefinitionTenants();
        tenants.setTenants(Set.of("clientA", "clientB"));
        attributes.setTenants(tenants);
        instance.setDefinitionAttributes(attributes);

        Set<String> result = instance.getAllClients();
        assertTrue(result.contains("clientA"));
        assertTrue(result.contains("clientB"));
        assertEquals(2, result.size());
    }

    @Test
    void testGetAllClients_WhenNoTenants_ShouldReturnEmptySet() {
        TaskDefinitionInstance instance = new TaskDefinitionInstance();
        TaskDefinitionAttributes attributes = new TaskDefinitionAttributes();
        attributes.setTenants(null);
        instance.setDefinitionAttributes(attributes);

        Set<String> result = instance.getAllClients();
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAllClients_WhenDefinitionAttributesIsNull_ShouldReturnEmptySet() {
        TaskDefinitionInstance instance = new TaskDefinitionInstance();
        instance.setDefinitionAttributes(null);

        Set<String> result = instance.getAllClients();
        assertTrue(result.isEmpty());
    }

}
