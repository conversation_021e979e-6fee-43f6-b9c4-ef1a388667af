package com.phonepe.merchant.gladius.models.tasks.request.filter;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class LeadViewTaskFilterRequestTest {

    @Test
    void testBuilderInitialization() {
        EsLocationRequest location = EsLocationRequest.builder().lat(12.34).lon(56.78).build();
        LeadViewTaskFilterRequest request = LeadViewTaskFilterRequest.builder()
                .location(location)
                .build();

        assertNotNull(request);
        assertEquals(TaskSearchRequestType.LEAD_VIEW, request.getTaskSearchRequestType());
        assertEquals(location, request.getLocation());
    }

    @Test
    void testDefaultConstructor() {
        LeadViewTaskFilterRequest request = new LeadViewTaskFilterRequest();

        assertNotNull(request);
        assertEquals(TaskSearchRequestType.LEAD_VIEW, request.getTaskSearchRequestType());
        assertNull(request.getLocation());
    }

    @Test
    void testParameterizedConstructor() {
        EsLocationRequest location = EsLocationRequest.builder().lat(12.34).lon(56.78).build();
        LeadViewTaskFilterRequest request = new LeadViewTaskFilterRequest(location);

        assertNotNull(request);
        assertEquals(TaskSearchRequestType.LEAD_VIEW, request.getTaskSearchRequestType());
        assertEquals(location, request.getLocation());
    }

    @Test
    void testSetLocation() {
        LeadViewTaskFilterRequest request = new LeadViewTaskFilterRequest();
        EsLocationRequest location = EsLocationRequest.builder().lat(12.34).lon(56.78).build();

        request.setLocation(location);

        assertEquals(location, request.getLocation());
    }
}