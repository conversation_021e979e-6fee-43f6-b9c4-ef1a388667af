package com.phonepe.merchant.gladius.models.tasks.request;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TaskCompletionByTaskTypeRequestTest {

    private static Validator validator;

    @BeforeAll
    public static void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    public void testValidRequest() {
        TaskCompletionByTaskTypeRequest request = TaskCompletionByTaskTypeRequest.builder()
                .taskType("SS_DEPLOYMENTS") // Use an actual TaskType value
                .entityId("entity-123")
                .completedBy("user-987")
                .build();

        Set<ConstraintViolation<TaskCompletionByTaskTypeRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty(), "The request should be valid");
    }

    @Test
    public void testNullTaskType() {
        TaskCompletionByTaskTypeRequest request = TaskCompletionByTaskTypeRequest.builder()
                .entityId("entity-123")
                .completedBy("user-987")
                .build();

        Set<ConstraintViolation<TaskCompletionByTaskTypeRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), "The request should be invalid due to null taskType");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("taskType")));
    }

    @Test
    public void testBlankEntityId() {
        TaskCompletionByTaskTypeRequest request = TaskCompletionByTaskTypeRequest.builder()
                .taskType("UNKNOWN")
                .entityId("") // Invalid empty string
                .completedBy("user-987")
                .build();

        Set<ConstraintViolation<TaskCompletionByTaskTypeRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), "The request should be invalid due to blank entityId");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("entityId")));
    }

    @Test
    public void testBlankCompletedBy() {
        TaskCompletionByTaskTypeRequest request = TaskCompletionByTaskTypeRequest.builder()
                .taskType("EDC_DEPLOYMENTS")
                .entityId("entity-123")
                .completedBy("") // Invalid empty string
                .build();

        Set<ConstraintViolation<TaskCompletionByTaskTypeRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), "The request should be invalid due to blank completedBy");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("completedBy")));
    }

    @Test
    public void testOriginalRequest() {
        TaskCompletionByTaskTypeRequest request = TaskCompletionByTaskTypeRequest.builder()
                .taskType("EDC_DEPLOYMENTS")
                .entityId("entity-123")
                .completedBy("ww")
                .build();

        Set<ConstraintViolation<TaskCompletionByTaskTypeRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty());
        Assertions.assertNull(request.getDefinitionId());
        Assertions.assertEquals("EDC_DEPLOYMENTS", request.getType());
    }
}