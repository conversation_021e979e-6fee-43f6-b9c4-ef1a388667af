package com.phonepe.merchant.gladius.models.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskMetaTypeDeserializerTest {

    private final TaskMetaTypeDeserializer deserializer = new TaskMetaTypeDeserializer();

    @Test
    void testDeserializeValidEnumValue() throws IOException {
        JsonParser parser = mock(JsonParser.class);
        DeserializationContext context = mock(DeserializationContext.class);

        when(parser.getText()).thenReturn("DEVICE_ID");

        TaskMetaType result = deserializer.deserialize(parser, context);

        assertEquals(TaskMetaType.DEVICE_ID, result);
    }

    @Test
    void testDeserializeInvalidEnumValue() throws IOException {
        JsonParser parser = mock(JsonParser.class);
        DeserializationContext context = mock(DeserializationContext.class);

        when(parser.getText()).thenReturn("INVALID_VALUE");

        TaskMetaType result = deserializer.deserialize(parser, context);

        assertEquals(TaskMetaType.UNKNOWN, result);
    }

    @Test
    void testDeserializeNullValue() throws IOException {
        JsonParser parser = mock(JsonParser.class);
        DeserializationContext context = mock(DeserializationContext.class);

        when(parser.getCurrentToken()).thenReturn(JsonToken.VALUE_NULL);

        TaskMetaType result = deserializer.deserialize(parser, context);

        assertNull(result);
    }
}
