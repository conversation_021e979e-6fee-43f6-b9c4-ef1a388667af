package com.phonepe.merchant.gladius.models.tasks.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode
@Builder
@AllArgsConstructor
public class TaskMetaInformation {

    @NotNull
    private TaskMetaType type;

    @NotBlank
    @Getter(AccessLevel.NONE)
    private Object value;

    private String displayText;
    @NotNull
    private Boolean displayInformation;

    private void initialize(TaskMetaType type,
                            String value,
                            Boolean displayInformation) {
        this.type = type;
        this.displayText = type.getDisplayText();
        this.value = value;
        this.displayInformation = displayInformation;
        if(type.equals(TaskMetaType.WORKFLOW_ID))
            this.displayInformation = false;
    }

    @JsonCreator
    public TaskMetaInformation(@JsonProperty(value = "type", required = true) TaskMetaType type,
                               @JsonProperty(value = "value", required = true) String value,
                               @JsonProperty(value = "displayInformation", required = true) Boolean displayInformation) {
        initialize(type,value,displayInformation);
    }

    public void setType(TaskMetaType type) {
        this.type = type;
        this.displayText = type.getDisplayText();
    }

    public Object getValue() {
       switch(type.getAttributeType()) {
           case STRING:
               return value.toString(); // No conversion needed for strings
           case DOUBLE:
               try {
                   return Double.parseDouble(value.toString());
               } catch (NumberFormatException e) {
                   return null;
               }
           default:
               return null;
       }
    }

}


