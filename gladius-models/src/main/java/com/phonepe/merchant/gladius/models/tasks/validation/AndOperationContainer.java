package com.phonepe.merchant.gladius.models.tasks.validation;

import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
public class AndOperationContainer<T extends LocationCommandRequest> extends ValidationContainer<T> {

    @NotEmpty
    @Size(min = 2)
    protected List<ValidationContainer> validationContainers;

    @Override
    public void validate(EntityType entityType, Map<String, ActionValidator> validatorMap) {
        validationContainers.forEach(validationContainer -> validationContainer.validate(entityType, validatorMap));
    }

    @Override
    public boolean validate(T taskCommandRequest,
                            Map<String, ActionValidator> validatorMap,
                            Set<ValidatorResponse> errorContext) {
        for (ValidationContainer validationContainer : validationContainers) {
            if (!validationContainer.validate(taskCommandRequest, validatorMap, errorContext))
                return false;
        }
        return true;
    }

    protected AndOperationContainer() {
        super("AND");
    }

    public AndOperationContainer(List<ValidationContainer> validationContainers) {
        super("AND");
        this.validationContainers = validationContainers;
    }

}
