package com.phonepe.merchant.gladius.models.tasks.request.commands;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "command"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = TaskAssignRequest.class, name = "ASSIGN"),
        @JsonSubTypes.Type(value = TaskCreateRequest.class, name = "CREATE"),
        @JsonSubTypes.Type(value = TaskStartRequest.class, name = "START"),
        @JsonSubTypes.Type(value = TaskMarkAvailableRequest.class, name = "AVAILABLE"),
        @JsonSubTypes.Type(value = TaskCompleteRequest.class, name = "COMPLETE"),
        @JsonSubTypes.Type(value = TaskUpdateRequest.class, name = "UPDATE"),
        @JsonSubTypes.Type(value = UserTaskCreationRequest.class, name = "USER_TASK_CREATION")
})
@Data
public abstract class TaskCommandRequest {
    private AppCommand command;
    private String taskInstanceId;

    protected TaskCommandRequest(AppCommand command) {
        this.command = command;
    }

    protected TaskCommandRequest(AppCommand command, String taskInstanceId) {
        this.command = command;
        this.taskInstanceId = taskInstanceId;
    }

    public abstract <T> T accept(TaskCommandRequest.TaskCommandRequestVisitor<T> var1);

    public interface TaskCommandRequestVisitor<T> {
        T visit(TaskCreateRequest data);

        T visit(TaskAssignRequest data);

        T visit(TaskCompleteRequest data);

        T visit(TaskUpdateRequest data);

        T visit(TaskMarkAvailableRequest data);

        T visit(TaskStartRequest data);

        T visit(UserTaskCreationRequest data);
    }
}
