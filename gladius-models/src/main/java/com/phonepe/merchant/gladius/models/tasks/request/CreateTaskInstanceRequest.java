package com.phonepe.merchant.gladius.models.tasks.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateTaskInstanceRequest implements DefinitionIdIdentifier {

    @NonNull
    @NotEmpty
    private String taskDefinitionId;

    @NotBlank
    private String campaignId;

    private Integer points;

    private TaskInstanceMeta taskInstanceMeta;

    @NotNull
    private String createdBy;

    private String entityId;

    @Override
    public String getDefinitionId() {
        return taskDefinitionId;
    }
}
