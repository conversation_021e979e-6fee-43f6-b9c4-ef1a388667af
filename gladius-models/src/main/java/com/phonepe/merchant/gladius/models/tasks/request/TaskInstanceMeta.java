package com.phonepe.merchant.gladius.models.tasks.request;

import com.collections.CollectionUtils;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskInstanceMeta {

    private Integer retriesDone;

    private List<TaskMetaInformation> taskMetaList;

    private Map<TaskMetaType, Object> taskMetaAsMap;

    public Map<TaskMetaType, Object> getTaskMetaAsMap() {
        if(CollectionUtils.isNotEmpty(taskMetaList)) {
            return taskMetaList.stream().collect(
                    Collectors.toMap(TaskMetaInformation::getType, TaskMetaInformation::getValue));
        }
        return Collections.emptyMap();
    }


}
