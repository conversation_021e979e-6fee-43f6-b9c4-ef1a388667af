package com.phonepe.merchant.gladius.models.entitystore;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.request.Address;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class AgentEntity extends Entity {

    private String phoneNumber;

    private AgentType role;

    private boolean active;

    private String managerId;

    private String emailId;

    private List<Address> addresses;

    private Long lastActiveDate;

    protected AgentEntity(String agentId,List<String> polygonIds,String agentName) {
        super(EntityType.AGENT,agentId,null,polygonIds,agentName);
    }

    @Builder
    public AgentEntity(String agentId,
                       String agentName,
                       List<String> polygonIds,
                       String phoneNumber,
                       boolean active,
                       String managerId,
                       String emailId,
                       List<Address> addresses,
                       Long lastActiveDate,
                       AgentType role) {
        this(agentId, polygonIds, agentName);
        this.phoneNumber = phoneNumber;
        this.active = active;
        this.managerId = managerId;
        this.emailId = emailId;
        this.addresses = addresses;
        this.lastActiveDate = lastActiveDate;
        this.role = role;
    }

}
