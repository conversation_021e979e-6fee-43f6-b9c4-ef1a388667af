package com.phonepe.merchant.gladius.models.tasks.request.filter;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import lombok.Builder;
import lombok.Data;

@Data
public class LeadViewTaskFilterRequest extends BaseLocationTaskFilterRequest {

    @Builder
    public LeadViewTaskFilterRequest(EsLocationRequest location) {
        super(TaskSearchRequestType.LEAD_VIEW, location);
    }

    public LeadViewTaskFilterRequest() {
        super(TaskSearchRequestType.LEAD_VIEW);
    }
}
