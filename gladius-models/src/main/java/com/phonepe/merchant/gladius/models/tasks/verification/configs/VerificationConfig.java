package com.phonepe.merchant.gladius.models.tasks.verification.configs;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;


import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_FORM_FILL_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BUSINESS_CATEGORY_UPDATE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EVENT_EXISTENCE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.FL_DRIVE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MANAGER_SECTOR_TERRITORY_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MERCHANT_KYC_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MID_SID_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.NO_ACTION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.OQC_VALIDATION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.P2ML_MIGRATION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.PHONE_NUMBER_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POA_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POB_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SELF_ONBOARDING_MERCHANT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_DUE_COLLECTION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_TROUBLESHOOT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SOUNDBOX_DEPLOYMENT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_CHECK_IN_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_QC_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_INSTANCE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_QC_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_DEPLOYMENT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EXTERNAL_ENTITY_TO_PHONEPE_MXN_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_REVERSE_PICKUP_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MERCHANT_LENDING_COMPLETION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_REVERSE_PICKUP_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MULTI_EDC_DEPLOYMENT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.AGENT_DEBOARDING_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_TASK_CLOSURE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.VPA_VERIFIER;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "verifierName"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = StoreCheckInVerifierConfig.class, name = STORE_CHECK_IN_VERIFIER),
        @JsonSubTypes.Type(value = NoActionVerifierConfig.class, name = NO_ACTION_VERIFIER),
        @JsonSubTypes.Type(value = OqcValidationVerifierConfig.class, name = OQC_VALIDATION_VERIFIER),
        @JsonSubTypes.Type(value = TaskQcVerifierConfig.class, name = TASK_QC_VERIFIER),
        @JsonSubTypes.Type(value = MerchantKycVerifierConfig.class, name = MERCHANT_KYC_VERIFIER),
        @JsonSubTypes.Type(value = EventExistenceVerifierConfig.class, name = EVENT_EXISTENCE_VERIFIER),
        @JsonSubTypes.Type(value = StoreQcVerifierConfig.class, name = STORE_QC_VERIFIER),
        @JsonSubTypes.Type(value = BrickbatFormFillVerifierConfig.class, name = BRICKBAT_FORM_FILL_VERIFIER),
        @JsonSubTypes.Type(value = FlDriveVerifierConfig.class, name = FL_DRIVE_VERIFIER),
        @JsonSubTypes.Type(value = SoundboxDeploymentVerifierConfig.class, name = SOUNDBOX_DEPLOYMENT_VERIFIER),
        @JsonSubTypes.Type(value = P2MLmigrationVerifierConfig.class, name = P2ML_MIGRATION_VERIFIER),
        @JsonSubTypes.Type(value = PoaVerifierConfig.class, name = POA_VERIFIER),
        @JsonSubTypes.Type(value = PobVerifierConfig.class, name = POB_VERIFIER),
        @JsonSubTypes.Type(value = BusinessCategoryUpdateVerifierConfig.class, name = BUSINESS_CATEGORY_UPDATE_VERIFIER),
        @JsonSubTypes.Type(value = SmartspeakerTroubleshootVerifierConfig.class, name = SMARTSPEAKER_TROUBLESHOOT_VERIFIER),
        @JsonSubTypes.Type(value = EdcDeploymentVerifierConfig.class, name = EDC_DEPLOYMENT_VERIFIER),
        @JsonSubTypes.Type(value = EdcReversePickupVerifierConfig.class, name = EDC_REVERSE_PICKUP_VERIFIER),
        @JsonSubTypes.Type(value = EdcTaskClosureVerifierConfig.class, name = EDC_TASK_CLOSURE_VERIFIER),
        @JsonSubTypes.Type(value = MultiEdcDeploymentVerifierConfig.class, name = MULTI_EDC_DEPLOYMENT_VERIFIER),
        @JsonSubTypes.Type(value = SmartspeakerTroubleshootVerifierConfig.class, name = SMARTSPEAKER_TROUBLESHOOT_VERIFIER),
        @JsonSubTypes.Type(value = SmartspeakerDueCollectionVerifierConfig.class, name = SMARTSPEAKER_DUE_COLLECTION_VERIFIER),
        @JsonSubTypes.Type(value = OnboardExternalEntityAsPhonepeMxnVerifierConfig.class, name = EXTERNAL_ENTITY_TO_PHONEPE_MXN_VERIFIER),
        @JsonSubTypes.Type(value = SmartspeakerReversePickupVerifierConfig.class, name = SMARTSPEAKER_REVERSE_PICKUP_VERIFIER),
        @JsonSubTypes.Type(value = MerchantLendingCompletionVerifierConfig.class, name = MERCHANT_LENDING_COMPLETION_VERIFIER),
        @JsonSubTypes.Type(value = SelfOnboardingMerchantVerifierConfig.class, name = SELF_ONBOARDING_MERCHANT_VERIFIER),
        @JsonSubTypes.Type(value = AgentDeboardingVerifierConfig.class, name = AGENT_DEBOARDING_VERIFIER),
        @JsonSubTypes.Type(value = ManagerSectorTerritoryVerifierConfig.class, name = MANAGER_SECTOR_TERRITORY_VERIFIER),
        @JsonSubTypes.Type(value = PhoneNumberVerifierConfig.class, name = PHONE_NUMBER_VERIFIER),
        @JsonSubTypes.Type(value = MidSidVerifierConfig.class, name = MID_SID_VERIFIER),
        @JsonSubTypes.Type(value = VpaVerifierConfig.class, name = VPA_VERIFIER),
        @JsonSubTypes.Type(value = TaskInstanceVerifierConfig.class, name = TASK_INSTANCE_VERIFIER),
})
@Data
public abstract class VerificationConfig {

    protected String verifierName;

    protected VerificationConfig(String verifierName) {
        this.verifierName = verifierName;
    }

}
