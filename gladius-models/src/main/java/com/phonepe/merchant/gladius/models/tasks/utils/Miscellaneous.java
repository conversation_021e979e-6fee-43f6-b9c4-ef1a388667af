package com.phonepe.merchant.gladius.models.tasks.utils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties
public class Miscellaneous {

    private double maxGeoSortDistance;

    @NotNull
    private Integer rescheduleOffsetInDays;

    @NotNull
    private Integer maxPossibleRescheduleOffsetInDays;

    private double formSubmitLocationRadiusInMeters;
}
