package com.phonepe.merchant.gladius.models.tasks.storage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.dropwizard.sharding.sharding.LookupKey;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "task_instances", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"task_instance_id"})
      }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Audited
public class StoredTaskInstance implements Serializable {

  @Id
  @Column(name = "id", unique = true)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Setter(AccessLevel.NONE)
  private long id;

  @Column(name = "entity_id", nullable = false)
  private String entityId;

  // instance specific meta , will use some kind of class to denote this
  // this are non-searchable and only custom notes, images or things like that
  @Column(name = "instance_meta", length = 3000)
  private byte[] instanceMeta;

  @LookupKey
  @Column(name = "task_instance_id", nullable = false, unique = true, updatable = false)
  private String taskInstanceId;

  @Column(nullable = false, name = "task_definition_id")
  private String taskDefinitionId;

  @Column(name = "entity_type", updatable = false, nullable = false)
  @Enumerated(EnumType.STRING)
  private EntityType entityType;

  @Column(name = "namespace", nullable = false)
  @Enumerated(EnumType.STRING)
  private Namespace namespace;

  @Column(name = "action_id", nullable = false)
  private String actionId;

  @NotAudited
  @OneToMany(fetch = FetchType.EAGER, mappedBy = "taskInstance")
  @Fetch(FetchMode.SUBSELECT)
  private List<StoredTaskTransition> taskTransitions;

  @Column(name = "cur_state", nullable = false)
  @Enumerated(EnumType.STRING)
  private LegionTaskStateMachineState curState;

  @Column(name = "created_at", updatable = false)
  @CreationTimestamp
  @Temporal(TemporalType.TIMESTAMP)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss z", timezone = "IST")
  private Date createdAt;

  @Column(name = "rescheduled_at")
  @Temporal(TemporalType.TIMESTAMP)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss z", timezone = "IST")
  private Date rescheduledAt;

  @Column(name = "due_date")
  @Temporal(TemporalType.TIMESTAMP)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss z", timezone = "IST")
  private Date dueDate;

  @Column(name = "updated_at")
  @UpdateTimestamp
  @Temporal(TemporalType.TIMESTAMP)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss z", timezone = "IST")
  private Date updatedAt;

  @Column(name = "secondary_index_sync_required")
  private boolean secondaryIndexSyncRequired;

  @Column(name = "partition_id")
  private int partitionId;

  @Column(name = "active", nullable = false)
  private boolean active;

  @Column(name = "points", nullable = false)
  private int points;

  @Column(name = "cur_actor")
  private String curActor;

  @Column(name = "campaign_id")
  private String campaignId;

  @Column(name = "created_by", nullable = false, updatable = false)
  private String createdBy;

  @Column(name = "updated_by")
  private String updatedBy;

  @Builder
  public StoredTaskInstance(String entityId, byte[] instanceMeta,
                            String taskInstanceId, String taskDefinitionId, EntityType entityType, String actionId,
                            boolean secondaryIndexSyncRequired, int partitionId, Namespace namespace, LegionTaskStateMachineState curState,
                            boolean active, int points, String curActor, String campaignId, Date rescheduledAt, Date dueDate,
                            String createdBy, String updatedBy) {
    this.entityId = entityId;
    this.instanceMeta = instanceMeta;
    this.taskInstanceId = taskInstanceId;
    this.taskDefinitionId = taskDefinitionId;
    this.entityType = entityType;
    this.actionId = actionId;
    this.secondaryIndexSyncRequired = secondaryIndexSyncRequired;
    this.partitionId = partitionId;
    this.namespace = namespace;
    this.curState = curState;
    this.active = active;
    this.points = points;
    this.curActor = curActor;
    this.campaignId = campaignId;
    this.rescheduledAt = rescheduledAt;
    this.dueDate = dueDate;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
  }

  public StoredTaskTransition lastTransition() {
    return this.getTaskTransitions().stream()
            .max(TaskUtils::taskComparator)
            .orElseThrow(() -> new RuntimeException("invalid task"));
  }
}
