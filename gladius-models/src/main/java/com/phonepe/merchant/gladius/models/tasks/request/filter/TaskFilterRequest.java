package com.phonepe.merchant.gladius.models.tasks.request.filter;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "taskSearchRequestType"
)@JsonSubTypes({
        @JsonSubTypes.Type(value = AssignedLocationTaskFilterRequest.class, name = "ASSIGNED_VIEW"),
        @JsonSubTypes.Type(value = DiscoveryLocationTaskFilterRequest.class, name = "DISCOVERY_VIEW"),
        @JsonSubTypes.Type(value = AssignedSectorTaskFilterRequest.class, name = "SECTOR_ASSIGNED_VIEW"),
        @JsonSubTypes.Type(value = DiscoverySectorTaskFilterRequest.class, name = "SECTOR_DISCOVERY_VIEW"),
        @JsonSubTypes.Type(value = AssignedHotspotTaskFilterRequest.class, name = "HOTSPOT_ASSIGNED_VIEW"),
        @JsonSubTypes.Type(value = DiscoveryHotspotTaskFilterRequest.class, name = "HOTSPOT_DISCOVERY_VIEW"),
        @JsonSubTypes.Type(value = EscalationViewTaskFilterRequest.class, name = "ESCALATED_VIEW"),
        @JsonSubTypes.Type(value = LeadViewTaskFilterRequest.class, name = "LEAD_VIEW"),
})
@Data
@NoArgsConstructor
public abstract class TaskFilterRequest {

    @NotNull
    @Valid
    protected TaskSearchRequestType taskSearchRequestType;

    protected TaskFilterRequest(TaskSearchRequestType taskSearchRequestType) {
        this.taskSearchRequestType = taskSearchRequestType;
    }

}
