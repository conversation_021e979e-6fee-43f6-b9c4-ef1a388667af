package com.phonepe.merchant.gladius.models.tasks.request;


import com.phonepe.models.merchants.tasks.EntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskCompletionByTaskTypeRequest implements DefinitionIdIdentifier {

    @NotNull
    private String taskType;

    @NotBlank
    private String entityId;

    @NotBlank
    private String completedBy;

    @Builder.Default
    private EntityType entityType = EntityType.STORE;

    @Override
    public String getDefinitionId() {
        return null;
    }

    @Override
    public String getType() {
        return taskType;
    }
}
