package com.phonepe.merchant.gladius.models.entitystore;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Data;

import java.util.List;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = StoreEntity.class, name = "STORE"),
        @JsonSubTypes.Type(value = MerchantEntity.class, name = "MERCHANT"),
        @JsonSubTypes.Type(value = SectorEntity.class, name = "SECTOR"),
        @JsonSubTypes.Type(value = TaskEntity.class, name = "TASK"),
        @JsonSubTypes.Type(value = TaskEntity.class, name = "EXTERNAL"),
        @JsonSubTypes.Type(value = PhoneNumberEntity.class, name = "PHONE_NUMBER"),
})
@Data
public abstract class Entity {

    protected EntityType type;

    protected String entityId;

    protected EsLocationRequest location;

    protected List<String> polygonIds;

    protected String name;

    protected Entity(EntityType type,
                     String entityId,
                     EsLocationRequest location,
                     List<String> polygonIds,
                     String name) {
        this.type = type;
        this.entityId = entityId;
        this.location = location;
        this.polygonIds = polygonIds;
        this.name = name;
    }

    protected Entity() {
    }
}
