package com.phonepe.merchant.gladius.models.tasks.request.search;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "taskSearchRequestType"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = AssignedViewTaskFetchRequest.class, name = "ASSIGNED_VIEW"),
        @JsonSubTypes.Type(value = DiscoveryViewTaskSearchRequest.class, name = "DISCOVERY_VIEW"),
        @JsonSubTypes.Type(value = SectorAssignedViewTaskSearchRequest.class, name = "SECTOR_ASSIGNED_VIEW"),
        @JsonSubTypes.Type(value = SectorDiscoveryViewTaskSearchRequest.class, name = "SECTOR_DISCOVERY_VIEW"),
        @JsonSubTypes.Type(value = EscalatedViewTaskSearchRequest.class, name = "ESCALATED_VIEW"),
        @JsonSubTypes.Type(value = HotspotDiscoveryViewTaskSearchRequest.class, name = "HOTSPOT_DISCOVERY_VIEW"),
        @JsonSubTypes.Type(value = HotspotAssignedViewTaskSearchRequest.class, name = "HOTSPOT_ASSIGNED_VIEW"),
        @JsonSubTypes.Type(value = LeadViewTaskSearchRequest.class, name = "LEAD_VIEW"),
})
@Data
@NoArgsConstructor
@SuperBuilder
public abstract class TaskSearchRequest {
    @Min(1)
    protected int pageNo;

    @Max(15L)
    @Min(1)
    protected int pageSize;

    @NotNull
    protected TaskSearchRequestType taskSearchRequestType;

    @NotNull
    @Valid
    protected EsLocationRequest location;

    protected List<Filter> filters;

    protected Sorter sorter;

    protected TaskSearchRequest(TaskSearchRequestType taskSearchRequestType) {
        this.taskSearchRequestType = taskSearchRequestType;
        this.filters = new ArrayList<>();
        this.sorter = Sorter.NONE;
    }
}
