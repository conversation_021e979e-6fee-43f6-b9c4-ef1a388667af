package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LatestTaskInfo {
    private String taskInstanceId;
    private String currentAgentName;
    private AgentType currentAgentRole;
    private String currentAgentId;
    private String leadIntent;
    private String remarks;
    private Date rescheduledAt;
    private boolean active;
}
