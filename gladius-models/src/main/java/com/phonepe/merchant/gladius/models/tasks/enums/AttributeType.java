package com.phonepe.merchant.gladius.models.tasks.enums;

import lombok.Getter;

@Getter
public enum AttributeType {

    OBJECTIVE("objective", true, false),

    ROLES_NOT_ALLOWED("roles_not_allowed", false, false),

    KYC_REQUIRED_AND_COMPLETED("kyc_required_and_completed", false, true);

    private final String name;
    private final boolean isDisplayAllowed;
    private final boolean isFeatureFlagBased;

    AttributeType(String name, boolean isDisplayAllowed, boolean isFeatureBased) {
        this.name = name;
        this.isDisplayAllowed = isDisplayAllowed;
        this.isFeatureFlagBased = isFeatureBased;
    }
}