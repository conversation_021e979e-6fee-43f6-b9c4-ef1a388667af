package com.phonepe.merchant.gladius.models.tasks.entitystore;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.models.merchants.BusinessUnit;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor
public class DiscoveryTaskInstance {

    @JsonProperty(DiscoveryTaskInstanceFieldNames.ENTITY_ID)
    private String entityId;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.ENTITY_TYPE)
    private EntityType entityType;

    @NotNull
    @NotEmpty
    @JsonProperty(DiscoveryTaskInstanceFieldNames.TASK_INSTANCE_ID)
    private String taskInstanceId;

    @NotNull
    @NotEmpty
    @JsonProperty(DiscoveryTaskInstanceFieldNames.TASK_DEFINITION_ID)
    private String taskDefinitionId;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.NAMESPACE)
    private Namespace namespace;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.TASK_STATE)
    private LegionTaskStateMachineState taskState;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.ASSIGNED_TO)
    private String assignedTo;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.COMPLETED_BY)
    private String completedBy;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.CREATED_AT)
    private Long createdAt;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.UPDATED_AT)
    private Long updatedAt;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.DUE_DATE)
    private Long dueDate;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.START_DATE)
    private Long startDate;

    @NotNull
    @NotEmpty
    @JsonProperty(DiscoveryTaskInstanceFieldNames.ACTION_ID)
    private String actionId;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.POINTS)
    private Long points;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.LOCATION)
    private EsLocationRequest location;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.BUSINESS_UNIT)
    private BusinessUnit businessUnit;

    @NotNull
    @JsonProperty(DiscoveryTaskInstanceFieldNames.ACTIVE)
    private Boolean active;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.VERIFIED_ON)
    private Long verifiedOn;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.COMPLETED_ON)
    private Long completedOn;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.POLYGON_IDS)
    // To be deprecated.
    private List<String> polygonIds;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.DISTANCE)
    private Double distance;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.CAMPAIGN)
    private String campaign;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.TAGS)
    private Set<String> tags;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.RESCHEDULED_AT)
    private long rescheduledAt;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.ATTRIBUTES)
    private Map<String, Set<String>> attributes;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.TASK_METADATA)
    private TaskInstanceMeta taskInstanceMeta;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.CREATED_BY)
    private String createdBy;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.UPDATED_BY)
    private String updatedBy;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.GEOHASH_8)
    private String geohash8;

    @JsonProperty(DiscoveryTaskInstanceFieldNames.S2_CELL)
    private String s2cell;

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class DiscoveryTaskInstanceFieldNames {
        public static final String ENTITY_ID = "entity_id";
        public static final String ENTITY_TYPE = "entity_type";
        public static final String TASK_INSTANCE_ID = "task_instance_id";
        public static final String TASK_DEFINITION_ID = "task_definition_id";
        public static final String NAMESPACE = "namespace";
        public static final String TASK_STATE = "task_state";
        public static final String ASSIGNED_TO = "assigned_to";
        public static final String CREATED_AT = "created_at";
        public static final String UPDATED_AT = "updated_at";
        public static final String POINTS = "points";
        public static final String LOCATION = "location";
        public static final String DUE_DATE = "due_date";
        public static final String ACTION_ID = "action_id";
        public static final String BUSINESS_UNIT = "business_unit";
        public static final String VERIFIED_ON = "verified_on";
        public static final String COMPLETED_ON = "completed_on";
        public static final String ACTIVE = "active";
        public static final String COMPLETED_BY = "completed_by";
        public static final String START_DATE = "start_date";
        public static final String POLYGON_IDS = "polygon_ids";
        public static final String DISTANCE = "distance";
        public static final String CAMPAIGN = "campaign";
        public static final String TAGS = "tags";
        public static final String RESCHEDULED_AT = "rescheduled_at";
        public static final String TASK_METADATA = "task_metadata";
        public static final String ATTRIBUTES = "attributes";
        public static final String ROLES_NOT_ALLOWED_STRING = "roles_not_allowed";
        public static final String ROLES_NOT_ALLOWED_FIELD = "attributes.roles_not_allowed";
        public static final String ROLES_NOT_ALLOWED_FIELD_KEYWORD = "attributes.roles_not_allowed.keyword";
        public static final String CREATED_BY = "created_by";
        public static final String UPDATED_BY = "updated_by";
        public static final String GEOHASH_8 = "geoHash_8";
        public static final String S2_CELL = "s2_cell";

    }

    public String toString() {
        return this.getTaskInstanceId();
    }
}
