package com.phonepe.merchant.gladius.models.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;

import java.io.IOException;

public class TaskMetaTypeDeserializer extends JsonDeserializer<TaskMetaType> {
    @Override
    public TaskMetaType deserialize(JsonParser p, DeserializationContext deserializationContext) throws IOException {
        if (p.getCurrentToken() == JsonToken.VALUE_NULL) {
            return null;
        }

        String value = p.getText();

        // 2. Try to find a matching enum value
        for (TaskMetaType enumValue : TaskMetaType.values()) {
            if (enumValue.name().equalsIgnoreCase(value)) {
                return enumValue;
            }
        }

        return TaskMetaType.UNKNOWN;
    }
}
