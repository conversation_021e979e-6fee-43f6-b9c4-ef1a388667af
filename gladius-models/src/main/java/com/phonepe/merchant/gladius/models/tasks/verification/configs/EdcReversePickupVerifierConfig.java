package com.phonepe.merchant.gladius.models.tasks.verification.configs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_REVERSE_PICKUP_VERIFIER;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
public class EdcReversePickupVerifierConfig extends VerificationConfig{

    public EdcReversePickupVerifierConfig() {
        super(EDC_REVERSE_PICKUP_VERIFIER);
    }

}
