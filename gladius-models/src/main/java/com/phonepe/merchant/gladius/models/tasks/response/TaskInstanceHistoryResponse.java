package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class TaskInstanceHistoryResponse {
    private String currentAgentName;
    private AgentType currentAgentRole;
    private String currentAgentId;
    private String leadIntent;
    private String remarks;
    private Date rescheduledAt;
    private boolean active;
    private int historyCount;
    private List<GroupedResponse<TaskInstanceAuditResponse>> history;
}
