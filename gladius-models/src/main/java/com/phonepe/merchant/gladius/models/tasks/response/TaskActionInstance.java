package com.phonepe.merchant.gladius.models.tasks.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationStrategy;
import com.phonepe.merchant.gladius.models.tasks.verification.VerificationStrategy;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskActionInstance {

    @NotBlank
    @Pattern(regexp = "^([A-Z][A-Z0-9]*([_][A-Z0-9]+)*)$", message = "can only start with capital alphabets, and can only consist of captial alphanumeric values seperated by _")
    private String actionId;

    @NotNull
    private EntityType entityType;

    @NotNull
    private Namespace namespace;

    @NotNull
    private String createdBy;

    @NotNull
    private String updatedBy;

    @Valid
    private VerificationStrategy verificationStrategy;

    @Valid
    @JsonProperty("validationStrategy")
    private ValidationStrategy completionValidationStrategy;

    @Valid
    private ValidationStrategy startTaskValidationStrategy;

    @Valid
    private ValidationStrategy assignTaskValidationStrategy;

    @NotNull
    private String description;

    @NotNull
    List<Object> subSteps;

    private Map<String, Set<String>> attributes;
}
