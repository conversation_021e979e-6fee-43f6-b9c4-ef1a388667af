package com.phonepe.merchant.gladius.models.tasks.validation;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = LeafContainer.class, name = "LEAF"),
        @JsonSubTypes.Type(value = AndOperationContainer.class, name = "AND"),
        @JsonSubTypes.Type(value = OrOperationContainer.class, name = "OR")
})
@Data
public abstract class ValidationContainer<T extends LocationCommandRequest> {

    private String type;

    public abstract boolean validate(T taskCommandRequest,
                                     Map<String, ActionValidator> validatorMap,
                                     Set<ValidatorResponse> errorContext);

    public abstract void validate(EntityType entityType, Map<String, ActionValidator> validatorMap);

    protected ValidationContainer(String type) {
        this.type = type;
    }

}
