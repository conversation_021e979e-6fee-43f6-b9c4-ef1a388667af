package com.phonepe.merchant.gladius.models.entitystore;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "entityType",
        visible = true
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = AgentEntityMeta.class, name = "AGENT"),
        @JsonSubTypes.Type(value = ExternalEntityMeta.class, name = "EXTERNAL"),
        @JsonSubTypes.Type(value = MerchantEntityMeta.class, name = "MERCHANT"),
        @JsonSubTypes.Type(value = SectorEntityMeta.class, name = "SECTOR"),
        @JsonSubTypes.Type(value = StoreEntityMeta.class, name = "STORE"),
        @JsonSubTypes.Type(value = TaskEntityMeta.class, name = "TASK"),
        @JsonSubTypes.Type(value = VpaEntityMeta.class, name = "VPA"),
        @JsonSubTypes.Type(value = PhoneNumberEntityMeta.class, name = "PHONE_NUMBER")
})
@Data
@SuperBuilder
@NoArgsConstructor
public abstract class EntityMeta {

    protected EntityType entityType;
    protected String entityId;
    protected String name;
    protected String genre;
    protected SourceMeta sourceMeta;

    protected EntityMeta(Entity entity) {
        this.entityType = entity.getType();
        this.entityId = entity.getEntityId();
        this.name = entity.getName();
    }

    protected EntityMeta(EntityMeta entity) {
        this.entityType = entity.getEntityType();
        this.entityId = entity.getEntityId();
        this.name = entity.getName();
        this.genre = entity.getGenre();
        this.sourceMeta = entity.getSourceMeta();
    }


}
