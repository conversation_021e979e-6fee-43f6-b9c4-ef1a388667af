package com.phonepe.merchant.gladius.models.entitystore;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
public class PhoneNumberEntity extends Entity {

    String phoneNumber;

    String storeName;

    @Builder
    public PhoneNumberEntity(String phoneNumber, String name) {
        super(EntityType.PHONE_NUMBER, phoneNumber, null, null, name);
        this.phoneNumber  = phoneNumber;
        this.storeName = name;
    }
}
