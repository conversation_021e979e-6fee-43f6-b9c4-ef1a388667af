package com.phonepe.merchant.gladius.models.tasks.validation;

import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ValidatorConfig;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.Set;

@Data
public class LeafContainer<T extends LocationCommandRequest> extends ValidationContainer<T> {

    @NotNull
    private ValidatorConfig validatorConfig;

    protected LeafContainer() {
        super("LEAF");
    }

    public LeafContainer(ValidatorConfig validatorConfig) {
        super("LEAF");
        this.validatorConfig = validatorConfig;
    }

    @Override
    public boolean validate(T taskCommandRequest,
                            Map<String, ActionValidator> validatorMap,
                            Set<ValidatorResponse> errorContext) {
        ValidatorResponse validatorResponse = validatorMap.get(validatorConfig.getValidatorName())
                .validate(taskCommandRequest, validatorConfig);
        if (!validatorResponse.isValidated()) {
            errorContext.add(validatorResponse);
        }
        return validatorResponse.isValidated();
    }

    @Override
    public void validate(EntityType entityType, Map<String, ActionValidator> validatorMap) {
        validatorMap.get(validatorConfig.getValidatorName()).validate(entityType, validatorConfig);
    }
}
