package com.phonepe.merchant.gladius.models.tasks.request.commands;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.DefinitionIdIdentifier;
import lombok.Builder;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
public class TaskCreateRequest extends TaskCommandRequest implements DefinitionIdIdentifier {

    @Valid
    @NotNull
    private CreateTaskInstanceRequest taskInstance;

    private boolean markAvailable;

    public TaskCreateRequest() {
        super(AppCommand.CREATE);
    }

    @Builder
    public TaskCreateRequest(CreateTaskInstanceRequest taskInstance, boolean markAvailable) {
        this();
        this.taskInstance = taskInstance;
        this.markAvailable = markAvailable;
    }

    @Override
    public <T> T accept(TaskCommandRequestVisitor<T> var1) {
        return var1.visit(this);
    }

    @Override
    public String getDefinitionId() {
        return taskInstance.getTaskDefinitionId();
    }
}
