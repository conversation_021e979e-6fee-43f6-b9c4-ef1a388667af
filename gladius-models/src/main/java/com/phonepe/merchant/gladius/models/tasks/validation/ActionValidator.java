package com.phonepe.merchant.gladius.models.tasks.validation;

import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ValidatorConfig;
import com.phonepe.models.merchants.tasks.EntityType;

public interface ActionValidator<T extends LocationCommandRequest> {

    void validate(EntityType entityType, ValidatorConfig validatorConfig);

    ValidatorResponse validate(T taskCommandRequest, ValidatorConfig validatorConfig);

}
