package com.phonepe.merchant.gladius.models.tasks.request;

import com.phonepe.merchant.gladius.models.tasks.enums.LeadIntent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntentWithRemarks {

    private LeadIntent intent;

    private String displayText;

    private Boolean displayInformation;

    private List<String> remarks;

    private Boolean visibleOnCreate;

    private Boolean visibleOnUpdate;

    private Boolean allowMarkTaskComplete;

    private String textColor;

    private String bgColor;

    private Boolean showRemarks;

    private Boolean showRescheduleDate;

    private Boolean showMerchantFormFeature;

    private Boolean enableLeadSubmitWithoutMerchantForm;

    private String merchantFormTypeValue;

    private Boolean showMerchantFormFeatureV2;

}
