package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskListResponse {

    @Min(0L)
    private int pageNo;

    @Min(0L)
    private int pageSize;

    private long count;

    private List<DiscoveryTaskInstance> tasks;
}
