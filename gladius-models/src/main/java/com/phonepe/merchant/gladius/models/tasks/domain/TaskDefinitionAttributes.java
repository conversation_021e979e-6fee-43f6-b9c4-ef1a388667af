package com.phonepe.merchant.gladius.models.tasks.domain;

import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.TaskType;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDefinitionAttributes {

    private Category category;
    private TaskDefinitionTenants tenants;
    private LeadConfig leadConfig;
    private TaskType taskType;
}
