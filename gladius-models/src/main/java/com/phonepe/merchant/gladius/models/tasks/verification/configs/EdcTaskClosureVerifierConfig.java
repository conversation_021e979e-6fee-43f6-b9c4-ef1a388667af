package com.phonepe.merchant.gladius.models.tasks.verification.configs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_TASK_CLOSURE_VERIFIER;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
public class EdcTaskClosureVerifierConfig extends VerificationConfig{

    public EdcTaskClosureVerifierConfig() {
        super(EDC_TASK_CLOSURE_VERIFIER);
    }

}
