package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EntityTaskHistoryResponse {
    private String entityId;
    private long historyCount;
    private String currentTaskInstanceId;
    private String currentAgentName;
    private AgentType currentAgentRole;
    private String currentAgentId;
    private String leadIntent;
    private String remarks;
    private Date rescheduledAt;
    private boolean active;
    private List<GroupedResponse<TaskInstanceAuditResponse>> history;
}



