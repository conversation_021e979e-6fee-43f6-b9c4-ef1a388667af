package com.phonepe.merchant.gladius.models.tasks.request;

import com.phonepe.models.merchants.tasks.EntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientTaskCreateAndAssignRequest implements DefinitionIdIdentifier {

    @NotNull
    @NotEmpty
    private String entityId;

    @NotBlank
    private String taskDefinitionId;

    @NotBlank
    private String campaignId;

    private String assigneeId;

    private boolean createTaskForManager;

    private boolean markAvailable;

    private String createdBy;

    private TaskInstanceMeta taskInstanceMeta;

    @Builder.Default
    private EntityType entityType = EntityType.STORE;

    @Builder.Default
    private boolean forceTaskCreationRequest = false;

    private String reasonForDeletion;

    @Override
    public String getDefinitionId() {
        return taskDefinitionId;
    }
}
