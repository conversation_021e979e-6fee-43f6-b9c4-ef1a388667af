package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.gladius.models.entitystore.EntityMeta;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.ListingRequestMeta;
import com.phonepe.merchants.odin.models.agent.AgentMeta;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TaskMetaResponse {

    String taskInstanceId;
    LegionTaskStateMachineState taskStatus;
    Long createdAt;
    Long updatedAt;
    Long dueDate;
    Long completedOn;
    Long points;
    String entityId;
    EntityMeta entityMeta;
    List<TaskMetaAttribute> attributes;
    List<TaskMetaAttribute> attributeMeta;
    List<TaskMetaAttribute> taskMeta;
    AgentMeta agentMeta;
    EsLocationRequest location;
    String description;
    String leadIntent;
    EntityType entityType;
    Long maxAllowedDateForIntent;
    Category category;
    boolean allowLeadIntentUpdate;
    private String taskDefinitionId;
    /****
     * @deprecated use
     */
    @Deprecated
    @SuppressWarnings("java:S6355")
    List<Object> steps;
    ListingRequestMeta listingRequestMeta;
    Map<String, Object> taskMetaMap;
    List<String> badges;
    long resheduledAt;
    String taskType;

    public void addAttribute(String displayText, String icon, String unit, Object value) {
        if (attributes == null)
            attributes = new ArrayList<>();

        attributes.add(TaskMetaAttribute.builder()
                .displayText(displayText)
                .icon(icon)
                .unit(unit)
                .value(value)
                .build());
    }

    public void addEscalationViewAttribute(String label, Object value) {
        if (attributeMeta == null)
            attributeMeta = new ArrayList<>();

        attributeMeta.add(TaskMetaAttribute.builder()
                .label(label)
                .value(value)
                .build());
    }

    public void addTaskMetaToDisplayList(TaskMetaInformation taskMetaInformation) {
        if (taskMeta == null)
            taskMeta = new ArrayList<>();
        taskMeta.add(TaskMetaAttribute.builder()
                .value(taskMetaInformation.getValue())
                .displayText(taskMetaInformation.getDisplayText())
                .key(taskMetaInformation.getType().toString())
                .build());
    }

    public void addTaskMetaToMap(TaskMetaInformation taskMetaInformation) {
        if(taskMetaMap == null)
            taskMetaMap = new HashMap<>();
        taskMetaMap.put(taskMetaInformation.getType().toString(), taskMetaInformation.getValue());
    }

}
