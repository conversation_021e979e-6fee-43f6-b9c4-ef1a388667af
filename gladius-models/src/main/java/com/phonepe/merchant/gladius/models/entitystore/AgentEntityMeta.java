package com.phonepe.merchant.gladius.models.entitystore;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AgentEntityMeta extends EntityMeta {

    private String phoneNumber;

    private String emailId;

    private AgentType role;

    public AgentEntityMeta(AgentEntity entity) {
        super(entity);
        this.emailId = entity.getEmailId();
        this.phoneNumber = entity.getPhoneNumber();
        this.role = entity.getRole();
    }

    public AgentEntityMeta(AgentEntityMeta entityMeta) {
        super(entityMeta);
        this.emailId = entityMeta.getEmailId();
        this.phoneNumber = entityMeta.getPhoneNumber();
        this.role = entityMeta.getRole();
    }

}
