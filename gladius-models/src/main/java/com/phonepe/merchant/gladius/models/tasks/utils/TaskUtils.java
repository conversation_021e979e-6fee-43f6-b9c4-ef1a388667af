package com.phonepe.merchant.gladius.models.tasks.utils;

import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredActionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredDefinitionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


public class TaskUtils {

    private TaskUtils() {

    }

    public static int taskComparator(StoredTaskTransition o1, StoredTaskTransition o2) {
        return Integer.compare(o1.getTransitionId(), o2.getTransitionId());
    }

    public static long getDueDate(ExpiryPeriod expiryPeriod, long expiryValue) {
        return expiryPeriod.accept(new ExpiryPeriodProvider(expiryValue));
    }

    public static Double setPrecision(Double value, int precision ) {
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(precision, RoundingMode.HALF_UP); // Set rounding mode as needed
        return bd.doubleValue();
    }

    public static List<StoredDefinitionAttributeMappings> getAttributes(TaskDefinitionInstance taskDefinitionInstance,
                                                                        StoredTaskDefinition definition) {
        return taskDefinitionInstance.getAttributes().values().stream().map(values -> (
                values.stream().map(attributeValue -> (
                        StoredDefinitionAttributeMappings.builder()
                                .attributeValue(attributeValue)
                                .taskDefinition(definition)
                                .active(true)
                                .createdBy(taskDefinitionInstance.getCreatedBy())
                                .updatedBy(taskDefinitionInstance.getUpdatedBy())
                                .build()
                )).collect(Collectors.toList()))
        ).flatMap(List::stream).collect(Collectors.toList());
    }

    public static List<StoredActionAttributeMappings> getAttributes(TaskActionInstance taskActionInstance,
                                                                    StoredTaskAction taskAction) {
        return taskActionInstance.getAttributes().values().stream().map(values -> (
                values.stream().map(attributeValue -> (
                        StoredActionAttributeMappings.builder()
                                .attributeValue(attributeValue)
                                .taskAction(taskAction)
                                .active(true)
                                .createdBy(taskActionInstance.getCreatedBy())
                                .updatedBy(taskActionInstance.getUpdatedBy())
                                .build()
                )).collect(Collectors.toList()))
        ).flatMap(List::stream).collect(Collectors.toList());
    }

    public static List<StoredDefinitionAttributeMappings> mergeAttributes(List<StoredDefinitionAttributeMappings> original,
                                                                          List<StoredDefinitionAttributeMappings> updated) {
        Set<String> updatedAttributes = new HashSet<>(updated.stream()
                .map(StoredDefinitionAttributeMappings::getAttributeValue).toList());
        Set<String> originalAttributes = new HashSet<>(original.stream()
                .map(StoredDefinitionAttributeMappings::getAttributeValue).toList());

        List<StoredDefinitionAttributeMappings> merged = original.stream().map(attribute -> {
            attribute.setActive(updatedAttributes.contains(attribute.getAttributeValue()));
            return attribute;
        }).collect(Collectors.toList());

        merged.addAll(updated.stream().map(attribute -> originalAttributes.contains(attribute.getAttributeValue()) ? null : attribute)
                .filter(Objects::nonNull).toList());

        return merged;
    }

    public static List<StoredActionAttributeMappings> mergeActionAttributes(List<StoredActionAttributeMappings> original,
                                                                          List<StoredActionAttributeMappings> updated) {
        Set<String> updatedAttributes = new HashSet<>(updated.stream()
                .map(StoredActionAttributeMappings::getAttributeValue).toList());
        Set<String> originalAttributes = new HashSet<>(original.stream()
                .map(StoredActionAttributeMappings::getAttributeValue).toList());

        List<StoredActionAttributeMappings> merged = original.stream().map(attribute -> {
            attribute.setActive(updatedAttributes.contains(attribute.getAttributeValue()));
            return attribute;
        }).collect(Collectors.toList());

        merged.addAll(updated.stream().map(attribute -> originalAttributes.contains(attribute.getAttributeValue()) ? null : attribute)
                .filter(Objects::nonNull).toList());

        return merged;
    }

    public static ParsedStringTerms getParsedStringTermsFromEsSearchResponse(SearchResponse searchResponse, String key) {
        return (ParsedStringTerms) searchResponse.getAggregations().getAsMap().get(key);
    }

    public static ParsedLongTerms getParsedLongTermsFromEsSearchResponse(SearchResponse searchResponse, String key) {
        return (ParsedLongTerms) searchResponse.getAggregations().getAsMap().get(key);
    }

    public static String toTitleCase(String input) {
        if (input == null || input.isBlank()) {
            return input;
        }

        return Arrays.stream(input.split("[_\\s]+"))
                .filter(word -> !word.isBlank())
                .map(word -> word.substring(0, 1).toUpperCase() +
                        word.substring(1).toLowerCase())
                .collect(Collectors.joining(" "));
    }
}
