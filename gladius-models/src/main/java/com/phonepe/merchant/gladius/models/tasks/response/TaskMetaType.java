package com.phonepe.merchant.gladius.models.tasks.response;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.validation.constraints.NotBlank;

@AllArgsConstructor
public enum TaskMetaType {

    DEVICE_ID("Device ID", false, AttributeType.STRING),
    WORKFLOW_ID("Workflow ID", false, AttributeType.STRING),
    TRANSACTION_AMOUNT("Transaction Amount", false, AttributeType.STRING),
    AMOUNT_DUE("Amount due", false, AttributeType.STRING),
    MHD_TICKET_ID("MHD Ticket Id", false, AttributeType.STRING),
    LEAD_INTENT("Lead Intent", true, AttributeType.STRING),
    REMARK("Remark", true, AttributeType.STRING),
    LAST_MONTH_TPAM("Last month tpam", false, AttributeType.DOUBLE),
    LAST_MONTH_TPV("Last month tpv", false, AttributeType.DOUBLE),
    LAST_MONTH_INDUSTRY_POTENTIAL_TPV("Last month tpv", false, AttributeType.DOUBLE),
    SIX_MONTH_HIGHEST_TPV("6 month highest tpv", false, AttributeType.DOUBLE),
    SIX_MONTH_HIGHEST_TPAM("6 month highest tpam", false, AttributeType.DOUBLE),
    SIX_MONTH_HIGHEST_INDUSTRY_POTENTIAL_TPV("6 month highest industry potential", false, AttributeType.DOUBLE),
    TWELVE_MONTH_HIGHEST_TPV("12 month highest tpv", false, AttributeType.DOUBLE),
    TWELVE_MONTH_HIGHEST_TPAM("12 month highest tpam", false, AttributeType.DOUBLE),
    TWELVE_MONTH_HIGHEST_INDUSTRY_POTENTIAL_TPV("12 month highest industry potential", false, AttributeType.DOUBLE),
    ORDER_ID("Order ID", false, AttributeType.STRING),
    SKU("SKU", false, AttributeType.STRING),
    FLOW_ID("Flow ID", false, AttributeType.STRING),
    @JsonEnumDefaultValue
    UNKNOWN("Unknown", false, AttributeType.STRING),
    MID("Merchant ID", false, AttributeType.STRING),
    TID("Terminal ID", false, AttributeType.STRING);

    @NotBlank
    @Getter
    private final String displayText;

    @NotBlank
    @Getter
    private final boolean allowUpdate;

    @NotBlank
    @Getter
    private final AttributeType attributeType;

}
