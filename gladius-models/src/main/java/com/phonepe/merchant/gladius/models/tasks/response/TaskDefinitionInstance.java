package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskOperationsMeta;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDefinitionInstance {

    private String taskDefinitionId;

    @NotNull
    @NotEmpty
    private String name;

    @NotNull
    @NotEmpty
    private String updatedBy;

    @NotNull
    @NotEmpty
    private String createdBy;

    @NotNull
    private Priority priority;

    @NotNull
    private Namespace namespace;

    @NotNull
    private Integer points;

    @NotNull
    private String actionId;

    private Date createdAt;

    private Date updatedAt;

    private TaskDefinitionMeta taskDefinitionMeta;

    private TaskOperationsMeta taskOperationsMeta;

    private Set<String> tags;

    private Map<String, Set<String>> attributes;

    private TaskDefinitionAttributes definitionAttributes;
}
