package com.phonepe.merchant.gladius.models.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LegionModelsConstants {

    //General Constants
    public static final long DAY_IN_SECONDS = 24*3600l;
    public static final String PHONE_NUMBER = "phoneNumber";
    public static final String MERCHANT_ID = "merchantId";
    public static final String STORE_ID = "storeId";
    public static final String TASK_INSTANCE_ID = "taskInstanceId";
    public static final String CREATED_AT = "createdAt";

    //Expiry related constants
    public static final Long MAX_RELATIVE_EXPIRY_FROM_TODAY_IN_DAYS = 93L;
    public static final short DEFAULT_RELATIVE_EXPIRY = 30;
    public static final Long MAX_START_DATE_FROM_TODAY_IN_DAYS = 60L;

    //Sorter related constants
    public static final String POINTS_UNIT = "pts";
    public static final String DISTANCE_UNIT = "kms";
    public static final String SECTOR_ID_LABEL = "Sector";
    public static final String CREATED_AT_LABEL = "Created At";
    public static final String ASSIGNEE_LABEL = "Assignee";
    public static final String TASKS_UNIT = "tasks";

    //Verifier Name Constants (GENERIC VERIFIERS THAT CAN BE USED IN ANY TASK)
    public static final String STORE_CHECK_IN_VERIFIER = "store_check_in_verifier";
    public static final String NO_ACTION_VERIFIER = "no_action_verifier";
    public static final String OQC_VALIDATION_VERIFIER = "oqc_verifier";
    public static final String TASK_QC_VERIFIER = "task_qc_verifier";
    public static final String MERCHANT_KYC_VERIFIER = "merchant_kyc_verifier";
    public static final String EVENT_EXISTENCE_VERIFIER = "event_exists_verifier";
    public static final String STORE_QC_VERIFIER = "store_qc_verifier";
    public static final String BRICKBAT_FORM_FILL_VERIFIER = "brickbat_form_fill_verifier";
    public static final String P2ML_MIGRATION_VERIFIER = "p2ml_migration_verifier";
    public static final String BUSINESS_CATEGORY_UPDATE_VERIFIER = "business_category_update_verifier";

    //Verifier Name Constants (TASK SPECIFIC VERIFIERS)
    public static final String FL_DRIVE_VERIFIER = "fl_drive_verifier";
    public static final String POA_VERIFIER = "poa_verifier";
    public static final String POB_VERIFIER = "pob_verifier";
    public static final String SOUNDBOX_DEPLOYMENT_VERIFIER = "soundbox_deployment_verifier";
    public static final String SMARTSPEAKER_TROUBLESHOOT_VERIFIER = "smartspeaker_troubleshoot_verifier";
    public static final String SMARTSPEAKER_DUE_COLLECTION_VERIFIER = "smartspeaker_due_collection_verifier";
    public static final String SELF_ONBOARDING_MERCHANT_VERIFIER = "self_onboarding_merchant_verifier";
    public static final String SMARTSPEAKER_REVERSE_PICKUP_VERIFIER = "smartspeaker_reverse_pickup_verifier";
    public static final String EDC_DEPLOYMENT_VERIFIER = "edc_deployment_verifier";
    public static final String MULTI_EDC_DEPLOYMENT_VERIFIER = "multi_edc_deployment_verifier";
    public static final String EDC_REVERSE_PICKUP_VERIFIER = "edc_reverse_pickup_verifier";
    public static final String EXTERNAL_ENTITY_TO_PHONEPE_MXN_VERIFIER = "onboard_external_entity_as_phonepe_mxn_verifier";
    public static final String MERCHANT_LENDING_COMPLETION_VERIFIER = "merchant_lending_completion_verifier";
    public static final String AGENT_DEBOARDING_VERIFIER = "agent_deboarding_verifier";
    public static final String EDC_TASK_CLOSURE_VERIFIER = "edc_task_closure_verifier";
    public static final String MANAGER_SECTOR_TERRITORY_VERIFIER = "manager_sector_territory_verifier";
    public static final String PHONE_NUMBER_VERIFIER = "phone_number_verifier";
    public static final String MID_SID_VERIFIER = "mid_sid_verifier";
    public static final String VPA_VERIFIER = "vpa_verifier";
    public static final String TASK_INSTANCE_VERIFIER = "task_instance_verifier";


    //Validator Name Constants
    public static final String UNKONWN_VALIDATOR = "unknown_validator";
    public static final String BRICKBAT_FORM_FILL_VALIDATOR = "brickbat_form_fill_validator";
    public static final String LEAD_INTENT_VALIDATOR = "lead_intent_validator";
    public static final String AGENT_LOCATION_VALIDATOR = "agent_location_validator";
    public static final String AGENT_TYPE_VALIDATOR = "agent_type_validator";
    public static final String IMAGE_QC_ACTION_VALIDATOR = "image_qc_action_validator";
    public static final String EXTERNAL_ENTITY_TO_PHONEPE_MXN_VALIDATOR = "onboard_external_entity_as_phonepe_mxn_validator";
    public static final String SMARTSPEAKER_DEPLOYMENT_VALIDATOR = "smartspeaker_deployment_validator";

    //Constants used in verification logic
    public static final String STORE_DOC_FROM_MO = "storeDoc";
    public static final String FOXTROT_TIME_KEY = "time";
    public static final String EVENT_TYPE = "eventType";
    public static final String BRICKBAT_TABLE = "brickbat";
    public static final String MERCHANT_ONBOARDING_TABLE = "merchant_onboarding";

    public static final String ENTITY_ID = "entityId";
    public static final String TASK_DEFINITION_ID = "taskDefinitionId";
    public static final String ROLE = "role";
    public static final String CLIENT = "client";
    public static final String AGENT_ID = "agentId";
    public static final String CAMPAIGN_ID = "campaignId";
    public static final String TASK_TYPE = "taskType";
    public static final String MARK_AVAILABLE = "markAvailable";
    public static final String DISTANCE = "Distance";
    public static final String POINTS = "Points";
    public static final String START_DUE_DATE = "StartDueDate";
    public static final String COMPLETED_ON = "CompletedOn";
    public static final String IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY = "is_self_assigned_allowed_config";
    public static final String ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS = "eligible_actions_for_transaction_location";
    public static final String SELF_ORDER_TASK_ACTIONS = "self_order_task_actions";

    public static final String ACTIVE = "active";
    public static final String ACTION_ID = "action_id";
    public static final String DEFINITION_ID = "task_definition_id";
    public static final String IST_ZONE_ID = "Asia/Calcutta";


}
