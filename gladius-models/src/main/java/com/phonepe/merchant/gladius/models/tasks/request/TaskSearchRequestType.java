package com.phonepe.merchant.gladius.models.tasks.request;

public enum TaskSearchRequestType {
    ASSIGNED_VIEW {
        @Override
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.visitAssignedRequest(payload);
        }
    },
    DISCOVERY_VIEW {
        @Override
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.visitDiscoveryRequest(payload);
        }
    },
    SECTOR_ASSIGNED_VIEW {
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.visitSectorAssignedRequest(payload);
        }
    },
    SECTOR_DISCOVERY_VIEW {
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.visitSectorDiscoveryRequest(payload);
        }
    },
    ESCALATED_VIEW {
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.viewEscalatedView(payload);
        }
    },
    HOTSPOT_ASSIGNED_VIEW {
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.viewHotspotAssignedView(payload);
        }
    },
    HOTSPOT_DISCOVERY_VIEW {
        public <T, J> T accept(TaskSearchRequestTypeVisitor<T, J> visitor, J payload) {
            return visitor.viewHotspotDiscoveryView(payload);
        }
    };

    public abstract <T, J> T accept(TaskSearchRequestType.TaskSearchRequestTypeVisitor<T, J> visitor, J payload);

    public interface TaskSearchRequestTypeVisitor<T, J> {

        T visitAssignedRequest(J payload) ;

        T visitDiscoveryRequest(J payload);

        T visitSectorAssignedRequest(J payload);

        T visitSectorDiscoveryRequest(J payload);

        T viewEscalatedView(J payload);

        T viewHotspotAssignedView(J payload);

        T viewHotspotDiscoveryView(J payload);
    }
}
