package com.phonepe.merchant.gladius.models.tasks.storage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.dropwizard.sharding.sharding.LookupKey;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Entity
@Table(name = "task_action", uniqueConstraints = {@UniqueConstraint(columnNames = {"action_id"})})
@Builder
@Audited
@AllArgsConstructor
@NoArgsConstructor
public class StoredTaskAction implements Serializable {

    @Id
    @Column(name = "id", unique = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Setter(AccessLevel.NONE)
    private long id;

    @LookupKey
    @Column(nullable = false, name = "action_id", unique = true)
    @EqualsAndHashCode.Include
    private String actionId;

    @Column(name = "entity_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private EntityType entityType;

    @Column(name = "namespace", nullable = false)
    @Enumerated(EnumType.STRING)
    private Namespace namespace;

    @Column(name = "verification_strategy", length = 5_000)
    private byte[] verificationStrategy;

    @Column(name = "validation_strategy", length = 5_000)
    private byte[] completionValidationStrategy;

    @Column(name = "start_task_validation_strategy", length = 5_000)
    private byte[] startTaskValidationStrategy;

    @Column(name = "assign_task_validation_strategy", length = 5_000)
    private byte[] assignTaskValidationStrategy;

    @Column(name = "created_by", nullable = false, updatable = false)
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "sub_steps", length = 10_000)
    private byte[] subSteps;

    @Column(name = "created_at", updatable = false)
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss z", timezone = "IST")
    private Date createdAt;

    @Column(name = "description", nullable = false)
    private String description;

    @OneToMany(mappedBy = "taskAction", fetch = FetchType.EAGER)
    @Cascade(CascadeType.ALL)
    @Fetch(FetchMode.SUBSELECT)
    @JsonManagedReference
    private List<StoredActionAttributeMappings> attributes;

    @Column(name = "updated_at")
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss z", timezone = "IST")
    private Date updatedAt;

    public List<StoredActionAttributeMappings> getActiveAttributes() {
        return CollectionUtils.isEmpty(attributes) ? new ArrayList<>() :
                attributes.stream().filter(StoredActionAttributeMappings::isActive).toList();
    }
}
