package com.phonepe.merchant.gladius.models.tasks.request.commands;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public abstract class LocationCommandRequest extends TaskCommandRequest {

    @JsonIgnore
    protected StoredTaskInstance storedTaskInstance;

    protected EsLocationRequest actorCurrentLocation;

    protected LocationCommandRequest(AppCommand command) {
        super(command);
    }

    protected LocationCommandRequest(AppCommand command, String taskInstanceId) {
        super(command, taskInstanceId);
    }

    protected LocationCommandRequest(AppCommand complete, String taskInstanceId, EsLocationRequest actorCurrentLocation) {
        super(complete, taskInstanceId);
        this.actorCurrentLocation = actorCurrentLocation;
    }
}
