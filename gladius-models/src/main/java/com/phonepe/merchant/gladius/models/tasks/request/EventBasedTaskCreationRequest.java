package com.phonepe.merchant.gladius.models.tasks.request;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.models.merchants.tasks.EligibleEventBasedTaskType;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationClient;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationConfig;
import com.phonepe.models.merchants.tasks.TaskCreationConfig;
import com.phonepe.platform.eventingestion.model.Event;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventBasedTaskCreationRequest {

    @NotBlank
    private String entityId;

    @NotBlank
    private String taskDefinitionId;

    @NotBlank
    private String campaignId;

    private String agentId;

    @NotNull
    private Boolean markAvailable;

    private AgentType role;

    @NotNull
    private EventBasedTaskCreationClient client;

    @NotNull
    private EligibleEventBasedTaskType taskType;

    @Builder
    public EventBasedTaskCreationRequest(String entityId,
                               String agentId,
                               Boolean markAvailable,
                               AgentType role,
                               EligibleEventBasedTaskType taskType,
                               EventBasedTaskCreationConfig config,
                                         EventBasedTaskCreationClient client) {
        this.entityId = entityId;
        this.role = role;
        this.markAvailable = markAvailable;
        this.agentId = agentId;
        this.client = config.getClientName();
        this.taskType = taskType;
        TaskCreationConfig taskCreationConfig = config.getTaskCreationConfigMap().get(taskType);
        this.taskDefinitionId = taskCreationConfig.getTaskDefinitionId();
        this.campaignId = taskCreationConfig.getCampaignId();
    }

    public Event<EventBasedTaskCreationRequest> toEvent() {
        return Event.<EventBasedTaskCreationRequest>builder()
                .app("legion_task_processor")
                .eventSchemaVersion("v1")
                .groupingKey(entityId)
                .eventType("TASK_CREATION_REQUEST")
                .eventData(this)
                .id(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

}
