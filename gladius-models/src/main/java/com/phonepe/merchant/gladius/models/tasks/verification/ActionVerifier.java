package com.phonepe.merchant.gladius.models.tasks.verification;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.models.merchants.tasks.EntityType;

import java.util.Map;

public abstract class ActionVerifier {

    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        // by default there is no validation
    }

    public abstract VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context);

    public boolean validateTaskCreation(CreateTaskInstanceRequest instanceRequest, TaskActionInstance actionInstance) {
        return instanceRequest != null && actionInstance != null;
    }
}
