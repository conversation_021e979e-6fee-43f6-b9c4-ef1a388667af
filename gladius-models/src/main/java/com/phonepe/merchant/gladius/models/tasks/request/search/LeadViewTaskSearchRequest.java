package com.phonepe.merchant.gladius.models.tasks.request.search;

import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
public class LeadViewTaskSearchRequest extends TaskSearchRequest {

    private String assignedTo;

    private boolean needScheduledTasks;

    private Long startDate;

    private Long endDate;

    public LeadViewTaskSearchRequest(TaskSearchRequestType taskSearchRequestType, String assignedTo,
                                     Boolean needScheduledTasks, Long startDate, Long endDate) {
        super(taskSearchRequestType);
        this.assignedTo = assignedTo;
        this.needScheduledTasks = needScheduledTasks;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public LeadViewTaskSearchRequest() {
        super(TaskSearchRequestType.LEAD_VIEW);
    }

    protected LeadViewTaskSearchRequest(TaskSearchRequestType type) {
        super(type);
    }
}
