package com.phonepe.merchant.gladius.models.tasks.utils;

import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadTaskConfig {

    @NotNull
    @NotEmpty
    private String taskType;

    @NotNull
    @NotEmpty
    private String displayText;

    @NotNull
    @NotEmpty
    private String definitionId;

    @NotNull
    @NotEmpty
    private String campaignId;

    @Min(1)
    private int version;

    private List<IntentWithRemarks> leadCreationIntents;
}
