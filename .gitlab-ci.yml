.default: &default
  only:
    - merge_requests
    - web

.default_tag: &default_tag
  tags:
    - backend-docker-large
  image: docker.phonepe.com/ci/ubuntu/jammy/jdk/openjdk/17/maven/3.8.4:PAPG

stages:
  - quality
  - release

merge_ready:
  stage: quality
  <<: *default_tag
  only:
    refs:
      - merge_requests
    variables:
      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME != "stage"
  script:
    - export MAVEN_OPTS="-Xms3072m -Xmx3072m"
    - mvn clean verify -U -Pquality_check -Dsonar.pullrequest.key=$CI_MERGE_REQUEST_IID -Dsonar.pullrequest.branch=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME -Dsonar.pullrequest.base=master

release_feature:
  stage: release
  <<: *default_tag
  allow_failure: false
  before_script:
    - export MAVEN_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED"
  script:
    - export MAVEN_OPTS="-Xms3072m -Xmx3072m"
    - mvn -U clean package deploy -Pdocker -DskipTests=true -Dmaven.install.skip=true
  when: manual
  except:
    - qa-release
    - develop

release_qa:
  stage: release
  <<: *default_tag
  allow_failure: false
  before_script:
    - export MAVEN_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED"
  script:
    - git remote set-url origin "**********************:${CI_PROJECT_PATH}.git"
    - git fetch --all
    - git checkout qa-release
    - git pull
    - mvn deploy -DskipTests -Ddocker.build=true
  when: manual
  only:
    refs:
      - qa-release

qa_release_back_merge:
  stage: release
  <<: *default_tag
  allow_failure: false
  script:
    - git fetch --all
    - git checkout develop
    - git pull
    - git checkout qa-release
    - git pull
    - git merge develop -S -m "Automated sync from develop"
    - git push
  when: manual
  only:
    refs:
      - develop

release:
  stage: release
  <<: *default_tag
  allow_failure: false
  script:
    - git remote set-url origin "**********************:${CI_PROJECT_PATH}.git"
    - git fetch --all
    - git checkout master
    - git pull
    - git checkout develop
    - git pull
    - mvn -U -Ddocker.build=true jgitflow:release-start jgitflow:release-finish
    - git push --all
    - git push --tags origin
  when: manual
  only:
    refs:
      - develop