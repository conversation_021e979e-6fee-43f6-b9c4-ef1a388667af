| Created | By [<EMAIL>](https://frontpage.phonepe.com/people/dharmik.popat) at 20 Oct 2025 |
| :---- | :---- |
| RFC | TBD |
| Type | Engineering requirements document for Gladius Sales Hierarchy v2 Migration |

## Table of Contents

[**Summary	1**](#summary)

[**Current vs New Hierarchy	1**](#current-vs-new-hierarchy)

[**Elasticsearch Schema Changes	2**](#elasticsearch-schema-changes)

[**Task Filtering Logic Changes	2**](#task-filtering-logic-changes)

[**API Contract Changes	3**](#api-contract-changes)

[**Campaign Creation Changes	4**](#campaign-creation-changes)

[**Task Action/Definition Changes	5**](#task-actiondefinition-changes)

[**Implementation Details	5**](#implementation-details)

---

## Summary

Gladius needs to support Sales Hierarchy v2 where every combination of Business Unit (BU) + Business Type (BT) + Channel becomes an organization. This requires:

1. **Task Visibility**: Change from Role+BU filtering to OrgId+RoleV2 filtering
2. **Campaign Creation**: Support AND-based Role+BU combinations with inclusion/exclusion logic
3. **Elasticsearch**: Add new hierarchy fields for filtering
4. **API Changes**: Update task creation, campaign APIs to support new hierarchy

---

## Current vs New Hierarchy

### Current Hierarchy Fields (AgentProfile)
```java
private AgentType agentType;           // Role (AGENT, MANAGER, etc.)
private BusinessUnit businessUnit;     // BU (MASS_RETAIL, SME, etc.)
private List<UserBaseAttribute> attributes; // Tags
```

### New Hierarchy Fields (AgentProfile)
```java
private String roleV2;                 // New role field
private String orgId;                  // Organization ID (BU+BT+Channel)
private String businessUnitV2;         // New BU field
private String channel;                // DIRECT, PARTNER, etc.
private String businessType;           // LENDING, PAYMENTS, etc.
```

### Hierarchy Mapping Example
```
Old: Role=AGENT, BU=MASS_RETAIL
New: RoleV2=FIELD_AGENT, OrgId=ORG_MR_LENDING_DIRECT, BU=MASS_RETAIL, Channel=DIRECT, BT=LENDING
```

---

## Elasticsearch Schema Changes

### Current Task Index Mapping
```json
{
  "mappings": {
    "properties": {
      "rolesNotAllowed": {
        "type": "keyword"
      },
      "tags": {
        "type": "keyword"
      },
      "businessUnit": {
        "type": "keyword"
      }
    }
  }
}
```

### New Task Index Mapping (Additional Fields)
```json
{
  "mappings": {
    "properties": {
      "orgId": {
        "type": "keyword"
      },
      "roleV2": {
        "type": "keyword"
      },
      "businessUnitV2": {
        "type": "keyword"
      },
      "channel": {
        "type": "keyword"
      },
      "businessType": {
        "type": "keyword"
      },
      "hierarchyVersion": {
        "type": "keyword"
      }
    }
  }
}
```

### Task Document Structure Changes
```json
{
  "taskInstanceId": "TASK_001",
  "actionId": "ACTION_001",
  "entityId": "MERCHANT_001",

  // Current hierarchy fields (maintain for backward compatibility)
  "rolesNotAllowed": ["MANAGER"],
  "tags": ["LENDING", "HIGH_VALUE"],
  "businessUnit": "MASS_RETAIL",

  // New hierarchy fields
  "orgId": "ORG_MR_LENDING_DIRECT",
  "roleV2": "FIELD_AGENT",
  "businessUnitV2": "MASS_RETAIL",
  "channel": "DIRECT",
  "businessType": "LENDING",
  "hierarchyVersion": "v2"
}
```

---

## Task Filtering Logic Changes

### Current Filtering Implementation
```java
// BaseSearchRequestQueryBuilder.java
public BoolQueryBuilder getQuery(T request, String actor) {
    AgentProfile userProfile = legionService.getAgentProfile(actor);
    BoolQueryBuilder query = getBaseQuery(states);

    // Current filtering
    query.must(getTagFilter(ProfileUtils.tagEnricher(userProfile)));
    query.must(TaskEsUtils.getRoleFilter(userProfile.getAgentType().toString()));

    return query;
}
```

### New Filtering Implementation
```java
// Updated BaseSearchRequestQueryBuilder.java
public BoolQueryBuilder getQuery(T request, String actor) {
    AgentProfile userProfile = legionService.getAgentProfile(actor);
    BoolQueryBuilder query = getBaseQuery(states);

    // New hierarchy-aware filtering
    query.must(getTagFilter(ProfileUtils.tagEnricher(userProfile)));
    query.must(getHierarchyFilter(userProfile));

    return query;
}

private BoolQueryBuilder getHierarchyFilter(AgentProfile userProfile) {
    BoolQueryBuilder hierarchyQuery = new BoolQueryBuilder();

    // Check if agent has new hierarchy fields
    if (hasHierarchyV2(userProfile)) {
        // Use new hierarchy filtering
        hierarchyQuery.should(getOrgBasedFilter(userProfile));
    }

    // Always include old hierarchy for backward compatibility
    hierarchyQuery.should(getLegacyHierarchyFilter(userProfile));

    return hierarchyQuery;
}

private BoolQueryBuilder getOrgBasedFilter(AgentProfile userProfile) {
    BoolQueryBuilder orgQuery = new BoolQueryBuilder();

    // Task must be visible to agent's organization
    orgQuery.must(QueryBuilders.termQuery("orgId", userProfile.getOrgId()));

    // Task must be allowed for agent's role
    orgQuery.mustNot(QueryBuilders.termQuery("rolesNotAllowed", userProfile.getRoleV2()));

    return orgQuery;
}
```

### New TaskEsUtils Methods
```java
// TaskEsUtils.java - New methods
public static BoolQueryBuilder getOrgFilter(String orgId) {
    return QueryBuilders.boolQuery()
        .must(QueryBuilders.termQuery("orgId", orgId));
}

public static BoolQueryBuilder getRoleV2Filter(String roleV2) {
    return QueryBuilders.boolQuery()
        .mustNot(QueryBuilders.termQuery("rolesNotAllowed", roleV2));
}

public static BoolQueryBuilder getHierarchyVersionFilter(String version) {
    return QueryBuilders.boolQuery()
        .must(QueryBuilders.termQuery("hierarchyVersion", version));
}
```

---

## API Contract Changes

### Task Discovery APIs (No Contract Changes)
All existing task discovery APIs maintain the same request/response contracts. Changes are internal to filtering logic only.

**Affected Endpoints:**
- `GET /v1/task/search/discovery`
- `GET /v1/task/search/assigned`
- `GET /v1/task/search/escalated`
- `POST /v1/task/search` (with filters)

**Internal Changes:**
- Updated `BaseSearchRequestQueryBuilder` to use new hierarchy fields
- Enhanced `TaskEsUtils` with org-based filtering methods
- Backward compatibility maintained for agents without hierarchy v2

### Campaign Creation API Changes

#### Current Campaign Creation
**Endpoint**: `POST /v1/campaign/create`

**Current Request:**
```json
{
  "name": "Campaign Name",
  "description": "Campaign Description",
  "tags": ["TAG1", "TAG2"],
  "businessUnits": ["MASS_RETAIL", "SME"],
  "roles": ["AGENT", "MANAGER"],
  "filterOperator": "OR"
}
```

#### Enhanced Campaign Creation (New)
**Endpoint**: `POST /v1/campaign/create`

**New Request Structure:**
```json
{
  "name": "Campaign Name",
  "description": "Campaign Description",

  // Legacy fields (maintain backward compatibility)
  "tags": ["TAG1", "TAG2"],
  "businessUnits": ["MASS_RETAIL"],
  "roles": ["AGENT"],
  "filterOperator": "OR",

  // New hierarchy v2 fields
  "hierarchyV2": {
    "enabled": true,
    "orgIds": ["ORG_MR_LENDING_DIRECT", "ORG_SME_PAYMENTS_PARTNER"],
    "roleV2Combinations": [
      {
        "roles": ["FIELD_AGENT", "SENIOR_AGENT"],
        "businessUnits": ["MASS_RETAIL"],
        "channels": ["DIRECT"],
        "businessTypes": ["LENDING"],
        "operator": "AND"
      }
    ],
    "inclusionExclusionRules": {
      "include": ["R,B", "O,C"],
      "exclude": ["R,O"]
    },
    "tagOperator": "AND"
  }
}
```

**Response:** No changes to response structure

### Task Action/Definition Creation API Changes

#### Task Action Creation
**Endpoint**: `POST /v1/task/action/create`

**Enhanced Request:**
```json
{
  "actionId": "ACTION_001",
  "name": "Action Name",
  "description": "Action Description",

  // Current hierarchy support
  "rolesNotAllowed": ["MANAGER"],
  "businessUnits": ["MASS_RETAIL"],

  // New hierarchy v2 support
  "hierarchyV2": {
    "orgIds": ["ORG_MR_LENDING_DIRECT"],
    "roleV2NotAllowed": ["SENIOR_MANAGER"],
    "businessUnitV2": ["MASS_RETAIL"],
    "channels": ["DIRECT"],
    "businessTypes": ["LENDING"]
  },

  "entityType": "MERCHANT",
  "verificationStrategy": {...},
  "completionValidationStrategy": {...}
}
```

#### Task Definition Creation
**Endpoint**: `POST /v1/task/definition/create`

**Enhanced Request:**
```json
{
  "definitionId": "DEF_001",
  "actionId": "ACTION_001",
  "name": "Definition Name",

  // New hierarchy v2 fields
  "hierarchyV2": {
    "orgId": "ORG_MR_LENDING_DIRECT",
    "applicableRoleV2": ["FIELD_AGENT"],
    "businessUnitV2": "MASS_RETAIL",
    "channel": "DIRECT",
    "businessType": "LENDING"
  },

  "attributes": {...},
  "definitionAttributes": {...}
}
```

---

## Campaign Creation Changes

### Current Campaign Logic
```java
// CampaignServiceImpl.java - Current implementation
public Campaign save(Campaign request) {
    validate(request);

    // Current: OR-based filtering for tags and roles
    List<String> tags = request.getTags();
    List<String> roles = request.getRoles();
    List<String> businessUnits = request.getBusinessUnits();

    // Creates tasks visible to ANY agent matching ANY of the criteria
    StoredCampaign save = campaignRepository.save(CampaignTransformationUtils.toStoredCampaign(request));
    return CampaignTransformationUtils.toCampaign(save);
}
```

### New Campaign Logic (Hierarchy v2)
```java
// Enhanced CampaignServiceImpl.java
public Campaign save(Campaign request) {
    validate(request);

    if (request.getHierarchyV2() != null && request.getHierarchyV2().isEnabled()) {
        // New: AND-based filtering with org support
        return saveWithHierarchyV2(request);
    } else {
        // Fallback to legacy campaign creation
        return saveLegacyCampaign(request);
    }
}

private Campaign saveWithHierarchyV2(Campaign request) {
    HierarchyV2Config config = request.getHierarchyV2();

    // Create tasks for specific org combinations
    List<TaskDefinition> taskDefinitions = new ArrayList<>();

    for (String orgId : config.getOrgIds()) {
        for (RoleV2Combination combination : config.getRoleV2Combinations()) {
            if (matchesInclusionExclusionRules(combination, config.getInclusionExclusionRules())) {
                TaskDefinition taskDef = createTaskDefinitionForOrg(orgId, combination, request);
                taskDefinitions.add(taskDef);
            }
        }
    }

    // Save campaign with org-specific task definitions
    StoredCampaign save = campaignRepository.save(CampaignTransformationUtils.toStoredCampaign(request));
    return CampaignTransformationUtils.toCampaign(save);
}
```

### Inclusion/Exclusion Rules Logic
```java
// New method for handling R,B/O,C logic
private boolean matchesInclusionExclusionRules(RoleV2Combination combination, InclusionExclusionRules rules) {
    // Include rules: ["R,B", "O,C"] means include Role+BU OR Org+Channel combinations
    for (String includeRule : rules.getInclude()) {
        if (matchesRule(combination, includeRule)) {
            // Check exclude rules
            for (String excludeRule : rules.getExclude()) {
                if (matchesRule(combination, excludeRule)) {
                    return false; // Excluded
                }
            }
            return true; // Included and not excluded
        }
    }
    return false; // Not included
}

private boolean matchesRule(RoleV2Combination combination, String rule) {
    String[] parts = rule.split(",");
    // R,B = Role + BusinessUnit
    // O,C = Org + Channel
    // R,O = Role + Org
    // etc.

    switch (rule) {
        case "R,B":
            return combination.getRoles() != null && combination.getBusinessUnits() != null;
        case "O,C":
            return combination.getOrgId() != null && combination.getChannels() != null;
        case "R,O":
            return combination.getRoles() != null && combination.getOrgId() != null;
        default:
            return false;
    }
}
```

---

## Task Action/Definition Changes

### Current Task Action Model
```java
// Current StoredTaskAction
public class StoredTaskAction {
    private String actionId;
    private String name;
    private List<String> rolesNotAllowed;
    private List<String> businessUnits;
    private Map<String, Set<String>> attributes;
    // ... other fields
}
```

### Enhanced Task Action Model
```java
// Enhanced StoredTaskAction with hierarchy v2 support
public class StoredTaskAction {
    private String actionId;
    private String name;

    // Legacy hierarchy fields (maintain backward compatibility)
    private List<String> rolesNotAllowed;
    private List<String> businessUnits;

    // New hierarchy v2 fields
    private HierarchyV2Config hierarchyV2;
    private String hierarchyVersion; // "v1" or "v2"

    private Map<String, Set<String>> attributes;
    // ... other fields
}

// New hierarchy v2 configuration
public class HierarchyV2Config {
    private List<String> orgIds;
    private List<String> roleV2NotAllowed;
    private List<String> businessUnitV2;
    private List<String> channels;
    private List<String> businessTypes;
}
```

### Task Definition Changes
```java
// Enhanced TaskDefinitionServiceImpl
public TaskDefinitionInstance save(TaskDefinitionCreateRequest request) {
    validateTaskDefinitionId(request);

    TaskActionInstance taskActionInstance = taskActionService.getFromDB(
        TaskActionFetchByIdRequest.builder()
            .taskActionId(request.getActionId())
            .build()
    );

    // Enhanced validation for hierarchy v2
    if (request.getHierarchyV2() != null) {
        validateHierarchyV2Compatibility(request.getHierarchyV2(), taskActionInstance);
    }

    TaskDefinitionInstance instance = TaskDefinitionTransformationUtils.toTaskDefinitionInstance(request, taskActionInstance);

    // Set hierarchy version
    instance.setHierarchyVersion(request.getHierarchyV2() != null ? "v2" : "v1");

    StoredTaskDefinition storedTaskDefinition = TaskDefinitionTransformationUtils.toStoredTask(instance);
    StoredTaskDefinition savedStoredTaskDefinition = taskDefinitionRepository.save(storedTaskDefinition);

    return TaskDefinitionTransformationUtils.toTaskDefinitionInstance(savedStoredTaskDefinition);
}

private void validateHierarchyV2Compatibility(HierarchyV2Config hierarchyV2, TaskActionInstance taskAction) {
    // Validate that task action supports the specified org/role combinations
    if (taskAction.getHierarchyV2() != null) {
        // Ensure definition's org is compatible with action's allowed orgs
        if (!taskAction.getHierarchyV2().getOrgIds().contains(hierarchyV2.getOrgId())) {
            throw new ValidationException("Task definition org not compatible with task action");
        }
    }
}
```

---

## Implementation Details

### Key Files to Modify

#### 1. Core Filtering Logic
- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/search/query/BaseSearchRequestQueryBuilder.java`
- **Changes**: Add `getHierarchyFilter()` method, update `getQuery()` method

- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/utils/TaskEsUtils.java`
- **Changes**: Add `getOrgFilter()`, `getRoleV2Filter()`, `getHierarchyVersionFilter()` methods

#### 2. Campaign Management
- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/services/impl/CampaignServiceImpl.java`
- **Changes**: Add hierarchy v2 support, inclusion/exclusion logic

- **File**: `gladius-models/src/main/java/com/phonepe/merchant/gladius/models/campaign/Campaign.java`
- **Changes**: Add `HierarchyV2Config` field

#### 3. Task Action/Definition
- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/services/impl/TaskActionServiceImpl.java`
- **Changes**: Add hierarchy v2 validation and support

- **File**: `gladius-tasks/src/main/java/com/phonepe/merchant/legion/tasks/services/impl/TaskDefinitionServiceImpl.java`
- **Changes**: Add hierarchy v2 compatibility validation

#### 4. Agent Profile Integration
- **File**: `gladius-core/src/main/java/com/phonepe/merchant/legion/core/services/LegionService.java`
- **Changes**: Handle new hierarchy fields in agent profile responses

### Elasticsearch Index Updates
- **Index**: `agent_tasks_v6` (or create new version `agent_tasks_v7`)
- **New Fields**: `orgId`, `roleV2`, `businessUnitV2`, `channel`, `businessType`, `hierarchyVersion`
- **Migration**: Reindex existing tasks with `hierarchyVersion: "v1"`

### Feature Flag Implementation
```java
// Add feature flag for gradual rollout
@Component
public class HierarchyV2FeatureFlag {

    @Value("${gladius.hierarchy.v2.enabled:false}")
    private boolean hierarchyV2Enabled;

    @Value("${gladius.hierarchy.v2.rollout.percentage:0}")
    private int rolloutPercentage;

    public boolean isHierarchyV2Enabled(String agentId) {
        if (!hierarchyV2Enabled) {
            return false;
        }

        // Gradual rollout based on agent ID hash
        int hash = Math.abs(agentId.hashCode() % 100);
        return hash < rolloutPercentage;
    }
}
```

### Backward Compatibility Strategy
1. **Dual Filtering**: Support both old and new hierarchy in parallel
2. **Graceful Degradation**: Fall back to old hierarchy if new fields are missing
3. **Feature Flags**: Control rollout percentage and enable/disable new hierarchy
4. **Data Migration**: Gradual migration of agents from old to new hierarchy
5. **Monitoring**: Track task visibility accuracy during transition
