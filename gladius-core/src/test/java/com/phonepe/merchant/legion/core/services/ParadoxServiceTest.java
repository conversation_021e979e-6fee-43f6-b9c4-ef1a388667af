package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.LegionCoreTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.paradox.models.core.PaginatedResponse;
import com.phonepe.paradox.models.device.DeviceType;
import com.phonepe.paradox.models.troubleshoot.TicketStatus;
import com.phonepe.paradox.models.troubleshoot.TroubleShootDetails;
import com.phonepe.paradox.models.workflow.duecollection.MerchantDeviceDetails;
import com.phonepe.paradox.models.workflow.registration.responses.TaskStatus;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ATLAS_SERVICE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class ParadoxServiceTest extends LegionCoreTest {
    HttpConfiguration httpConfiguration;
    private static ServiceEndpointProviderFactory provider;
    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ServiceEndpointProvider serviceEndpointProvider;
    FoxtrotEventIngestionService eventIngestionService;
    ObjectMapper objectMapper;
    private ParadoxService paradoxService;
    MetricRegistry metricRegistry;
    ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    Response response;

    @BeforeEach
    public void setUpInner() {
        httpConfiguration = HttpConfiguration.builder().host("test").clientId(ATLAS_SERVICE).port(8080).build();
        objectMapper = new ObjectMapper();
        metricRegistry = mock(MetricRegistry.class);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        serviceDiscoveryConfiguration = ServiceDiscoveryConfiguration.builder().build();
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        provider = mock(ServiceEndpointProviderFactory.class);
        getEndPointMock(ATLAS_SERVICE);
        response = mock(Response.class);
        when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
        SerDe.init(objectMapper);

    }

    private void getEndPointMock(String clientId) {
        when(provider.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com").port(8080).secure(false).build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

    @Test
    void getTroubleshootingStatusSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            GenericResponse<TroubleShootDetails> genericResponse = GenericResponse.<TroubleShootDetails>builder()
                    .success(true)
                    .data(TroubleShootDetails.builder()
                            .deviceId("did")
                            .merchantId("mid")
                            .requestorId("rid")
                            .storeId("sid")
                            .ticketStatus(TicketStatus.INIT)
                            .workflowId("wid").build())
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            paradoxService = spy(new ParadoxService(httpConfiguration, provider, objectMapper,metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(paradoxService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(genericResponse));
            TroubleShootDetails expectedResponse = paradoxService.getTroubleshootingStatus("wid");
            Assertions.assertEquals(expectedResponse, genericResponse.getData());
        }
    }

    @Test
    void getParadoxTaskStatusFailure() throws IOException {

    try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
         MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
         MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
        authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
        httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

        GenericError genericError = GenericError.builder()
                .code("INVALID_TASK").build();
        Response response = Mockito.mock(Response.class);
        when(response.isSuccessful()).thenReturn(false);
        paradoxService = spy(new ParadoxService(httpConfiguration, provider, objectMapper,metricRegistry,
                eventIngestionService, serviceDiscoveryConfiguration));
        doReturn(response).when(paradoxService).callWithoutCheck(any(), any(), any(), any(), any());
        when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
        okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericError));
        TaskStatus expectedResponse = paradoxService.getParadoxTaskStatus("mid", "tid", "type");
        Assertions.assertEquals(TaskStatus.NOT_COMPLETED, expectedResponse);
    }
}

    @Test
    void getActiveDevicesOfMerchantSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            GenericResponse<PaginatedResponse<MerchantDeviceDetails>> genericResponse = GenericResponse.<PaginatedResponse<MerchantDeviceDetails>>builder()
                    .success(true)
                    .data(PaginatedResponse.<MerchantDeviceDetails>builder()
                            .pageNo(1)
                            .pageSize(10)
                            .results(List.of(MerchantDeviceDetails.builder()
                                    .deviceId("did")
                                    .merchantId("mid")
                                    .storeId("sid")
                                    .deviceType(DeviceType.SOUND_BOX)
                                    .build()))
                            .build())
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            paradoxService = spy(new ParadoxService(httpConfiguration, provider, objectMapper,metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(paradoxService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(genericResponse));
            PaginatedResponse<MerchantDeviceDetails> expectedResponse = paradoxService.getActiveDevicesOfMerchant("mid", "sid", List.of(DeviceType.SOUND_BOX));
            Assertions.assertEquals(expectedResponse, genericResponse.getData());
        }
    }
}