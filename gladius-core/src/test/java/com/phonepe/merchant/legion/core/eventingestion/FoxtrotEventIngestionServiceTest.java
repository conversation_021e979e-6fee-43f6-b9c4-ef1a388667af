package com.phonepe.merchant.legion.core.eventingestion;

import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.AppConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType.DEVICE_ID;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FoxtrotEventIngestionServiceTest {

    private EventIngestorClient mockClient;
    private EventIngestionProvider mockProvider;
    private FoxtrotEventIngestionService ingestionService;

    @BeforeEach
    void setUp() {
        mockClient = mock(EventIngestorClient.class);
        mockProvider = mock(EventIngestionProvider.class);

        when(mockProvider.getEventIngestorClient()).thenReturn(mockClient);

        AppConfig config = mock(AppConfig.class);
        when(config.isEventIngestionEnabled()).thenReturn(true);

        ingestionService = new FoxtrotEventIngestionService(mockProvider, config);
    }

    @Test
    void testIngestSelfAssignmentLimitBreached() throws Exception {
        // given
        String taskInstanceId = "task-123";
        String userId = "user-456";
        String actionId = "actionId";
        doNothing().when(mockClient).send(anyList());
        ingestionService.ingestSelfAssignmentLimitBreached(taskInstanceId, userId, actionId,  5, 7);

    }

    @Test
    void testIngestTaskAccessResolverFailureEvent() throws Exception {
        // given
        String clientId = "client-123";
        Exception e = new Exception("Test Exception");
        doNothing().when(mockClient).send(anyList());
        ingestionService.ingestTaskAccessResolverFailureEvent(clientId, e);
    }

    @Test
    void testIngestTaskCreationSuccessEvent() throws Exception {
        doNothing().when(mockClient).send(anyList());
        TaskCreateRequest request = TaskCreateRequest.builder().taskInstance(CreateTaskInstanceRequest.builder()
                        .taskDefinitionId("TD1")
                        .entityId("entityId")
                        .campaignId("campaignId")
                        .taskInstanceMeta(TaskInstanceMeta.builder().taskMetaList(new ArrayList<>(List.of(TaskMetaInformation.builder()
                                .type(DEVICE_ID).value("D123").displayInformation(false).build()))).build())
                        .build())
                .build();
        StoredTaskInstance taskInstance = StoredTaskInstance.builder().taskInstanceId("TI2").build();
        ingestionService.ingestTaskCreationSuccessEvent(request, taskInstance);

    }

    @Test
    void testIngestTaskCreationFailureEvent() throws Exception {
        // given
        doNothing().when(mockClient).send(anyList());
        TaskCreateRequest request = TaskCreateRequest.builder().taskInstance(CreateTaskInstanceRequest.builder()
                        .taskDefinitionId("TD1")
                        .entityId("entityId")
                        .campaignId("campaignId")
                        .build())
                .build();
        ingestionService.ingestTaskCreationRequestReceivedEvent(request);
    }

    @Test
    void testIngestTaskCreationOnTransactionLocationEvent() throws Exception {
        doNothing().when(mockClient).send(anyList());
        CreateTaskInstanceRequest request = CreateTaskInstanceRequest.builder()
                        .taskDefinitionId("TD1")
                        .entityId("entityId")
                        .campaignId("campaignId")
                        .build();
        ingestionService.ingestTaskCreationOnTransactionLocationEvent(request, 12.20, 89.90);

    }
}