package com.phonepe.merchant.legion.core;

import com.phonepe.merchant.legion.core.models.ChimeraLiteConfig;
import com.phonepe.merchant.legion.core.models.GladiusConfig;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Set;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ATLAS_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.BRICKBAT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.CLOCKWORK_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SEER_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.FORTUNA_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.FOXTROT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.GEMINI_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.HERMOD_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.INTEL_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.KILLSWITCH_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.LEGION_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_ONBOARDING_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ODIN_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.PARADOX_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SCOUT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.TMS_SERVICE;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CoreModuleTest {

    @Mock
    private AppConfig appConfig;

    @Mock
    private RangerHubConfiguration rangerHubConfiguration;

    @Mock
    private HttpConfiguration httpConfiguration;

    @InjectMocks
    private CoreModule coreModule;

    @Test
    public void testProvideClockWorkServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(CLOCKWORK_SERVICE);

        HttpConfiguration result = coreModule.providesClockWorkServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideKillSwitchConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(KILLSWITCH_SERVICE);

        HttpConfiguration result = coreModule.providesKillSwitchServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideHermodServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(HERMOD_SERVICE);

        HttpConfiguration result = coreModule.providesHermodServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideParadoxServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(PARADOX_SERVICE);

        HttpConfiguration result = coreModule.providesParadoxServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideSeerServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(SEER_SERVICE);

        HttpConfiguration result = coreModule.provideSeerServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideOdinServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(ODIN_SERVICE);

        HttpConfiguration result = coreModule.providesOdinServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideTmsServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(TMS_SERVICE);

        HttpConfiguration result = coreModule.providesTmsServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }



    @Test
    public void testProvideFoxtrotServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(FOXTROT_SERVICE);

        HttpConfiguration result = coreModule.getFoxtrotServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideFortunaServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(FORTUNA_SERVICE);

        HttpConfiguration result = coreModule.providesFortunaServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideLegionServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(LEGION_SERVICE);

        HttpConfiguration result = coreModule.getLegionServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideMerchantOnboardingServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(MERCHANT_ONBOARDING_SERVICE);

        HttpConfiguration result = coreModule.getMerchantOnboardingServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideMerchantServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(MERCHANT_SERVICE);

        HttpConfiguration result = coreModule.getMerchantServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideIntelServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(INTEL_SERVICE);

        HttpConfiguration result = coreModule.getIntelServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }



    @Test
    public void testProvideAtlasServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(ATLAS_SERVICE);

        HttpConfiguration result = coreModule.getAtlasConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }


    @Test
    public void testProvideBrickbatServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(BRICKBAT_SERVICE);

        HttpConfiguration result = coreModule.getBrickbatConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }


    @Test
    public void testProvideScoutServiceConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(SCOUT_SERVICE);

        HttpConfiguration result = coreModule.getScoutServiceConfig(appConfig);

        assertEquals(httpConfiguration, result);
    }


    @Test
    public void testProvideGladiusConfig() throws Exception {
        GladiusConfig expected = new GladiusConfig();
        when(appConfig.getGladiusConfig()).thenReturn(expected);

        GladiusConfig result = coreModule.provideGladiusConfig(appConfig);

        assertEquals(expected, result);
    }

    @Test
    public void testProvideChimeraConfig() {
        ChimeraLiteConfig expected = ChimeraLiteConfig.builder().build();
        when(appConfig.getChimeraLiteConfig()).thenReturn(expected);
        ChimeraLiteConfig actual = coreModule.getChimeraLiteConfig(appConfig);
        Assertions.assertEquals(expected, actual);
    }


    @Test
    public void testProvideGeminiConfig() throws Exception {
        when(appConfig.getRangerHubConfiguration()).thenReturn(rangerHubConfiguration);
        when(rangerHubConfiguration.getServices()).thenReturn(Set.of(httpConfiguration));
        when(httpConfiguration.getClientId()).thenReturn(GEMINI_SERVICE);
        HttpConfiguration result = coreModule.getGeminiServiceConfig(appConfig);
        assertEquals(httpConfiguration, result);
    }

    @Test
    public void testProvideTaskTypeConfig() throws Exception {
        List<String> expected = List.of("test-type");
        when(appConfig.getTaskTypes()).thenReturn(expected);
        List<String> actual = coreModule.getTaskTypes(appConfig);
        Assertions.assertEquals(expected, actual);
    }

    @Test
    public void testProvideWhitelistedDefinitionConfig() throws Exception {
        Set<String> expected = Set.of("definition-id");
        when(appConfig.getWhitelistedDefinitions()).thenReturn(expected);
        Set<String> actual = coreModule.getWhitelistedDefinitions(appConfig);
        Assertions.assertEquals(expected, actual);
    }
}