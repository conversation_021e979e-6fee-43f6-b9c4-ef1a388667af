package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.LegionCoreTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.models.ElasticSearchRequest;
import com.phonepe.merchant.legion.core.models.MerchantDetails;
import com.phonepe.merchant.legion.core.models.MerchantDetailsFilter;
import com.phonepe.merchant.legion.core.models.MerchantOnboardedByAgent;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.LegionCoreConstants;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

class MerchantOnboardingServiceTest extends LegionCoreTest {
    HttpConfiguration httpConfiguration;
    private static ServiceEndpointProviderFactory provider;

    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ServiceEndpointProvider serviceEndpointProvider;
    FoxtrotEventIngestionService eventIngestionService;
    ObjectMapper objectMapper;
    private MerchantOnboardingService merchantOnboardingService;
    MetricRegistry metricRegistry;
    ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    Response response;

    @BeforeEach
    public void setUp() {
        httpConfiguration = HttpConfiguration.builder().host("test").clientId("MerchantOnboardingService").port(8080).build();
        objectMapper = new ObjectMapper();
        metricRegistry = mock(MetricRegistry.class);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        serviceDiscoveryConfiguration = ServiceDiscoveryConfiguration.builder().build();
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        provider = mock(ServiceEndpointProviderFactory.class);
        getEndPointMock(LegionCoreConstants.MERCHANT_ONBOARDING_SERVICE);
        response = mock(Response.class);
        when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
        SerDe.init(objectMapper);
        try (MockedStatic<HttpUtils> httpUtil = Mockito.mockStatic(HttpUtils.class)) {
            httpUtil.when(() -> HttpUtils.makeOkHttpClient(any(), any()))
                    .thenReturn(client);
            merchantOnboardingService = spy(new MerchantOnboardingService(httpConfiguration, provider, objectMapper, metricRegistry, eventIngestionService, serviceDiscoveryConfiguration));
        }
    }

    private void getEndPointMock(String clientId) {
        when(provider.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com").port(8080).secure(false).build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

    @Test
    void getStoreDocSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GenericResponse<ElasticSearchRequest> genericResponse = com.phonepe.models.response.GenericResponse.<ElasticSearchRequest>builder()
                    .success(true)
                    .data(ElasticSearchRequest.builder()
                            .merchantId("mid")
                            .storeId("sid")
                            .build())
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantOnboardingService = spy(new MerchantOnboardingService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantOnboardingService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(genericResponse));
            ElasticSearchRequest expectedResponse = merchantOnboardingService.getStoreDoc("agentId1");
            assertEquals( genericResponse.getData() , expectedResponse);
        }
    }

    @Test
    void getStoreDocFailure() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            Response response = Mockito.mock(Response.class);

            when(response.isSuccessful()).thenReturn(false);
            GenericError genericError = GenericError.builder()
                    .code("NO_RESULT_FOUND").build();
            merchantOnboardingService = spy(new MerchantOnboardingService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantOnboardingService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericError));
            Assertions.assertNull(merchantOnboardingService.getStoreDoc("agentId1"));
        }
    }


    @Test
    void getMerchantsOnboardedByAgentSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GenericResponse<List<MerchantOnboardedByAgent>> genericResponse = GenericResponse.<List<MerchantOnboardedByAgent>>builder()
                    .success(true)
                    .data(List.of(MerchantOnboardedByAgent.builder()
                            .merchantId("mohit").build()))
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantOnboardingService = spy(new MerchantOnboardingService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantOnboardingService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(genericResponse));
            List<MerchantOnboardedByAgent> expectedResponse = merchantOnboardingService.getMerchantsOnboardedByAgent("agentId1", 1L, 2L);
            assertEquals( genericResponse.getData() , expectedResponse);
        }
    }

    @Test
    void getFilteredMerchantDetailsSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GenericResponse<MerchantDetails> genericResponse = GenericResponse.<MerchantDetails>builder()
                    .success(true)
                    .data(MerchantDetails.builder().build())
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantOnboardingService = spy(new MerchantOnboardingService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantOnboardingService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString((any()))).thenReturn(objectMapper.writeValueAsString(genericResponse));
            MerchantDetails expectedResponse = merchantOnboardingService.getFilteredMerchantDetails("mid", MerchantDetailsFilter.builder().build());
            assertEquals( genericResponse.getData() , expectedResponse);
        }
    }
}