package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.atlas.platform.client.atlasclient.Atlas;
import com.phonepe.atlas.platform.client.atlasclient.AtlasClient;
import com.phonepe.merchant.legion.core.AtlasClientProvider;
import com.phonepe.merchant.legion.core.LegionCoreTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.atlas.model.exceptions.ImproperRequestException;
import com.phonepe.platform.atlas.model.fence.Geofence;
import com.phonepe.platform.atlas.model.fence.Polygon;
import com.phonepe.platform.atlas.model.fence.Shape;
import com.phonepe.platform.atlas.model.fence.dto.GeofenceResponse;
import com.phonepe.platform.atlas.model.fence.dto.GeofenceSearchResponse;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ATLAS_SERVICE;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class AtlasServiceTest extends LegionCoreTest {
    HttpConfiguration httpConfiguration;
    private static ServiceEndpointProviderFactory provider;
    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ServiceEndpointProvider serviceEndpointProvider;
    FoxtrotEventIngestionService eventIngestionService;
    ObjectMapper objectMapper;
    private AtlasService atlasService;
    MetricRegistry metricRegistry;
    ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    Response response;
    AtlasClientProvider atlasClientProvider;
    Atlas atlasClient;
    @BeforeEach
    public void setUpInner() {
        httpConfiguration = HttpConfiguration.builder().host("test").clientId(ATLAS_SERVICE).port(8080).build();
        objectMapper = new ObjectMapper();
        metricRegistry = mock(MetricRegistry.class);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        serviceDiscoveryConfiguration = ServiceDiscoveryConfiguration.builder().build();
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        provider = mock(ServiceEndpointProviderFactory.class);
        getEndPointMock(ATLAS_SERVICE);
        response = mock(Response.class);
        when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
        SerDe.init(objectMapper);
        atlasClient = Mockito.mock(AtlasClient.class);
        atlasClientProvider = Mockito.mock(AtlasClientProvider.class);

    }

    private void getEndPointMock(String clientId) {
        when(provider.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com").port(8080).secure(false).build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

    @Test
    void getSectorCoordinatesSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GeofenceResponse.StoredFence storedFence = new GeofenceResponse.StoredFence();
            storedFence.setFence(Geofence.builder().geoJson(new Polygon()).build());
            GenericResponse<GeofenceResponse.StoredFence> genericResponse = com.phonepe.models.response.GenericResponse.<GeofenceResponse.StoredFence>builder()
                    .success(true)
                    .data(storedFence)
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            atlasService = spy(new AtlasService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration,atlasClientProvider));
            when(atlasClientProvider.getAtlasClient()).thenReturn(atlasClient);
            when(atlasClient.getFence(any(), any())).thenReturn(new GeofenceResponse("Success",new GeofenceResponse.StoredFence(Geofence.builder().geoJson(new Polygon()).build())));
            Shape expectedResponse = atlasService.getSectorCoordinates("agentId1");
            Assertions.assertNotNull(expectedResponse);
        }
    }

    @Test
    void getSectorCoordinatesFailure() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            Response response = Mockito.mock(Response.class);

            when(response.isSuccessful()).thenReturn(false);
            String responseErrorMsg = "{\"errorCode\":\"FENCE_NOT_FOUND\"}";
            byte[] responseMsg = responseErrorMsg.getBytes();
            atlasService = spy(new AtlasService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration, null));
            doReturn(response).when(atlasService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            when(atlasClientProvider.getAtlasClient()).thenReturn(atlasClient);
            LegionException mockedException = Mockito.mock(LegionException.class);
            when(mockedException.getCause()).thenReturn(new ImproperRequestException(""));
            doThrow(mockedException).when(atlasClient).getFence(any(), any());

            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(responseMsg);
            Assertions.assertThrows(LegionException.class, () -> atlasService.getSectorCoordinates("agentId1"));
        }
    }

    @Test
    void getSectorCoordinatesFailureImproper() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            Response response = Mockito.mock(Response.class);

            when(response.isSuccessful()).thenReturn(false);
            String responseErrorMsg = "{\"errorCode\":\"FENCE_NOT_FOUND\"}";
            byte[] responseMsg = responseErrorMsg.getBytes();
            atlasService = spy(new AtlasService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration, null));
            doReturn(response).when(atlasService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            when(atlasClientProvider.getAtlasClient()).thenReturn(atlasClient);
            doThrow(LegionException.class).when(atlasClient).getFence(any(), any());

            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(responseMsg);
            Assertions.assertThrows(LegionException.class, () -> atlasService.getSectorCoordinates("agentId1"));
        }
    }

    @Test
    void getSectorIdByLatLongSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GeofenceResponse.StoredFence storedFence = new GeofenceResponse.StoredFence();
            storedFence.setFence(Geofence.builder().geoJson(new Polygon()).build());
            GenericResponse<GeofenceResponse.StoredFence> genericResponse = com.phonepe.models.response.GenericResponse.<GeofenceResponse.StoredFence>builder()
                    .success(true)
                    .data(storedFence)
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(false);
            atlasService = spy(new AtlasService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration, atlasClientProvider));
            Mockito.when(atlasClientProvider.getAtlasClient()).thenReturn(atlasClient);
            when(atlasClient.searchFences(any(), anyDouble(), anyDouble(), anyBoolean())).thenReturn(new GeofenceSearchResponse("Success", new GeofenceSearchResponse.MatchingGeofences(List.of(Geofence.builder()
                            .id("SECTOR").build()))));
            doReturn(response).when(atlasService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericResponse));
            Assertions.assertEquals("SECTOR", atlasService.getSectorIdByLatLong(12.73, 77.53).get(0));
        }
    }
}
