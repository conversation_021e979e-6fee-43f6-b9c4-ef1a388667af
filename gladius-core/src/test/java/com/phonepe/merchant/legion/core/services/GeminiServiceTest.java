package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.gemini.model.response.order.DeliveryHubOrderResponse;
import com.phonepe.gemini.model.response.order.states.OrderProductState;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.zeus.models.GenericResponse;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.GEMINI_SERVICE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class GeminiServiceTest {

    HttpConfiguration httpConfiguration;
    private static ServiceEndpointProviderFactory provider;
    private static ServiceEndpointProvider serviceEndpointProvider;
    FoxtrotEventIngestionService eventIngestionService;
    ObjectMapper objectMapper;
    private GeminiService geminiService;
    MetricRegistry metricRegistry;
    ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    Response response;

    @BeforeEach
    public void setUpInner() {
        httpConfiguration = HttpConfiguration.builder()
                .host("test")
                .clientId(GEMINI_SERVICE)
                .port(8080)
                .build();
        objectMapper = new ObjectMapper();
        metricRegistry = mock(MetricRegistry.class);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        serviceDiscoveryConfiguration = ServiceDiscoveryConfiguration.builder()
                .build();
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        provider = mock(ServiceEndpointProviderFactory.class);
        getEndPointMock(GEMINI_SERVICE);
        response = mock(Response.class);
        when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
        SerDe.init(objectMapper);

    }

    private static void getEndPointMock(String clientId) {
        when(provider.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com")
                .port(8080)
                .secure(false)
                .build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

    @Test
    public void test_fetchOrderDetails() throws IOException {

        String taskInstanceId = "task-id";
        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(
                HttpUtils.class); MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(
                OkHttpUtils.class); MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(
                AuthHeaderProviderUtil.class)) {

            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader)
                    .thenReturn("token");
            httpUtilsMockedStatic.when(() -> HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry))
                    .thenReturn(mock(OkHttpClient.class));

            DeliveryHubOrderResponse expectedOrderResponse = DeliveryHubOrderResponse.builder()
                    .currentState(OrderProductState.CREATED)
                    .build();
            GenericResponse<DeliveryHubOrderResponse> expectedResponse = new GenericResponse<>();
            expectedResponse.setData(expectedOrderResponse);

            Response response = mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);

            geminiService = spy(
                    new GeminiService(httpConfiguration, provider, metricRegistry, objectMapper, eventIngestionService,
                            serviceDiscoveryConfiguration));
            doReturn(response).when(geminiService)
                    .call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any()))
                    .thenReturn(objectMapper.writeValueAsString(expectedResponse));

            DeliveryHubOrderResponse actualResponse = geminiService.getOrderDetails(taskInstanceId);
            Assertions.assertEquals(expectedOrderResponse, actualResponse);
        }
    }

}
