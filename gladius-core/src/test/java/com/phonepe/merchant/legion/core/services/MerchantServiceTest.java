package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.LegionCoreTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.categorytree.CategoryTreeNode;
import com.phonepe.models.merchants.categorytree.CategoryTreeNodeDetailResponse;
import com.phonepe.models.merchants.categorytree.CategoryType;
import com.phonepe.models.merchants.tasks.TaskMerchantMeta;
import com.phonepe.models.merchants.tasks.TaskStoreMeta;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ATLAS_SERVICE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class MerchantServiceTest extends LegionCoreTest {
    HttpConfiguration httpConfiguration;
    private static ServiceEndpointProviderFactory provider;
    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ServiceEndpointProvider serviceEndpointProvider;
    FoxtrotEventIngestionService eventIngestionService;
    ObjectMapper objectMapper;
    private MerchantService merchantService;
    MetricRegistry metricRegistry;
    ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    Response response;

    @BeforeEach
    public void setUpInner() {
        httpConfiguration = HttpConfiguration.builder().host("test").clientId(ATLAS_SERVICE).port(8080).build();
        objectMapper = new ObjectMapper();
        metricRegistry = mock(MetricRegistry.class);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        serviceDiscoveryConfiguration = ServiceDiscoveryConfiguration.builder().build();
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        provider = mock(ServiceEndpointProviderFactory.class);
        getEndPointMock(ATLAS_SERVICE);
        response = mock(Response.class);
        when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
        SerDe.init(objectMapper);

    }

    private void getEndPointMock(String clientId) {
        when(provider.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com").port(8080).secure(false).build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

    @Test
    void getCategoryNodeDetailsSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            GenericResponse<CategoryTreeNodeDetailResponse> genericResponse = GenericResponse.<CategoryTreeNodeDetailResponse>builder()
                    .success(true)
                    .data(CategoryTreeNodeDetailResponse.builder()
                            .node(CategoryTreeNode.builder()
                                    .categoryNodeId("node id")
                                    .value("value")
                                    .categoryType(CategoryType.CATEGORY).build()).build()
                            )
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(genericResponse));
            CategoryTreeNodeDetailResponse expectedResponse = merchantService.getCategoryNodeDetails("agentId1");
            Assertions.assertEquals(expectedResponse, genericResponse.getData());
        }
    }

    @Test
    void getListOfStoresSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            List<TaskStoreMeta> stores = List.of(TaskStoreMeta.builder().build());
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(stores));
            List<TaskStoreMeta> expectedResponse = merchantService.getListOfStores(Set.of("agentId1"));
            Assertions.assertEquals(expectedResponse, stores);
        }
    }


    @Test
    void getListOfMerchantsSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            List<TaskMerchantMeta> stores = List.of(TaskMerchantMeta.builder().build());
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(stores));
            List<TaskMerchantMeta> expectedResponse = merchantService.getListOfMerchants(Set.of("agentId1"));
            Assertions.assertEquals(expectedResponse, stores);
        }
    }


    @Test
    void getListOfMerchantStoresSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            List<PhysicalStore> stores = List.of(PhysicalStore.builder().build());
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(stores));
            List<PhysicalStore> expectedResponse = merchantService.getListOfMerchantStores("mid", 3);
            Assertions.assertEquals(expectedResponse, stores);
        }
    }


    @Test
    void getMerchantFactDetailsV1Success() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            MerchantProfile profile = MerchantProfile.builder()
                    .merchantId("mid")
                    .fullName("mohit")
                    .displayName("puri")
                    .email("<EMAIL>")
                    .mcc("0000")
                    .phoneNumber("**********")
                    .disabled(false).build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(profile));
            MerchantProfile expectedResponse = merchantService.getMerchantFactDetails("mid");
            Assertions.assertEquals(expectedResponse, profile);
        }
    }

    @Test
    void getStoreDetailsSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));

            PhysicalStore profile = PhysicalStore.builder()
                    .merchantId("mid")
                    .displayName("puri")
                    .phoneNumber("**********")
                    .storeId("sid")
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsString(any())).thenReturn(objectMapper.writeValueAsString(profile));
            PhysicalStore expectedResponse = merchantService.getStoreDetails("mid", "sid");
            Assertions.assertEquals(expectedResponse, profile);
        }
    }

    @Test
    void getMerchantDetailsFailure() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            Response response = Mockito.mock(Response.class);

            when(response.isSuccessful()).thenReturn(false);
            GenericError genericError = GenericError.builder()
                    .code("INVALID_MERCHANT_ID").build();
            merchantService = spy(new MerchantService(httpConfiguration, provider, objectMapper, metricRegistry,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(merchantService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericError));
            Assertions.assertThrows(LegionException.class, () -> merchantService.getMerchantDetails("agentId1"));
        }
    }
}