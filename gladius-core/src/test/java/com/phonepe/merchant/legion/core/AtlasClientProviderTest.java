package com.phonepe.merchant.legion.core;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.atlas.platform.client.AtlasClientBuilder;
import com.phonepe.atlas.platform.client.atlasclient.Atlas;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;


public class AtlasClientProviderTest {
    private EventIngestorClientConfig eventIngestorClientConfig;
    private ServiceEndpointProviderFactory serviceEndpointProviderFactory;
    private ObjectMapper objectMapper;
    private MetricRegistry metricRegistry;
    private AppConfig appConfig;

    private AtlasClientProvider atlasClientProvider;

    @BeforeEach
    public void setup() {
        eventIngestorClientConfig = new EventIngestorClientConfig();
        serviceEndpointProviderFactory = Mockito.mock(ServiceEndpointProviderFactory.class);
        objectMapper = Mockito.mock(ObjectMapper.class);
        metricRegistry = Mockito.mock(MetricRegistry.class);
        appConfig = Mockito.mock(AppConfig.class);
    }

    @Test
    void testConstructor() throws Exception {
        atlasClientProvider = new AtlasClientProvider(serviceEndpointProviderFactory, appConfig, objectMapper);
        Assertions.assertNotNull(atlasClientProvider);
    }

    @Test
    void testStart() throws Exception {
        Atlas atlasClient = Mockito.mock(Atlas.class);
        MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class);
        authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
        MockedStatic<AtlasClientBuilder> atlasClientProviderMockedStatic = Mockito.mockStatic(AtlasClientBuilder.class);
        atlasClientProviderMockedStatic.when(() -> AtlasClientBuilder.build(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(atlasClient);
        atlasClientProvider = new AtlasClientProvider(serviceEndpointProviderFactory, appConfig, objectMapper);
        atlasClientProvider.start();
        authHeaderProviderUtilMockedStatic.close();
        atlasClientProviderMockedStatic.close();
        Assertions.assertTrue(true);
    }

}
