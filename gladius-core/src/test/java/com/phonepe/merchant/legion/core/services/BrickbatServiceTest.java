package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.LegionCoreTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.brickbat.models.feedback.FeedbackApprovalStatus;
import com.phonepe.platform.brickbat.models.feedback.StoredFeedback;
import com.phonepe.platform.brickbat.models.feedback.StoredSurvey;
import com.phonepe.platform.brickbat.models.question.MCQQuestion;
import com.phonepe.platform.brickbat.models.question.Question;
import com.phonepe.platform.brickbat.models.question.options.Option;
import com.phonepe.platform.brickbat.models.user.response.CreateFeedbackResponse;
import com.phonepe.platform.brickbat.models.widget.WidgetType;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.BRICKBAT_SERVICE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class BrickbatServiceTest extends LegionCoreTest {
    HttpConfiguration httpConfiguration;
    private static ServiceEndpointProviderFactory provider;
    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ServiceEndpointProvider serviceEndpointProvider;
    FoxtrotEventIngestionService eventIngestionService;
    ObjectMapper objectMapper;
    private BrickbatService brickbatService;
    MetricRegistry metricRegistry;
    ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    Response response;

    @BeforeEach
    public void setUpInner() {
        httpConfiguration = HttpConfiguration.builder().host("test").clientId(BRICKBAT_SERVICE).port(8080).usingDiscovery(false).build();
        objectMapper = new ObjectMapper();
        metricRegistry = mock(MetricRegistry.class);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        serviceDiscoveryConfiguration = ServiceDiscoveryConfiguration.builder().build();
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        provider = mock(ServiceEndpointProviderFactory.class);
        getEndPointMock(BRICKBAT_SERVICE);
        response = mock(Response.class);
        when(response.body()).thenReturn(mock(okhttp3.ResponseBody.class));
        SerDe.init(objectMapper);

    }

    private void getEndPointMock(String clientId) {
        when(provider.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com").port(8080).secure(false).build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

    @Test
    void getSurveyResultSuccess() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            StoredSurvey storedSurvey = StoredSurvey.builder()
                    .active(true)
                    .approvalStatus(FeedbackApprovalStatus.APPROVED)
                    .campaignId("camapaignId").build();
            GenericResponse<StoredFeedback> genericResponse = com.phonepe.models.response.GenericResponse.<StoredFeedback>builder()
                    .success(true)
                    .data(storedSurvey)
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            brickbatService = spy(new BrickbatService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(brickbatService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericResponse));
            GenericResponse<StoredFeedback> expectedResponse = brickbatService.getSurveyResult("FeedbackId");
            Assertions.assertTrue(expectedResponse.isSuccess());
        }
    }

    @Test
    void getSurveyResultFailure() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            Response response = Mockito.mock(Response.class);
            GenericError genericError = GenericError.builder()
                    .code("FEEDBACK_NOT_FOUND").build();
            when(response.isSuccessful()).thenReturn(false);
            brickbatService = spy(new BrickbatService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(brickbatService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericError));
            Assertions.assertThrows(LegionException.class, () -> brickbatService.getSurveyResult("agentId1"));
        }
    }

    @Test
    void getCampaignQuestionsSuccess() throws IOException {
        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GenericResponse<Map<String, Question>> genericResponse = GenericResponse.<Map<String, Question>>builder()
                    .success(true)
                    .data(Map.of("question", new MCQQuestion("Id", 23433L,123232L,  "title", 1, Map.of("Optins", Option.builder().build()), WidgetType.MCQ_MULTI, true)))
                    .build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            brickbatService = spy(new BrickbatService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(brickbatService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericResponse));
            GenericResponse<Map<String, Question>> expectedResponse = brickbatService.getCampaignQuestions("FeedbackId");
            Assertions.assertTrue(expectedResponse.isSuccess());
        }
    }

    @Test
    void getCampaignQuestionsFailure() throws IOException {

        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            Response response = Mockito.mock(Response.class);
            GenericError genericError = GenericError.builder()
                    .code("CAMPAIGN_NOT_FOUND").build();
            when(response.isSuccessful()).thenReturn(false);
            brickbatService = spy(new BrickbatService(httpConfiguration, provider, metricRegistry, objectMapper,
                    eventIngestionService, serviceDiscoveryConfiguration));
            doReturn(response).when(brickbatService).callWithoutCheck(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericError));
            Assertions.assertThrows(LegionException.class, () -> brickbatService.getCampaignQuestions("agentId1"));
        }
    }

    @Test
    void createSurveyBulkForAgentUserSuccess() throws IOException {
        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class)) {
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GenericResponse<CreateFeedbackResponse> genericResponse = GenericResponse.<CreateFeedbackResponse>builder()
                    .success(true)
                    .data(null).build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            brickbatService = spy(new BrickbatService(httpConfiguration, provider, metricRegistry, objectMapper, eventIngestionService,
                    serviceDiscoveryConfiguration));
            doReturn(response).when(brickbatService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericResponse));
            GenericResponse<CreateFeedbackResponse> expectedResponse = brickbatService.createSurveyBulkForAgentUser(null, "endUserId");
            Assertions.assertTrue(expectedResponse.isSuccess());
        }
    }

    @Test
    void getQuestionsFromQuestionIdsSuccess() throws IOException {
        try (MockedStatic<HttpUtils> httpUtilsMockedStatic = Mockito.mockStatic(HttpUtils.class);
             MockedStatic<OkHttpUtils> okHttpUtilsMockedStatic = Mockito.mockStatic(OkHttpUtils.class);
             MockedStatic<AuthHeaderProviderUtil> authHeaderProviderUtilMockedStatic = Mockito.mockStatic(AuthHeaderProviderUtil.class)) {
            authHeaderProviderUtilMockedStatic.when(AuthHeaderProviderUtil::getSystemAuthHeader).thenReturn("token");
            httpUtilsMockedStatic.when(() ->  HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry)).thenReturn(mock(OkHttpClient.class));
            GenericResponse<List<Question>> genericResponse = GenericResponse.<List<Question>>builder()
                    .success(true)
                    .data(null).build();
            Response response = Mockito.mock(Response.class);
            when(response.isSuccessful()).thenReturn(true);
            brickbatService = spy(new BrickbatService(httpConfiguration, provider, metricRegistry, objectMapper, eventIngestionService,
                    serviceDiscoveryConfiguration));
            doReturn(response).when(brickbatService).call(any(), any(), any(), any(), any());
            when(response.body()).thenReturn(Mockito.mock(okhttp3.ResponseBody.class));
            okHttpUtilsMockedStatic.when(() -> OkHttpUtils.bodyAsBytes(any())).thenReturn(objectMapper.writeValueAsBytes(genericResponse));
            GenericResponse<List<Question>> expectedResponse = brickbatService.getQuestionsFromQuestionIds("q1,q2");
            Assertions.assertTrue(expectedResponse.isSuccess());
        }
    }





}