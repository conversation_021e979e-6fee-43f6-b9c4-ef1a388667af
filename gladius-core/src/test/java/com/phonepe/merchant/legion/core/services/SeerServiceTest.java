package com.phonepe.merchant.legion.core.services;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.INVALID_TASK_ID_ERROR;

public class SeerServiceTest {

    @Mock
    private HttpConfiguration httpConfiguration;

    @Mock
    private ServiceEndpointProviderFactory serviceEndpointProviderFactory;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private FoxtrotEventIngestionService eventIngestionService;

    @Mock
    private ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;

    @Mock
    private OkHttpClient okHttpClient;

    @Mock
    private Response mockResponse;

    @Mock
    private ResponseBody responseBody;

    private SeerService seerService;
    private static final String TEST_GLADIUS_TASK_ID = "TI2510101727497799878202";
    private static final String TOKEN = "abc";
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Initialize SerDe with ObjectMapper
        SerDe.init(new ObjectMapper());

        seerService = spy(new SeerService(
                httpConfiguration,
                serviceEndpointProviderFactory,
                objectMapper,
                metricRegistry,
                eventIngestionService,
                serviceDiscoveryConfiguration
        ));

        // Mock inherited methods from CommunicationService
        doNothing().when(seerService).ingestServiceFailureEvent(anyString(), any(Integer.class), any(Integer.class));
        doNothing().when(seerService).handleResponseFailure(anyString(), anyString(), any(Integer.class));
    }

    @Test
    void testGetTaskClosureVerificationStatus_SuccessfulResponse() {
        when(mockResponse.isSuccessful()).thenReturn(true);
        doReturn(mockResponse).when(seerService).callWithoutCheck(
                eq("getTaskClosureVerificationStatus"),
                eq("v1/servicing/tasks/" + TEST_GLADIUS_TASK_ID + "/closure-verification"),
                eq(null),
                eq("get"),
                any(Headers.class)
        );
        doReturn(Headers.of("Authorization", TOKEN)).when(seerService).getSystemAuthHeader();

        boolean result = seerService.getTaskClosureVerificationStatus(TEST_GLADIUS_TASK_ID); // Changed to boolean return type

        assertTrue(result, "Successful 204 response should return true");
    }

    @Test
    void testGetTaskClosureVerificationStatus_InvalidTaskIdError() {
        GenericError errorResponse = GenericError.builder()
                .code(INVALID_TASK_ID_ERROR)
                .message("Invalid task ID")
                .build();

        byte[] errorResponseBytes = SerDe.writeValueAsBytes(errorResponse);

        when(mockResponse.isSuccessful()).thenReturn(false);
        doReturn(mockResponse).when(seerService).callWithoutCheck(
                eq("getTaskClosureVerificationStatus"),
                eq("v1/servicing/tasks/" + TEST_GLADIUS_TASK_ID + "/closure-verification"),
                eq(null),
                eq("get"),
                any(Headers.class)
        );
        doReturn(errorResponseBytes).when(seerService).body(mockResponse);
        doReturn(Headers.of("Authorization", TOKEN)).when(seerService).getSystemAuthHeader();

        boolean result = seerService.getTaskClosureVerificationStatus(TEST_GLADIUS_TASK_ID);

        assertFalse(result, "INVALID_TASK_ID error should return false");
    }
}
