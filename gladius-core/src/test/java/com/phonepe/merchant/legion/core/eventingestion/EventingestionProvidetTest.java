package com.phonepe.merchant.legion.core.eventingestion;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.dataplatform.ClientType;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;

public class EventingestionProvidetTest {

    private EventIngestorClientConfig eventIngestorClientConfig;
    private ServiceEndpointProviderFactory serviceEndpointProviderFactory;
    private ObjectMapper objectMapper;
    private MetricRegistry metricRegistry;

    private EventIngestionProvider eventIngestionProvider;

    @BeforeEach
    public void setup() {
        objectMapper = new ObjectMapper();
        eventIngestorClientConfig = new EventIngestorClientConfig();
        serviceEndpointProviderFactory = Mockito.mock(ServiceEndpointProviderFactory.class);
        metricRegistry = Mockito.mock(MetricRegistry.class);
    }

    @Test
    public void testConstructor() throws Exception {
        EventIngestorClientConfig eventIngestorClientConfig = new EventIngestorClientConfig(0, 0, ClientType.async, "", 0L, 0, true, "FARM", "", false, Collections.EMPTY_SET);
        eventIngestionProvider = new EventIngestionProvider(eventIngestorClientConfig, serviceEndpointProviderFactory, objectMapper, metricRegistry);
        Assertions.assertNotNull(eventIngestionProvider);
    }
}
