package com.phonepe.merchant.legion.core.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.repackaged.com.google.common.base.Preconditions;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import javax.ws.rs.ext.Provider;
import java.io.InputStream;

@Provider
@Slf4j
public class SerDe {
    private static ObjectMapper mapper;

    public static void init(ObjectMapper objectMapper) {
        mapper = objectMapper;
    }

    @Nullable
    public static String writeValueAsString(Object value) {
        return writeValueAsString(mapper(), value);
    }

    @Nullable
    public static String writeValueAsString(ObjectMapper objectMapper, Object value) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.writeValueAsString(value);
        } catch (Exception e) {
            log.error("Error while serializing object" + value, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static byte[] writeValueAsBytes(Object value) {
        return writeValueAsBytes(mapper(), value);
    }

    @Nullable
    public static byte[] writeValueAsBytes(ObjectMapper objectMapper, Object value) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.writeValueAsBytes(value);
        } catch (Exception e) {
            log.error("Error while serializing object" + value, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T readValue(String value, Class<T> valueType) {
        return readValue(mapper(), value, valueType);
    }

    @Nullable
    public static <T> T readValue(ObjectMapper objectMapper, String value, Class<T> valueType) {
        try {
            if (value == null) {
                log.info("Object is null for:{}", valueType);
                return null;
            }
            return objectMapper.readValue(value, valueType);
        } catch (Exception e) {
            log.error("Error while deserializing object" + value + " class:" + valueType, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T readValue(byte[] value, Class<T> valueType) {
        return readValue(mapper(), value, valueType);
    }

    @Nullable
    public static <T> T readValue(ObjectMapper objectMapper, byte[] value, Class<T> valueType) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.readValue(value, valueType);
        } catch (Exception e) {
            log.error("Error while deserializing byte[]" + new String(value) + " class:" + valueType, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T readValue(byte[] value, TypeReference<T> valueTypeRef) {
        return readValue(mapper(), value, valueTypeRef);
    }

    @Nullable
    public static <T> T readValue(ObjectMapper objectMapper, byte[] value, TypeReference<T> valueTypeRef) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.readValue(value, valueTypeRef);
        } catch (Exception e) {
            log.error("Error while deserializing byte[]" + new String(value) + " valueTypeRef:" + valueTypeRef, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T readValue(String value, TypeReference<T> valueTypeRef) {
        return readValue(mapper(), value, valueTypeRef);
    }

    @Nullable
    public static <T> T readValue(ObjectMapper objectMapper, String value, TypeReference<T> valueTypeRef) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.readValue(value, valueTypeRef);
        } catch (Exception e) {
            log.error("Error while deserializing value" + value + " valueTypeRef:" + valueTypeRef, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static JsonNode readTree(String value) {
        return readTree(mapper(), value);
    }

    @Nullable
    public static JsonNode readTree(ObjectMapper objectMapper, String value) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.readTree(value);
        } catch (Exception e) {
            log.error("Error while readTress value" + value, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }


    @Nullable
    public static <T> T readValue(InputStream src, Class<T> valueType) {
        return readValue(mapper(), src, valueType);
    }

    @Nullable
    public static <T> T readValue(InputStream src, TypeReference<T> valueTypeRef) {
        try {
            if (src == null) {
                return null;
            }
            return mapper().readValue(src, valueTypeRef);
        } catch (Exception e) {
            log.error("Error while deserializing inputStream valueType:" + valueTypeRef, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T readValue(ObjectMapper objectMapper, InputStream src, Class<T> valueType) {
        try {
            if (src == null) {
                return null;
            }
            return objectMapper.readValue(src, valueType);
        } catch (Exception e) {
            log.error("Error while deserializing inputStream valueType:" + valueType, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        return convertValue(mapper(), fromValue, toValueType);
    }

    @Nullable
    public static <T> T convertValue(ObjectMapper objectMapper, Object fromValue, Class<T> toValueType) {
        try {
            if (fromValue == null) {
                return null;
            }
            return objectMapper.convertValue(fromValue, toValueType);
        } catch (Exception e) {
            log.error("Error while deserializing fromValue" + fromValue + " toValueType:" + toValueType, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static <T> T convertValue(Object fromValue, TypeReference<T> valueTypeRef) {
        return convertValue(mapper(), fromValue, valueTypeRef);
    }

    @Nullable
    public static <T> T convertValue(ObjectMapper objectMapper, Object fromValue, TypeReference<T> valueTypeRef) {
        try {
            if (fromValue == null) {
                return null;
            }
            return objectMapper.convertValue(fromValue, valueTypeRef);
        } catch (Exception e) {
            log.error("Error while deserializing fromValue" + fromValue + " valueTypeRef:" + valueTypeRef, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }

    @Nullable
    public static JsonNode toJsonNode(Object value) {
        return toJsonNode(mapper(), value);
    }

    @Nullable
    public static JsonNode toJsonNode(ObjectMapper objectMapper, Object value) {
        try {
            if (value == null) {
                return null;
            }
            return objectMapper.valueToTree(value);
        } catch (Exception e) {
            log.error("Error while converting object to JsonNode: " + value, e);
            throw LegionException.propagate(CoreErrorCode.JSON_ERROR, e);
        }
    }


    private static ObjectMapper mapper() {
        Preconditions.checkNotNull(mapper, "Please call SerDe.init(mapper) to set mapper");
        return mapper;
    }

}
