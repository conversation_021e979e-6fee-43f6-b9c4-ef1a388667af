package com.phonepe.merchant.legion.core.eventingestion;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.dropwizard.lifecycle.Managed;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Getter
public class EventIngestionProvider implements Managed {

    private final EventIngestorClient eventIngestorClient;

    @Inject
    public EventIngestionProvider(
            final EventIngestorClientConfig eventIngestorClientConfig,
            final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
            final ObjectMapper objectMapper,
            final MetricRegistry metricRegistry
    ) {
        try {
            this.eventIngestorClient = new EventIngestorClient(eventIngestorClientConfig, serviceEndpointProviderFactory, objectMapper, metricRegistry);
        } catch (Exception e) {
            log.error("Error while initializing eventIngestionClient", e);
            throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
        }
    }

    @VisibleForTesting
    public EventIngestionProvider(final EventIngestorClient eventIngestorClient) {
        this.eventIngestorClient = eventIngestorClient;
    }

    @Override
    public void start() throws Exception {
        log.info("Starting the Event Ingestion Provider");
        eventIngestorClient.start();
        log.info("Started the Event Ingestion Provider");
    }

    @Override
    public void stop() throws Exception {
        log.info("Stopping the Event Ingestion Provider");
        eventIngestorClient.close();
    }
}