package com.phonepe.merchant.legion.core.eventingestion.models;

import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskCreationSuccessEvent {

    private String taskInstanceId;

    private String entityId;

    private String campaignId;

    private String taskDefinitionId;

    private String createdBy;

    private Map<TaskMetaType, Object> userTaskCreationMeta;

    private boolean markAvailable;

    @Builder
    TaskCreationSuccessEvent(TaskCreateRequest request, StoredTaskInstance storedTaskInstance) {
        this.taskDefinitionId = request.getDefinitionId();
        this.campaignId = request.getTaskInstance().getCampaignId();
        this.entityId = request.getTaskInstance().getEntityId();
        this.taskInstanceId = storedTaskInstance.getTaskInstanceId();
        this.markAvailable = request.isMarkAvailable();
        this.createdBy = request.getTaskInstance().getCreatedBy();
        this.userTaskCreationMeta = (request.getTaskInstance().getTaskInstanceMeta() != null &&
                request.getTaskInstance().getTaskInstanceMeta().getTaskMetaAsMap() != null)
                ? request.getTaskInstance().getTaskInstanceMeta().getTaskMetaAsMap()
                : Collections.emptyMap();
    }
}
