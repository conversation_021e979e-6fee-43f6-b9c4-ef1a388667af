package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.phonepe.merchant.gladius.models.tasks.request.TaskOperationsMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskRecreationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.actor.ActorMessage;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.platform.clockwork.model.ClockworkResponse;
import com.phonepe.platform.clockwork.model.OnceOnlyTimeSpec;
import com.phonepe.platform.clockwork.model.SchedulingRequest;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import com.phonepe.platform.clockwork.model.request.CallType;
import com.phonepe.platform.clockwork.model.request.RangerCallScheduledAction;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.http.HttpStatus;

import javax.ws.rs.core.MediaType;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.CLOCKWORK_SERVICE_CONFIG;

@Slf4j
@Singleton
public class ClockWorkService extends CommunicationService {

    private static final String CLOCKWORK_CLIENT_ID = "gladius";

    @Inject
    public ClockWorkService(@Named(value = CLOCKWORK_SERVICE_CONFIG) final HttpConfiguration httpConfiguration,
                            final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                            MetricRegistry metricRegistry,
                            ObjectMapper objectMapper,
                            FoxtrotEventIngestionService eventIngestionService,
                            ServiceDiscoveryConfiguration serviceDiscoveryConfiguration) {
        super(httpConfiguration,
                HttpUtils.makeOkHttpClient(httpConfiguration,metricRegistry),
                () -> serviceEndpointProviderFactory.provider(httpConfiguration.getClientId()),
                objectMapper,
                eventIngestionService,
                serviceDiscoveryConfiguration);
    }

    public ClockworkResponse<SchedulingResponse> scheduleClockworkForProvider(ActorMessage data, Date requestDateTime)  {
        String commandName = "scheduleClockworkForProvider";
        try{

            RangerCallScheduledAction rangerCallScheduledAction = RangerCallScheduledAction.builder()
                    .callbackPath("/v1/task/instance/verify/execute")
                    .callType(CallType.POST)
                    .mimeType(MediaType.APPLICATION_JSON)
                    .payload(SerDe.writeValueAsString(data))
                    .successStatusCodes(Set.of(HttpStatus.SC_OK))
                    .headers(Map.of("Content-Type", "application/json"))
                    .build();

            SchedulingRequest schedulingRequest = SchedulingRequest.builder()
                    .time(OnceOnlyTimeSpec.builder()
                            .at(requestDateTime)
                            .build())
                    .action(rangerCallScheduledAction)
                    .build();
            Response response = call(commandName,
                    "/jobs/"+ CLOCKWORK_CLIENT_ID +"/v2",
                    schedulingRequest,
                    CallTypes.POST,
                    getSystemAuthHeader()
            );
            return readValue(response, new TypeReference<>() {
            });
        } catch (Exception ex){
            log.error(ERROR_LOG, commandName, ex.getMessage());
            throw LegionException.error(
                    CoreErrorCode.COMMUNICATION_ERROR,
                    Map.of(COMMAND_NAME, commandName,
                            RESPONSE,  ex.getMessage()));
        }
    }

    public ClockworkResponse<SchedulingResponse> scheduleClockworkForTaskRecreation(StoredTaskInstance storedTaskInstance, TaskOperationsMeta taskOperationsMeta) {
        String commandName = "scheduleClockworkForTaskRecreation";
        try {

            TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                    .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                    .build();


            RangerCallScheduledAction rangerCallScheduledAction = RangerCallScheduledAction.builder()
                    .callType(CallType.POST)
                    .callbackPath("/v1/task/instance/recreate")
                    .mimeType(MediaType.APPLICATION_JSON)
                    .payload(SerDe.writeValueAsString(taskRecreationRequest))
                    .successStatusCodes(Set.of(HttpStatus.SC_OK))
                    .headers(Map.of("Content-Type", "application/json"))
                    .build();

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.SECOND, taskOperationsMeta.getRecreationInterval());
            SchedulingRequest request = SchedulingRequest.builder()
                    .time(OnceOnlyTimeSpec.builder()
                            .at(calendar.getTime())
                            .build())
                    .action(rangerCallScheduledAction)
                    .build();

            Response response = call(commandName,
                    "/jobs/"+ CLOCKWORK_CLIENT_ID +"/v2",
                    request,
                    CallTypes.POST,
                    getSystemAuthHeader()
            );
            return readValue(response, new TypeReference<>() {
            });
        }
        catch(Exception e) {
            log.error(ERROR_LOG,commandName,e.getMessage());
            throw LegionException.error(
                    CoreErrorCode.COMMUNICATION_ERROR,
                    Map.of(COMMAND_NAME, commandName,
                            RESPONSE,  e.getMessage()));
        }
    }
}
