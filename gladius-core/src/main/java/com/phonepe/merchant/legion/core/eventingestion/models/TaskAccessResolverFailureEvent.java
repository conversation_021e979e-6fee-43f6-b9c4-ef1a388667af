package com.phonepe.merchant.legion.core.eventingestion.models;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskAccessResolverFailureEvent {

    private String clientId;
    private String accessResolverErrorMessage;
    private String accessResolverExceptionDetails;
    private String accessResolverRootCause;
}
