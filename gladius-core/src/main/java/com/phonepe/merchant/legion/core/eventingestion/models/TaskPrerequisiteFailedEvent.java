package com.phonepe.merchant.legion.core.eventingestion.models;

import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskPrerequisiteFailedEvent {

    private LocationCommandRequest taskRequest;
    private Map<String,Boolean> error;
    private String command;

}
