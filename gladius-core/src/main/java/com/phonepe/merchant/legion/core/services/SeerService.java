package com.phonepe.merchant.legion.core.services;


import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.Response;

import javax.ws.rs.core.UriBuilder;
import java.net.URI;
import java.util.List;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SEER_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.INVALID_TASK_ID_ERROR;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.WORKFLOW_NOT_COMPLETED_ERROR;

@Singleton
public class SeerService extends CommunicationService {

    private static final List<String> verificationFailureErrorCodes = List.of(INVALID_TASK_ID_ERROR, WORKFLOW_NOT_COMPLETED_ERROR);

    @Inject
    public SeerService(@Named(value = SEER_SERVICE_CONFIG) final HttpConfiguration httpConfiguration,
                       final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                       ObjectMapper mapper,
                       MetricRegistry metricRegistry,
                       FoxtrotEventIngestionService eventIngestionService,
                       ServiceDiscoveryConfiguration serviceDiscoveryConfiguration) {
        super(httpConfiguration,
                HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry),
                () -> serviceEndpointProviderFactory.provider(httpConfiguration.getClientId()),
                mapper,
                eventIngestionService,
                serviceDiscoveryConfiguration);
    }

    public boolean getTaskClosureVerificationStatus(String gladiusTaskId) {
        String commandName = "getTaskClosureVerificationStatus";
        URI uri = UriBuilder.fromUri("v1/servicing/tasks/" + gladiusTaskId + "/closure-verification").build();

        Response response = callWithoutCheck(
                commandName,
                uri.toString(),
                null,
                CallTypes.GET,
                getSystemAuthHeader()
        );

        if (!response.isSuccessful()) {
            byte[] responseBody = body(response);
            GenericError errorResponse = SerDe.readValue(responseBody, GenericError.class);
            String responseString = (responseBody != null) ? new String(responseBody) : null;
            if (!verificationFailureErrorCodes.contains(errorResponse.getCode())) {
                handleResponseFailure(commandName, responseString, response.code());
            }
            return false;
        }
        return true;
    }
}