package com.phonepe.merchant.legion.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.phonepe.merchant.gladius.models.survey.ClientQcTaskConfig;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.config.AttributeInfoConfig;
import com.phonepe.merchant.legion.core.config.AuditConfig;
import com.phonepe.merchant.legion.core.entitystore.ESConnection;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.models.ChimeraLiteConfig;
import com.phonepe.merchant.legion.core.models.GladiusConfig;
import com.phonepe.merchant.legion.core.models.StoreCheckInConfig;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.repository.impl.ESRepositoryImpl;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import io.dropwizard.setup.Environment;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.ERROR_IN_GETTING_HTTP_CONFIGURATION;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ATLAS_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ATLAS_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.BRICKBAT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.BRICKBAT_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.CLOCKWORK_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.CLOCKWORK_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SEER_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SEER_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.FORTUNA_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.FORTUNA_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.FOXTROT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.FOXTROT_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.GEMINI_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.GEMINI_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.HERMOD_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.HERMOD_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.INTEL_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.INTEL_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.KILLSWITCH_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.KILLSWITCH_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.LEGION_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.LEGION_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_ONBOARDING_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_ONBOARDING_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ODIN_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ODIN_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.PARADOX_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.PARADOX_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SCOUT_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.SCOUT_SERVICE_CONFIG;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.TMS_SERVICE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.TMS_SERVICE_CONFIG;


@Slf4j
public class CoreModule extends AbstractModule {

    private static final String MESSAGE = "message";

    @Override
    protected void configure() {
        bind(ESRepository.class).to(ESRepositoryImpl.class);
        bind(ChimeraLiteRepository.class).to(ChimeraRepositoryImpl.class);
        bind(ESConnection.class);
    }

    @Provides
    @Singleton
    @Named(CLOCKWORK_SERVICE_CONFIG)
    public HttpConfiguration providesClockWorkServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, CLOCKWORK_SERVICE);
    }

    @Provides
    @Singleton
    public ObjectMapper provideObjectMapper(Environment environment) {
        return environment.getObjectMapper();
    }

    @Provides
    public Map<CacheName, CacheConfig> provideCacheConfigs(final AppConfig config) {
        return config.getCaches();
    }

    @Provides
    @Singleton
    public StoreCheckInConfig provideStoreCheckInConfig(final AppConfig config) {
        return config.getStoreCheckInConfig();
    }

    @Provides
    @Singleton
    public GladiusConfig provideGladiusConfig(final AppConfig config) {
        return config.getGladiusConfig();
    }


    @Provides
    @Singleton
    @Named(FOXTROT_SERVICE_CONFIG)
    public HttpConfiguration getFoxtrotServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, FOXTROT_SERVICE);
    }

    @Provides
    @Singleton
    @Named(BRICKBAT_SERVICE_CONFIG)
    public HttpConfiguration getBrickbatConfig(AppConfig appConfig){
        return getConfig(appConfig, BRICKBAT_SERVICE);
    }

    @Provides
    @Singleton
    public ClientQcTaskConfig getClientQcTaskConfig(AppConfig appConfig){
        return appConfig.getClientQcTaskConfig();
    }

    @Provides
    @Singleton
    @Named(ATLAS_SERVICE_CONFIG)
    public HttpConfiguration getAtlasConfig(AppConfig appConfig){
        return getConfig(appConfig, ATLAS_SERVICE);
    }

    @Provides
    @Singleton
    @Named(SCOUT_SERVICE_CONFIG)
    public HttpConfiguration getScoutServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, SCOUT_SERVICE);
    }

    @Provides
    @Singleton
    @Named(INTEL_SERVICE_CONFIG)
    public HttpConfiguration getIntelServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, INTEL_SERVICE);
    }

    @Provides
    @Singleton
    @Named(MERCHANT_ONBOARDING_SERVICE_CONFIG)
    public HttpConfiguration getMerchantOnboardingServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, MERCHANT_ONBOARDING_SERVICE);
    }

    @Provides
    @Singleton
    @Named(MERCHANT_SERVICE_CONFIG)
    public HttpConfiguration getMerchantServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, MERCHANT_SERVICE);
    }

    @Provides
    @Singleton
    @Named(LEGION_SERVICE_CONFIG)
    public HttpConfiguration getLegionServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, LEGION_SERVICE);
    }

    @Provides
    @Singleton
    @Named(PARADOX_SERVICE_CONFIG)
    public HttpConfiguration providesParadoxServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, PARADOX_SERVICE);
    }

    @Provides
    @Singleton
    @Named(SEER_SERVICE_CONFIG)
    public HttpConfiguration provideSeerServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, SEER_SERVICE);
    }

    @Provides
    @Singleton
    @Named(ODIN_SERVICE_CONFIG)
    public HttpConfiguration providesOdinServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, ODIN_SERVICE);
    }

    @Provides
    @Singleton
    @Named(TMS_SERVICE_CONFIG)
    public HttpConfiguration providesTmsServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, TMS_SERVICE);
    }

    @Provides
    @Singleton
    @Named(FORTUNA_SERVICE_CONFIG)
    public HttpConfiguration providesFortunaServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, FORTUNA_SERVICE);
    }

    @Provides
    @Singleton
    @Named(HERMOD_SERVICE_CONFIG)
    public HttpConfiguration providesHermodServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, HERMOD_SERVICE);
    }

    @Provides
    @Singleton
    public AuditConfig getAuditConfig(AppConfig appConfig) {
        return appConfig.getAuditConfig();
    }

    @Provides
    @Singleton
    @Named(KILLSWITCH_SERVICE_CONFIG)
    public HttpConfiguration providesKillSwitchServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, KILLSWITCH_SERVICE);
    }

    @Provides
    @Singleton
    public Map<String, AttributeInfoConfig> providesAttributeInfoConfig(AppConfig appConfig) {
        return appConfig.getAttributeInfo();
    }

    @Provides
    @Singleton
    public ChimeraLiteConfig getChimeraLiteConfig(AppConfig config) {
        return config.getChimeraLiteConfig();
    }

    @Provides
    @Singleton
    public List<String> getTaskTypes(AppConfig appConfig){
        return appConfig.getTaskTypes();
    }

    @Provides
    @Singleton
    public Set<String> getWhitelistedDefinitions(AppConfig appConfig){
        return appConfig.getWhitelistedDefinitions();
    }

    @Provides
    @Singleton
    @Named(GEMINI_SERVICE_CONFIG)
    public HttpConfiguration getGeminiServiceConfig(AppConfig appConfig){
        return getConfig(appConfig, GEMINI_SERVICE);
    }

    private HttpConfiguration getConfig(AppConfig appConfig, String serviceName){
        return appConfig.getRangerHubConfiguration().getServices().stream()
                .filter(httpConfig -> serviceName.equals(httpConfig.getClientId()))
                .findAny()
                .orElseThrow(()->LegionException.error(ERROR_IN_GETTING_HTTP_CONFIGURATION, Map.of(MESSAGE, String.format("Not able to fetch http configuration for %s", serviceName))));
    }
}
