package com.phonepe.merchant.legion.core.eventingestion.models;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Builder
public class TaskStateTransitionEvent {

    private String taskInstanceId;
    private String entityType;
    private String entityId;
    private String fromTaskState;
    private String toTaskState;
    private String actionId;
    private String taskDefinitionId;
    private String campaignId;
    private Map<String, Boolean> verificationContext;
    private String merchantId;
    private String storeId;
    private String leadIntent;

}
