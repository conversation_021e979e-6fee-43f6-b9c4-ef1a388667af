package com.phonepe.merchant.legion.core.utils;

import com.phonepe.platform.eventingestion.model.Event;
import com.phonepe.merchant.legion.core.eventingestion.models.conduitevents.ConduitCallbackEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.conduitevents.ConduitJobFailureEvent;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.UUID;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CoreFoxtrotEventUtils<T, J> {

    private static final String MESSAGE  = "There is an exception trying to construct a foxtrot event for {}";

    public static Event<ConduitJobFailureEvent> conduitJobFailure(ConduitJobFailureEvent conduitJobFailureEvent) {
        try {
            final ConduitJobFailureEvent data = ConduitJobFailureEvent.builder()
                    .failureMsg(conduitJobFailureEvent.getFailureMsg())
                    .build();

            return Event.<ConduitJobFailureEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.ReportingEvents.CONDUIT_JOB_SUBMISSION_FAILURE.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(conduitJobFailureEvent.getJobId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error(MESSAGE, e);
            return null;
        }
    }

    public static Event<ConduitCallbackEvent> conduitCallback(ConduitCallbackEvent conduitCallbackEvent) {
        try {

            return Event.<ConduitCallbackEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.ReportingEvents.CONDUIT_CALLBACK_RECEIVED.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(conduitCallbackEvent)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(conduitCallbackEvent.getJobId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error(MESSAGE, e);
            return null;
        }
    }

    public static <T, J>  Event<T> getEventObject(T object, String groupingKey, J eventType) {
        try {

            return Event.<T>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(eventType.toString())
                    .id(UUID.randomUUID().toString())
                    .eventData(object)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(groupingKey)
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error("Error in creating event object - groupingKey:{} eventType:{}",groupingKey,eventType,e);
            return null;
        }
    }

}
