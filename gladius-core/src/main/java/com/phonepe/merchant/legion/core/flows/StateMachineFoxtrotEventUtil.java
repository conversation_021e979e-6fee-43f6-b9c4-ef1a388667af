package com.phonepe.merchant.legion.core.flows;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.legion.core.eventingestion.models.StateMachineTransitionFoxtrotEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.TaskCreationFailureEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.TaskStateTransitionEvent;
import com.phonepe.merchant.legion.core.flows.models.LegionStateMachineContext;
import com.phonepe.merchant.legion.core.utils.EventConstants;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.platform.eventingestion.model.Event;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.UUID;

import static com.phonepe.merchant.gladius.models.utils.GenericUtil.getMidAndSidFromEid;

@Slf4j
public class StateMachineFoxtrotEventUtil {
    private static final String STATE_MACHINE_TRANSITION_FOXTROT_EVENT = "STATE_MACHINE_TRANSITION";
    private static final String TASK_STATE_TRANSITION = "TASK_STATE_TRANSITION";
    private static final String TASK_CREATION_FAILURE_EVENT = "TASK_CREATION_FAILURE_EVENT";
    private StateMachineFoxtrotEventUtil() {
    }

    public static Event<StateMachineTransitionFoxtrotEvent> toStateMachineTransitionFoxtrotEvent(LegionStateMachineContext context) {
        try {
            String[] entityContext =  getMidAndSidFromEid(context.getEntityId());
            final StateMachineTransitionFoxtrotEvent data = StateMachineTransitionFoxtrotEvent.builder()
                    .flowType(context.getType())
                    .actionId(context.getActionId())
                    .taskInstanceId(context.getTaskInstanceId())
                    .taskDefinitionId(context.getTaskDefinitionId())
                    .campaignId(context.getCampaignId())
                    .causeEvent(context.getCausedEvent().name())
                    .curActor(context.getCurActor())
                    .entityId(context.getEntityId())
                    .entityType(context.getEntityType())
                    .from(null != context.getFrom() ? context.getFrom().name() : null)
                    .to(null != context.getTo() ? context.getTo().name() : null)
                    .merchantId(context.getEntityType().equals(EntityType.STORE.name()) ? entityContext[0] : null)
                    .storeId(context.getEntityType().equals(EntityType.STORE.name()) ? entityContext[1] : null)
                    .leadIntent(context.getLeadIntent())
                    .build();

            return Event.<StateMachineTransitionFoxtrotEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(STATE_MACHINE_TRANSITION_FOXTROT_EVENT)
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(context.getTaskInstanceId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error("There is an exception trying to construct a foxtrot event for state machine transition event for eventName", e);
            return null;
        }
    }

    public static Event<TaskStateTransitionEvent> toTaskStateTransitionFoxtrotEvent(LegionStateMachineContext context){
        String[] entityContext =  getMidAndSidFromEid(context.getEntityId());
        TaskStateTransitionEvent eventData = TaskStateTransitionEvent.builder()
                .taskInstanceId(context.getTaskInstanceId())
                .entityType(context.getEntityType())
                .entityId(context.getEntityId())
                .fromTaskState(null != context.getFrom() ? context.getFrom().name() : null)
                .toTaskState(null != context.getTo() ? context.getTo().name() : null)
                .actionId(context.getActionId())
                .taskDefinitionId(context.getTaskDefinitionId())
                .campaignId(context.getCampaignId())
                .verificationContext(context.getVerificationContext())
                .merchantId(context.getEntityType().equals(EntityType.STORE.name()) ? entityContext[0] : null)
                .storeId(context.getEntityType().equals(EntityType.STORE.name()) ? entityContext[1] : null)
                .leadIntent(context.getLeadIntent())
                .build();
        return Event.<TaskStateTransitionEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(TASK_STATE_TRANSITION)
                .id(UUID.randomUUID().toString())
                .eventData(eventData)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(context.getTaskInstanceId())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<TaskCreationFailureEvent> toTaskCreationFailureFoxtrotEvent(LegionStateMachineContext context, String errorMessage){
        CreateTaskInstanceRequest request = context.getAdditionalContextData(CreateTaskInstanceRequest.class.getSimpleName(),
                CreateTaskInstanceRequest.class);
        TaskCreationFailureEvent eventData = TaskCreationFailureEvent.builder()
                .entityId(request.getEntityId())
                .fromTaskState(null != context.getFrom() ? context.getFrom().name() : null)
                .toTaskState(null != context.getTo() ? context.getTo().name() : null)
                .requesterId(request.getCreatedBy())
                .taskDefinitionId(request.getTaskDefinitionId())
                .campaignId(request.getCampaignId())
                .message(errorMessage)
                .build();
        return Event.<TaskCreationFailureEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(TASK_CREATION_FAILURE_EVENT)
                .id(UUID.randomUUID().toString())
                .eventData(eventData)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }
}
