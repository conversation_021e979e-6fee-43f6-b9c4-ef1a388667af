package com.phonepe.merchant.legion.core.eventingestion.models;

import com.phonepe.merchant.legion.core.flows.LegionFlowType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StateMachineTransitionFoxtrotEvent {
    private LegionFlowType flowType;
    private String actionId;
    private String taskInstanceId;
    private String taskDefinitionId;
    private String campaignId;
    private String causeEvent;
    private String entityId;
    private String entityType;
    private String curActor;
    private String from;
    private String to;
    private String merchantId;
    private String storeId;
    private String leadIntent;
}
