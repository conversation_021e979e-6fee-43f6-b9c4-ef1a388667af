package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.models.ElasticSearchRequest;
import com.phonepe.merchant.legion.core.models.MerchantDetails;
import com.phonepe.merchant.legion.core.models.MerchantDetailsFilter;
import com.phonepe.merchant.legion.core.models.MerchantOnboardedByAgent;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;

import javax.ws.rs.core.UriBuilder;
import java.net.URI;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTHORIZATION;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.MERCHANT_ONBOARDING_SERVICE_CONFIG;

@Singleton
@Slf4j
public class MerchantOnboardingService extends CommunicationService {

    private static final String STORE_DOC_NOT_FOUND = "NO_RESULT_FOUND";

    @Inject
    public MerchantOnboardingService(@Named(value = MERCHANT_ONBOARDING_SERVICE_CONFIG) final HttpConfiguration httpConfiguration,
                                     final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                                     ObjectMapper mapper,
                                     MetricRegistry metricRegistry,
                                     FoxtrotEventIngestionService eventIngestionService,
                                     ServiceDiscoveryConfiguration serviceDiscoveryConfiguration) {
        super(httpConfiguration,
                HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry),
                () -> serviceEndpointProviderFactory.provider(httpConfiguration.getClientId()),
                mapper,
                eventIngestionService,
                serviceDiscoveryConfiguration);
    }

    public ElasticSearchRequest getStoreDoc(String entityId) {
        String commandName = "getStoreDoc";
        URI uri = UriBuilder.fromPath("/internal/store/" + entityId + "/doc")
                .build();

        Response response = callWithoutCheck(
                commandName,
                uri.toString(),
                null,
                CallTypes.GET,
                Headers.of(Map.of(AUTHORIZATION, AuthHeaderProviderUtil.getSystemAuthHeader()))
        );

        if (!response.isSuccessful()) {
            byte[] responseBody = body(response);
            GenericError errorResponse = SerDe.readValue(responseBody, GenericError.class);
            String responseString = responseBody != null ? new String(responseBody) : null;
            if (STORE_DOC_NOT_FOUND.equals(errorResponse.getCode())) {
                ingestServiceFailureEvent(responseString, response.code());
                return null;
            }
            handleResponseFailure(commandName, responseString, response.code());
        }

        return SerDe.readValue(bodyStr(response),
                new TypeReference<GenericResponse<ElasticSearchRequest>>() {
                }).getData();
    }

    public List<MerchantOnboardedByAgent> getMerchantsOnboardedByAgent(String agentId, long from, long to) {
        Headers headers = Headers.of(Map.of("x-external-user-id", agentId,
                AUTHORIZATION, AuthHeaderProviderUtil.getSystemAuthHeader()));
        String commandName = "getMerchantsOnboardedByAgent";
        URI uri = UriBuilder.fromPath("/v1/fos-app/merchants/onboardedby")
                .queryParam("from", from)
                .queryParam("to", to)
                .build();
        Response response = call(
                commandName,
                uri.toString(),
                null,
                CallTypes.GET,
                headers
        );
        return SerDe.readValue(bodyStr(response),
                new TypeReference<GenericResponse<List<MerchantOnboardedByAgent>>>() {
                }).getData();
    }

    public MerchantDetails getFilteredMerchantDetails(String merchantId, MerchantDetailsFilter merchantDetailsFilter) {
        String commandName = "getFilteredMerchantDetails";
        URI uri = UriBuilder.fromPath("/v2/merchants/" + merchantId)
                .build();

        Response response = call(
                commandName,
                uri.toString(),
                merchantDetailsFilter,
                CallTypes.POST,
                Headers.of(Map.of("x-device-fingerprint", "gladius",
                        AUTHORIZATION, AuthHeaderProviderUtil.getSystemAuthHeader())
        ));

        GenericResponse genericResponse = SerDe.readValue(bodyStr(response), new TypeReference<GenericResponse>() {
        });
        return SerDe.convertValue(genericResponse.getData(), MerchantDetails.class);
    }
}
