package com.phonepe.merchant.legion.core.exceptions;

import lombok.Getter;

import javax.ws.rs.core.Response;

@Getter
public enum CoreErrorCode implements ErrorCode {

    DAO_ERROR(Response.Status.BAD_REQUEST, "Error in db call"),
    FOR<PERSON><PERSON><PERSON><PERSON>(Response.Status.FORBIDDEN, "Error in accessing resource"),
    SQL_CONSTRAINT_EXCEPTION(Response.Status.BAD_REQUEST, "Error with data constraints layer"),
    TRANSITION_NOT_FOUND(Response.Status.BAD_REQUEST, "transition not found"),
    INTERNAL_ERROR(Response.Status.BAD_REQUEST, "Error in accessing resource"),
    NOT_FOUND(Response.Status.NOT_FOUND, "resource not found"),
    FEEDBACK_NOT_FOUND(Response.Status.BAD_REQUEST),
    INVALID_REQUEST(Response.Status.BAD_REQUEST),
    INDEX_DOES_NOT_EXIST(Response.Status.NOT_FOUND, "index not found"),
    JSON_ERROR(Response.Status.BAD_REQUEST, "Error in json mapping"),
    IO_ERROR(Response.Status.BAD_REQUEST),
    QUEUE_ERROR(Response.Status.BAD_REQUEST),
    COMMUNICATION_ERROR(Response.Status.BAD_REQUEST),
    CAMPAIGN_NOT_FOUND(Response.Status.NOT_FOUND, "Campaign not found"),
    TASK_DEFINITION_NOT_FOUND(Response.Status.NOT_FOUND, "Task Definition not found"),
    DUPLICATE_REQUEST(Response.Status.NOT_FOUND),
    AUTH_HEADER_MISSING(Response.Status.BAD_REQUEST, "User Auth is missing"),
    INVALID_AUTH_TOKEN(Response.Status.BAD_REQUEST),
    INVALID_AUTH_PREFIX(Response.Status.BAD_REQUEST, "Unrecognizable Auth Token Prefix"),
    INTENT_RESTRICTED_FOR_COMPLETION(Response.Status.BAD_REQUEST),
    NO_BODY_FOUND(Response.Status.BAD_REQUEST),
    NO_SECTORS_MAPPED(Response.Status.NOT_FOUND, "No sectors attached to the user"),

    INVALID_ENUM_INPUT(Response.Status.BAD_REQUEST),
    /* gandalf error codes */
    SESSION_TIME_OUT(Response.Status.BAD_REQUEST),
    TOKEN_EXPIRED(Response.Status.BAD_REQUEST),
    INVALID_CREDENTIAL(Response.Status.BAD_REQUEST),
    INACTIVE_USER(Response.Status.BAD_REQUEST),
    INVALID_TOKEN(Response.Status.BAD_REQUEST),
    USER_BLACKLISTED(Response.Status.BAD_REQUEST),
    USER_NOT_FOUND(Response.Status.BAD_REQUEST),
    ERROR_IN_GETTING_HTTP_CONFIGURATION(Response.Status.NOT_FOUND),


    /* OTP related error codes */
    BANNED(Response.Status.BAD_REQUEST, "User Banned. Please retry after sometime"),
    OTP_EXPIRED(Response.Status.BAD_REQUEST, "OTP Expired"),
    ATTEMPTS_EXHAUSTED(Response.Status.BAD_REQUEST, "Attempts Exhausted"),
    ALREADY_VERIFIED(Response.Status.BAD_REQUEST, "OTP Already Verified"),
    INVALID_OTP(Response.Status.BAD_REQUEST, "Incorrect OTP, Please Re-enter OTP"),

    TASK_ATTRIBUTE_ABSENT(Response.Status.BAD_REQUEST),
    TASK_DEFINITION_ATTRIBUTE_ABSENT(Response.Status.BAD_REQUEST),
    TASK_DEFINITION_ATTRIBUTE_DETAILS_ABSENT(Response.Status.BAD_REQUEST),

    USER_ID_NOT_FOUND(Response.Status.BAD_REQUEST),

    /*  Cache Utils */
    INVALID_CACHE(Response.Status.INTERNAL_SERVER_ERROR, " Cache does not exist"),
    ERROR_IN_CREATING_SERVICE_PROVIDER(Response.Status.INTERNAL_SERVER_ERROR, "Not able to create service provider check config"),
    COMMENTS_ADDITION_LIMIT_EXHAUSTED(Response.Status.INTERNAL_SERVER_ERROR, "Comments addition limit exhausted"),
    FAILED_TO_FETCH_ACTION_DETAILS(Response.Status.INTERNAL_SERVER_ERROR);

    private Response.Status code;
    private String message;

    CoreErrorCode(Response.Status code) {
        this.code = code;
        this.message = "";
    }

    CoreErrorCode(Response.Status code, String message) {
        this.code = code;
        this.message = message;
    }
}
