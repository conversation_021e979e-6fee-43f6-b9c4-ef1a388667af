

| Created | By [<EMAIL>](https://frontpage.phonepe.com/people/gaurav.kumar7) at 9 Sep 2025 21:22 |
| :---- | :---- |
| RFC | [https://rfc.phonepe.com/details/c4413ca7-7f84-4fd5-91df-393bb40c7cb4](https://rfc.phonepe.com/details/c4413ca7-7f84-4fd5-91df-393bb40c7cb4) |
| Type | Engineering requirements document in Merchant |

## Table of Contents {#table-of-contents}

[**Table of Contents	1**](#table-of-contents)

[**Summary	1**](#heading=h.e78ghv8fgeju)

[**Overview	1**](#heading=h.an4fgwjheyrv)

[**Features & Deliverables	1**](#heading=h.2mhxzpiwj5np)

[**System Components	2**](#heading=h.vvdjg2p8rps6)

[**Physical Infrastructure	2**](#heading=h.8jvehe7wqwq8)

[**Software Infrastructure	2**](#heading=h.fyy19osrj520)

[**Goals	2**](#heading=h.8snhny74324)

[**Specification	2**](#heading=h.bmew2plhqdch)

[Features	2](#heading=h.6fjruigw3iaa)

[Use Cases	2](#heading=h.wbwfmx956jwj)

[Processes	2](#heading=h.fr2um1uypy5m)

[Operations	2](#heading=h.ary770pl321d)

[SOP	3](#heading=h.idjvncxn8y50)

###  **eSummary**

Transaction-level data is essential for the collection of processing fees on insurance. This is a mandatory requirement from the merchant's side, as they will not process the fees without this data. Additionally, the transaction-level data must align with the P0 (Summary Report). The report is required to be generated and delivered on a monthly basis.

---

### **Overview**

This document describes the capability to generate on-demand transaction reports for GST invoices, applicable to both Net Settlement and Gross Settlement.

---

### **Features & Deliverables**

1. Download Button for GST Invoices:  
   A button will be available on PB Web to download GST invoices.  
2. Scope:  
   * This feature is applicable to merchants only.  
   * Super merchants are out of scope.  
3. Download Timing:  
   The download button will be enabled 6 hours after the invoice is generated.  
4. Error Handling:  
   * If a mismatch in transaction count occurs due to incomplete ingestion, the report generation will fail.  
   * Users can retry only after the failure of the previous report request.  
5. Data Retention:  
   Users can generate GST invoice transaction reports for the last 6 months only.

---

### **System Components**

1. Merchant Insights  
2. Scribe  
3. Tstore Lucy Ingestion  
4. Panther  
5. PB Web

---

### **API Contracts**

#### **PB Web**

##### **Generate GST Transaction Reports**

Request:

```shell
curl -X 'POST' \
  'http://merchant-insights.nixy.stg-drove.phonepe.nb6/v2/reports/gstInvoice/generate' \
  -H 'accept: application/json' \
  -H 'Authorization: <>' \
  -H 'Content-Type: application/json' \
  -d '{
  "invoiceNo": "P25261000865",
  "merchantId": "M101NDFXHEXH7C"
}'
```

Responses:

1. Success:  
   * Status: 200  
   * Response Body:

```json
{
  "status": "TO_BE_STARTED",
  "jobDetails": {
    "jobId": "f8a79747-b298-4ff2-ae6e-6214f2276903",
    "templateId": "GST_INVOICE_TRANSACTION_REPORT_DEFAULT"
  }
}
```

2. Invalid Invoice Number:  
   * Status: 400  
   * Response Body:

```json
{
  "code": "INVALID_INVOICE_NUMBER",
  "message": "Invalid invoice number",
  "context": {}
}
```

3. Merchant ID Validation Failed:  
   * Status: 400  
   * Response Body:

```json
{
  "code": "MERCHANT_ID_VALIDATION_FAILED",
  "message": "Invalid merchant query",
  "context": {}
}
```

---

##### **Get GST Transaction Reports**

Request:

```shell
curl -X 'POST' \
  'http://merchant-insights.nixy.stg-drove.phonepe.nb6/v2/reports/gstInvoice/info' \
  -H 'accept: application/json' \
  -H 'Authorization: <>' \
  -H 'Content-Type: application/json' \
  -d '{
  "invoiceNo": "P25261000865",
  "merchantId": "M101NDFXHEXH7C"
}'
```

Responses:

1. No Older Job Exists:  
   * Status: 200  
   * Response Body:

```json
{
  "invoiceNo": "P25261000865",
  "olderJobExists": true,
  "invoiceDate": 1756578600000,
  "transactionCount": 1
}
```

2. Failed Job (Retry Needed): txn count mismatch  
   * Status: 200  
   * Response Body :

```json
{
  "scribeReportInfo": {
    "status": "FAILED",
    "reportContext": {
      "retryable": false
    }
  },
  "olderJobExists": true
}
```

3. Successful Job (Download Available):  
   * Status: 200  
   * Response Body:

```json
{
  "scribeReportInfo": {
    "status": "SUCCESS",
    "reportContext": {
      "docStoreId": "abc",
      "retryable": false
    }
  },
  "olderJobExists": true
}
```

4. Invoice not found:  
   * Status: 200  
   * Response Body:

```json
{
  "olderJobExists": false
}
```

---

#### **SCP API Contracts**

##### **Generate GST Transaction Reports**

Request:

```shell
curl -X 'POST' \
  'http://merchant-insights.nixy.stg-drove.phonepe.nb6/v2/reports/internal/gstInvoice/generate' \
  -H 'accept: application/json' \
  -H 'Authorization: <>' \
  -H 'Content-Type: application/json' \
  -d '{
  "invoiceNo": "P25261000865",
  "merchantId": "M101NDFXHEXH7C"
}'
```

Responses:

1. Success:  
   * Status: 200  
   * Response Body:

```json
{
  "status": "TO_BE_STARTED",
  "jobDetails": {
    "jobId": "f8a79747-b298-4ff2-ae6e-6214f2276903",
    "templateId": "GST_INVOICE_TRANSACTION_REPORT_DEFAULT"
  }
}
```

2. Invalid Invoice Number:  
   * Status: 400  
   * Response Body:

```json
{
  "code": "INVALID_INVOICE_NUMBER",
  "message": "Invalid invoice number",
  "context": {}
}
```

3. Merchant ID Validation Failed:  
   * Status: 400  
   * Response Body:

```json
{
  "code": "MERCHANT_ID_VALIDATION_FAILED",
  "message": "Invalid merchant query",
  "context": {}
}
```

---

##### **Get GST Transaction Reports**

Request:

```shell
curl -X 'POST' \
  'http://merchant-insights.nixy.stg-drove.phonepe.nb6/v2/reports/internal/gstInvoice/info' \
  -H 'accept: application/json' \
  -H 'Authorization: <>' \
  -H 'Content-Type: application/json' \
  -d '{
  "invoiceNo": "P25261000865",
  "merchantId": "M101NDFXHEXH7C"
}'
```

Responses:

1. No Older Job Exists:  
   * Status: 200  
   * Response Body:

```json
{
  "invoiceNo": "P25261000865",
  "olderJobExists": true,
  "invoiceDate": 1756578600000,
  "transactionCount": 1
}
```

2. Failed Job (Retry Needed):  
   * Status: 200  
   * Response Body:

```json
{
  "scribeReportInfo": {
    "status": "FAILED",
    "reportContext": {
      "retryable": false
    }
  },
  "olderJobExists": true
}
```

3. Successful Job (Download Available):  
   * Status: 200  
   * Response Body:

```json
{
  "scribeReportInfo": {
    "status": "SUCCESS",
    "reportContext": {
      "docStoreId": "abc",
      "retryable": false
    }
  },
  "olderJobExists": true
}
```

5\. Invoice not found:

* Status: 200  
  * Response Body:

```json
{
  "olderJobExists": false
}
```

### 11 Sept, 2025 | MOM

1. All GST reports on the settlements tab will open in a modal and will use the old v1 APIs.  
2. Visibility \-  GST Invoice reports listing under the reports tab would not be visible to the supermerchant.  
3. There would be at most 2 GST invoice reports generated per month. We (at the front end) will call the status for those reports once the list is fetched.  
4. There would be a month picker in the GST Invoice reports listing under the reports tab  
   