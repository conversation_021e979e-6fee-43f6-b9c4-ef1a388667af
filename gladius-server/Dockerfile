FROM docker.phonepe.com/core/ubuntu/jammy/jdk/openjdk/17
LABEL maintainer="<EMAIL>"

EXPOSE 8080
EXPOSE 8081
EXPOSE 8010

VOLUME /var/log/gladius

ADD target/gladius-server.jar gladius.jar


CMD DNS_HOST=`ip r | awk '/default/{print $3}'` && printf "nameserver $DNS_HOST\n" > /etc/resolv.conf && sh -c "java -jar -Dcom.sun.management.jmxremote.local.only=false -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -XX:+${GC_ALGO-UseG1GC} -Xms${JAVA_PROCESS_MIN_HEAP-1g} -Xmx${JAVA_PROCESS_MAX_HEAP-1g} -Ddb.shards=${SHARDS} ${JAVA_OPTS} gladius.jar server /rosey/config.yml"
