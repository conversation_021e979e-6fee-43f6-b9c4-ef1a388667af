{"assignedFilterOptions": {"filters": [{"displayText": "Task Status", "groupingOperator": "OR", "multipleSelect": false, "options": [{"key": "BOUNDED_ASSIGNED", "displayText": "Pending", "field": "task_state", "operator": "EQUALS"}, {"key": "SELF_ASSIGNED", "displayText": "Self Assigned", "field": "task_state", "operator": "EQUALS"}, {"key": "COMPLETED", "displayText": "In Review", "field": "task_state", "operator": "EQUALS"}, {"key": "VERIFICATION_FAILED", "displayText": "Rejected", "field": "task_state", "operator": "EQUALS"}, {"key": "VERIFICATION_SUCCESS", "displayText": "Approved", "field": "task_state", "operator": "EQUALS"}]}, {"displayText": "Lead Status", "groupingOperator": "OR", "multipleSelect": true, "options": [{"key": "HOT", "displayText": "Hot", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "WARM", "displayText": "Warm", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "COLD", "displayText": "Cold", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "NOT_INTERESTED", "displayText": "Not Interested", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "TASK_DONE", "displayText": "Task Done", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "LEAD_INTENT", "displayText": "No Intent", "operator": "NOT_EQUALS", "field": "task_metadata.taskMetaList.type.keyword"}]}]}, "discoveryFilterOptions": {"filters": [{"displayText": "Lead Status", "groupingOperator": "OR", "multipleSelect": true, "options": [{"key": "HOT", "displayText": "Hot", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "WARM", "displayText": "Warm", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "COLD", "displayText": "Cold", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "NOT_INTERESTED", "displayText": "Not Interested", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "TASK_DONE", "displayText": "Task Done", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "LEAD_INTENT", "displayText": "No Intent", "operator": "NOT_EQUALS", "field": "task_metadata.taskMetaList.type.keyword"}]}]}, "leadFilterOptions": {"filters": [{"displayText": "Task Status", "groupingOperator": "OR", "multipleSelect": false, "options": [{"key": "BOUNDED_ASSIGNED", "displayText": "Pending", "field": "task_state", "operator": "EQUALS"}, {"key": "SELF_ASSIGNED", "displayText": "Self Assigned", "field": "task_state", "operator": "EQUALS"}, {"key": "COMPLETED", "displayText": "In Review", "field": "task_state", "operator": "EQUALS"}, {"key": "VERIFICATION_FAILED", "displayText": "Rejected", "field": "task_state", "operator": "EQUALS"}, {"key": "VERIFICATION_SUCCESS", "displayText": "Approved", "field": "task_state", "operator": "EQUALS"}]}, {"displayText": "Lead Status", "groupingOperator": "OR", "multipleSelect": true, "options": [{"key": "HOT", "displayText": "Hot", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "WARM", "displayText": "Warm", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "COLD", "displayText": "Cold", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "NOT_INTERESTED", "displayText": "Not Interested", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "TASK_DONE", "displayText": "Task Done", "field": "task_metadata.taskMetaList.value.keyword", "operator": "EQUALS"}, {"key": "LEAD_INTENT", "displayText": "No Intent", "operator": "NOT_EQUALS", "field": "task_metadata.taskMetaList.type.keyword"}]}]}}