package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.SysAsyncVerificationStrategy;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.ActionVerificationProcessorImpl;
import com.phonepe.merchant.legion.tasks.actions.verifiers.NoActionVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.OqcValidationVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.StoreCheckInVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.TaskQcVerifier;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskVerificationServiceImpl;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.NO_ACTION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.OQC_VALIDATION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_CHECK_IN_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_QC_VERIFIER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskVerificationServiceTest extends LegionTaskBaseTest {

    private static TaskVerificationService taskVerificationService;
    private static TaskActionService taskActionService;

    public TaskVerificationServiceTest() {
        Map<String, ActionVerifier> verifiers = new HashMap<>();
        verifiers.put(NO_ACTION_VERIFIER, new NoActionVerifier());
        verifiers.put(STORE_CHECK_IN_VERIFIER, new StoreCheckInVerifier(merchantOnboardingService));
        verifiers.put(OQC_VALIDATION_VERIFIER, new OqcValidationVerifier(merchantOnboardingService));
        verifiers.put(TASK_QC_VERIFIER, new TaskQcVerifier());
        taskActionService = mock(TaskActionServiceImpl.class);
        actionVerificationProcessor = new ActionVerificationProcessorImpl(verifiers, taskTransitionRepository, taskActionService, mock(FoxtrotEventIngestionService.class));
        taskVerificationService = new TaskVerificationServiceImpl(clockWorkService, actionVerificationProcessor,
                taskActionService, eventExecutor);
    }

    @Test(expected = Test.None.class)
    public void testScheduleTaskVerificationTest() {
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT")
                .taskDefinitionId("taskDefintionID")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.CREATED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .build();

    when(taskActionService.getFromCache(any()))
        .thenReturn(
            TaskActionInstance.builder()
                .verificationStrategy(SysAsyncVerificationStrategy.builder()
                        .verificationDelay(1_005)
                        .build())
                .build());

    StoredTaskInstance schedule =
        taskVerificationService.schedule(storedTaskInstance, TaskCompleteRequest.builder()
                .completedBy("suraj")
                .isForced(false)
                .taskInstanceId("TI12312132131")
                .build());

    }
}
