package com.phonepe.merchant.legion.tasks.processors;

import com.google.inject.Injector;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.validation.AndOperationContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.LeafContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationStrategy;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.AgentLocationValidatorConfig;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.AssignmentActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.validators.AgentLocationValidator;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.mock;
public class AssignmentActionValidationProcessorTest extends LegionTaskBaseTest{

    @Test
    public void testAssignActionConstructor() {
        Injector injector = mock(Injector.class);
        ESRepository esRepository = Mockito.mock(ESRepository.class);
        AgentLocationValidator<TaskAssignRequest> locationValidator = new AgentLocationValidator<>(foxtrotEventIngestionService, esRepository);
        Mockito.when(injector.getInstance(Mockito.any(Class.class))).thenReturn(locationValidator);
        assignActionValidationProcessor1 = new AssignmentActionValidationProcessor(injector, taskActionService, foxtrotEventIngestionService);
        Assertions.assertTrue(true);
    }

    @Test
    public void testAssignActionConstructor1() {
        assignActionValidationProcessor1 = new AssignmentActionValidationProcessor(Collections.emptyMap(), taskActionService, foxtrotEventIngestionService);
        Assertions.assertTrue(true);
    }

    @Test
    public void testGetValidationContainer() {
        List<ValidationContainer> validationContainers = new ArrayList<>();
        validationContainers.add(new LeafContainer(new AgentLocationValidatorConfig()));
        ValidationContainer validationContainer = new AndOperationContainer(validationContainers);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(validationContainer);
        assignActionValidationProcessor1 = new AssignmentActionValidationProcessor(Collections.emptyMap(), taskActionService, foxtrotEventIngestionService);
        Assertions.assertNotNull(assignActionValidationProcessor1.getTaskValidationContainer(TaskActionInstance.builder().assignTaskValidationStrategy(validationStrategy).build()));
    }

}
