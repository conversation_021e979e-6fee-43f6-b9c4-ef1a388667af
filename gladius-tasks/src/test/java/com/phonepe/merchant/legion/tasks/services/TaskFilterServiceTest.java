package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoverySectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.TaskTestUtils;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import com.phonepe.merchant.legion.tasks.resources.TaskDiscoveryResourceV2;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.AssignedTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.DiscoveryTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.EscalationViewTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.SectorAssignedTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.SectorDiscoveryTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.TaskFilterRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateAssignedViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateDiscoveryViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateLeadViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.RequestViewWiseFilterFactory;
import com.phonepe.merchant.legion.tasks.services.impl.TaskFilterServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import edu.emory.mathcs.backport.java.util.Arrays;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.joda.time.LocalDate;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskInstanceList;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasks;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasksV2;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.STEP_KEY_NAME;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getDueDateFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getStartDateFilter;
import static com.phonepe.models.merchants.tasks.EntityType.STORE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;


public class TaskFilterServiceTest extends LegionTaskBaseTest {
    private static ESRepository esRepository;
    private static TaskFilterService taskFilterService = mock(TaskFilterService.class);
    private static SearchResponse searchResponse ;
    private static TaskFilterRequestQueryBuilderFactory requestFilterGenerator = mock(TaskFilterRequestQueryBuilderFactory.class);
    private static TaskFilterRequestQueryBuilderFactory requestFilterGeneratorMocked = mock(TaskFilterRequestQueryBuilderFactory.class);
    private static RequestViewWiseFilterFactory requestViewWiseFilterFactory = mock(RequestViewWiseFilterFactory.class);
    private static RequestViewWiseFilterFactory requestViewWiseFilterFactoryMocked = mock(RequestViewWiseFilterFactory.class);
    private static AssignedTaskFilterRequestQueryBuilder assignedViewFilterGenerator;
    private static DiscoveryTaskFilterRequestQueryBuilder discoveryViewFilterGenerator;
    private static SectorAssignedTaskFilterRequestQueryBuilder sectorAssignedViewFilterGenerator;
    private static SectorDiscoveryTaskFilterRequestQueryBuilder sectorDiscoveryViewFilterGenerator;
    private static EscalationViewTaskFilterRequestQueryBuilder escalationViewTaskFilterRequestQueryBuilder;
    private static HotspotRequestQueryBuilder hotspotRequestQueryBuilder;
    private static GenerateAssignedViewWiseFilters generateAssignedViewWiseFilters = mock(GenerateAssignedViewWiseFilters.class);
    private static GenerateDiscoveryViewWiseFilters generateDiscoveryViewWiseFilters = mock(GenerateDiscoveryViewWiseFilters.class);
    private static TaskEsUtils taskEsUtils = mock(TaskEsUtils.class);
    private static final String AGENT_ID = "AGENT_1";
    private static final String FILTERS_JSON_FILE = "staticFilter.json";
    public static TaskDiscoveryResourceV2 taskDiscoveryResourceV2 = mock(TaskDiscoveryResourceV2.class);
    private static DiscoveryLocationTaskFilterRequest discoveryLocationTaskFilterRequest = mock(DiscoveryLocationTaskFilterRequest.class);
    private static TaskFilters taskFilters;
    private static TaskFiltersV2 taskFiltersV2;
    private static final String ACTOR = "actor";
    private static final String ENTITY_ID = "entityId";

    @BeforeClass
    public static void init() {
        chimeraLiteRepository = mock(ChimeraLiteRepository.class);
        taskFilters = getFiltersOnTasks();
        taskFiltersV2 = getFiltersOnTasksV2();
        searchResponse = mock(SearchResponse.class);
        requestViewWiseFilterFactoryMocked = mock(RequestViewWiseFilterFactory.class);
        taskActionRepository = mock(TaskActionRepository.class);
        esRepository = mock(ESRepository.class);
        taskFilterService = mock(TaskFilterService.class);
        taskAttributeService = mock(TaskAttributeService.class);
        assignedViewFilterGenerator = mock(AssignedTaskFilterRequestQueryBuilder.class);
        discoveryViewFilterGenerator = mock(DiscoveryTaskFilterRequestQueryBuilder.class);
        sectorAssignedViewFilterGenerator = mock(SectorAssignedTaskFilterRequestQueryBuilder.class);
        sectorDiscoveryViewFilterGenerator = mock(SectorDiscoveryTaskFilterRequestQueryBuilder.class);
        escalationViewTaskFilterRequestQueryBuilder = mock(EscalationViewTaskFilterRequestQueryBuilder.class);
        hotspotRequestQueryBuilder  = mock(HotspotRequestQueryBuilder.class);
        requestViewWiseFilterFactory = new RequestViewWiseFilterFactory(new GenerateAssignedViewWiseFilters(), new GenerateDiscoveryViewWiseFilters(), new GenerateLeadViewWiseFilters());
        requestFilterGenerator = new TaskFilterRequestQueryBuilderFactory(assignedViewFilterGenerator, discoveryViewFilterGenerator,sectorAssignedViewFilterGenerator, sectorDiscoveryViewFilterGenerator,escalationViewTaskFilterRequestQueryBuilder, hotspotRequestQueryBuilder, null);

        taskFilterService = new TaskFilterServiceImpl(taskActionRepository, esRepository, taskFilters, taskFiltersV2, requestFilterGenerator, requestViewWiseFilterFactory, taskAttributeService);
        taskDiscoveryResourceV2 = new TaskDiscoveryResourceV2(taskFilterService);
    }

    @Test
    public void getTaskFilterOptionsTest() {
        ViewWiseFilters expectedResponse = TaskTestUtils.getFiltersOnTasks().getAssignedFilterOptions();
        expectedResponse.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedResponse.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        AssignedLocationTaskFilterRequest request = AssignedLocationTaskFilterRequest.builder().build();
        request.setLocation(EsLocationRequest.builder().lon(0.0).lat(0.0).build());
        request.setTaskSearchRequestType(TaskSearchRequestType.ASSIGNED_VIEW);
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        filters.add(getStartDateFilter());
        filters.add(getDueDateFilter());
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, "agent"));
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        Mockito.when(requestFilterGenerator.enrichFilter("actor", request)).thenReturn(boolQueryBuilder);
        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        SearchResponse searchResponse1 = TaskTestUtils.getEsSearchResponse(tasks, false);
        Mockito.when(esRepository.getAggregationResult(anyString(),any(), any())).thenReturn(searchResponse1);
        ViewWiseFilters actualFilters = taskFilterService.getTaskFilterOptions("agent", request);
        Assertions.assertEquals(actualFilters, expectedResponse);
    }

    @Test
    public void getTaskFilterOptionsTestV2() {
        requestViewWiseFilterFactoryMocked = Mockito.mock(RequestViewWiseFilterFactory.class);
        ViewWiseFiltersV2 expectedResponse = TaskTestUtils.getFiltersOnTasksV2().getAssignedFilterOptions();
        expectedResponse.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedResponse.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        AssignedLocationTaskFilterRequest request = AssignedLocationTaskFilterRequest.builder().build();
        request.setLocation(EsLocationRequest.builder().lon(0.0).lat(0.0).build());
        request.setTaskSearchRequestType(TaskSearchRequestType.ASSIGNED_VIEW);
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        filters.add(getStartDateFilter());
        filters.add(getDueDateFilter());
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, "agent"));
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        Mockito.when(requestFilterGenerator.enrichFilter("actor", request)).thenReturn(boolQueryBuilder);
        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        SearchResponse searchResponse1 = TaskTestUtils.getEsSearchResponse(tasks, false);
        Mockito.when(chimeraLiteRepository.getStaticFiltersV2()).thenReturn(taskFiltersV2);
        Mockito.when(esRepository.getAggregationResult(anyString(),any(), any())).thenReturn(searchResponse1);
        ViewWiseFiltersV2 actualFilters = taskFilterService.getTaskFilterOptionsV2("agent", request);
        Assertions.assertEquals(actualFilters, expectedResponse);
    }

    private Aggregations getPointsAggregation() {
        Aggregations aggregations = Mockito.mock(Aggregations.class);

        ParsedLongTerms parsedLongTerms = Mockito.mock(ParsedLongTerms.class);
        ParsedStringTerms parsedStringTerms = Mockito.mock(ParsedStringTerms.class);

        Terms.Bucket bucket = Mockito.mock(Terms.Bucket.class);
        Mockito.when(bucket.getKeyAsNumber()).thenReturn(10L);

        Terms.Bucket bucket2 = Mockito.mock(Terms.Bucket.class);
        Mockito.when(bucket2.getKeyAsString()).thenReturn("ACTION_1");

        Terms.Bucket bucket3 = Mockito.mock(Terms.Bucket.class);
        Mockito.when(bucket3.getKeyAsString()).thenReturn("OBJ_1");

        Terms.Bucket bucket4 = Mockito.mock(Terms.Bucket.class);
        Mockito.when(bucket4.getKeyAsString()).thenReturn("LEAD_1");

        Mockito.when(parsedLongTerms.getBuckets()).thenReturn(Arrays.asList(new Terms.Bucket[]{bucket}));
        Mockito.when(parsedStringTerms.getBuckets()).thenReturn(Arrays.asList(new Terms.Bucket[]{bucket2, bucket3, bucket4}));

        Mockito.when(aggregations.getAsMap()).thenReturn(Map.of("points", parsedLongTerms, "action_id", parsedStringTerms, "attributes", parsedStringTerms, "lead_intent_agg", parsedStringTerms));

        return aggregations;
    }

    @Test
    public void getTaskFilterOptionsDiscoveryTestV2() {
        requestViewWiseFilterFactoryMocked = Mockito.mock(RequestViewWiseFilterFactory.class);
        DiscoverySectorTaskFilterRequest request = DiscoverySectorTaskFilterRequest.builder().build();
        request.setSectorId("Sector");
        request.setTaskSearchRequestType(TaskSearchRequestType.SECTOR_DISCOVERY_VIEW);
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        filters.add(getStartDateFilter());
        filters.add(getDueDateFilter());
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, "agent"));
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        Mockito.when(requestFilterGenerator.enrichFilter("actor", request)).thenReturn(boolQueryBuilder);
        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        StoredTaskAction storedTaskAction = StoredTaskAction.builder().description("action").build();
        Mockito.when(taskActionRepository.get(any())).thenReturn(Optional.of(storedTaskAction));
        Mockito.when(chimeraLiteRepository.getStaticFiltersV2()).thenReturn(taskFiltersV2);
        TaskAttributeInstance taskAttributeInstance = TaskAttributeInstance.builder().name("objective").build();
        Mockito.when(taskAttributeService.getFromCache(Mockito.any())).thenReturn(taskAttributeInstance);
        SearchResponse searchResponse1 = TaskTestUtils.getEsSearchResponseWithAggregation(tasks, false, getPointsAggregation());
        Mockito.when(esRepository.getAggregationResult(anyString(),any(), any())).thenReturn(searchResponse1);
        ViewWiseFiltersV2 actualFilters = taskFilterService.getTaskFilterOptionsV2("agent", request);
        Assertions.assertNotNull(actualFilters);
    }

    private List<DiscoveryTaskInstance> getAgentListingTestTasks() {
        List<DiscoveryTaskInstance> tasks = new ArrayList<>();

        EnumSet.allOf(EntityType.class).stream().forEach(entityType -> {
            String actionId = IdGenerator.generate("A").getId();
            String entityId = IdGenerator.generate("E").getId();

            List<Object> actionSubSteps = new ArrayList<>();
            actionSubSteps.add(Map.of(STEP_KEY_NAME, "2"));
            List<Object> taskDefSubSteps = new ArrayList<>();
            taskDefSubSteps.add(Map.of(STEP_KEY_NAME, "1"));
            taskDefSubSteps.add(Map.of(STEP_KEY_NAME, "3"));
            List<Object> expectedOverallSubsteps = new ArrayList<>();
            expectedOverallSubsteps.add(Map.of(STEP_KEY_NAME, "1"));
            expectedOverallSubsteps.add(Map.of(STEP_KEY_NAME, "2"));
            expectedOverallSubsteps.add(Map.of(STEP_KEY_NAME, "3"));
            String taskDefDescription = "task definition description";
            List<DiscoveryTaskInstance> tempTasks = getDiscoveryTaskInstanceList(LegionTaskStateMachineState.COMPLETED, 10);

            tempTasks.forEach(taskInstance -> {
                taskDefinitionRepository.save(StoredTaskDefinition.builder()
                        .namespace(taskInstance.getNamespace())
                        .name("name")
                        .createdBy("someone")
                        .points(taskInstance.getPoints().intValue())
                        .actionId(taskInstance.getActionId())
                        .priority(Priority.P1)
                        .taskDefinitionId(taskInstance.getTaskDefinitionId())
                        .taskDefinitionMeta(SerDe.writeValueAsBytes(
                                TaskDefinitionMeta.builder()
                                        .description(taskDefDescription)
                                        .substeps(taskDefSubSteps)
                                        .build()
                        ))
                        .build());
                taskInstance.setEntityId(entityId);
                taskInstance.setEntityType(STORE);
                taskInstance.setAssignedTo(ACTOR);
                taskInstance.setActionId(actionId);
            });

            tasks.addAll(tempTasks);
        });

        return tasks;
    }
}