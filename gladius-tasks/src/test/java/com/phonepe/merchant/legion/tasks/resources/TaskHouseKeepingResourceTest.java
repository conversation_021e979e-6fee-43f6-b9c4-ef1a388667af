package com.phonepe.merchant.legion.tasks.resources;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.phonepe.gandalf.models.authn.UserType;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotSyncRequest;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotAccessDetailsResponse;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotDto;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.BulkSectorUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.geopolygon.GeoFenceRemappingSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.EsDocScrollResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.FoxtrotService;
import com.phonepe.merchant.legion.core.services.HermodService;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskEsDirectUpdateMessage;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.services.HotspotService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskFilterService;
import com.phonepe.merchant.legion.tasks.services.TaskHousekeepingService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.atlas.model.fence.Polygon;
import com.phonepe.platform.atlas.model.fence.Shape;
import com.phonepe.platform.atlas.model.fence.ShapeType;
import lombok.extern.slf4j.Slf4j;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@Slf4j
public class TaskHouseKeepingResourceTest {

    private static TaskHouseKeepingResource taskHouseKeepingResource;
    private static TaskESRepository taskEsRepository;
    private static TaskDiscoveryService taskDiscoveryService;
    private static TaskHousekeepingService taskHousekeepingService;
    private static TaskDefinitionService taskDefinitionService;
    private static MockedStatic<TaskActionMessagePublisher> taskActionMessagePublisherMockedStatic;
    private static TaskFilterService taskFilterService;

    private static TaskInstanceManagementService taskInstanceManagementService;

    private static AtlasService atlasService;
    private static HotspotService hotspotService;


    @BeforeClass
    public static void init() {
        taskActionMessagePublisherMockedStatic = mockStatic(TaskActionMessagePublisher.class);
        taskEsRepository = mock(TaskESRepository.class);
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        taskHousekeepingService = mock(TaskHousekeepingService.class);
        taskDefinitionService = mock(TaskDefinitionService.class);
        taskInstanceManagementService = mock(TaskInstanceManagementService.class);
        atlasService = mock(AtlasService.class);
        hotspotService = mock(HotspotService.class);
        taskHouseKeepingResource = new TaskHouseKeepingResource(
                taskDiscoveryService,
                mock(FoxtrotService.class),
                taskHousekeepingService,
                taskDefinitionService,
                taskFilterService,
                mock(HermodService.class),
                taskInstanceManagementService,
                mock(IntelService.class),
                atlasService,
                hotspotService);
    }

    @AfterClass
    public static void tearDownClass() {
        taskActionMessagePublisherMockedStatic.close();
        taskActionMessagePublisherMockedStatic = null;
    }

    @Test
    public void syncWithDb() {
        //arrange
        String taskInstanceId = "TASK_INSTANCE_ID";
        doNothing().when(taskEsRepository).syncWithDB(eq(taskInstanceId), anyBoolean());

        //call
        GenericResponse actualResponse = taskHouseKeepingResource.syncWithDb(Optional.empty(), taskInstanceId);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
    }

    @Test
    public void fetchSector(){
        when(atlasService.getSectorIdByLatLong(anyDouble(), anyDouble())).thenReturn(List.of("Sector1", "Sector2"));
        GenericResponse<List<String>> response = taskHouseKeepingResource.getSector(Optional.empty(), null, 17.6, 76.545);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getData().contains("Sector1"));
        Assert.assertTrue(response.getData().contains("Sector2"));

    }

    @Test
    public void fetchSectorBySectorId(){
        when(atlasService.getSectorCoordinates(anyString())).thenReturn(Polygon.builder().build());
        GenericResponse<Shape> response = taskHouseKeepingResource.getSectorDetailsFromSectorId(null,"testing");
        Assert.assertTrue(response.isSuccess());
        Assert.assertEquals(ShapeType.Polygon, response.getData().getType());
    }


    @Test
    public void update() throws JsonProcessingException {
        //arrange
        String taskInstanceId = "TASK_INSTANCE_ID";
        String data = "{data: \"Task Discovery Instance\"}";
        TaskEsDirectUpdateMessage request = new TaskEsDirectUpdateMessage(taskInstanceId, data);
        when(TaskActionMessagePublisher.taskEsUpdateRetry(request)).thenReturn(true);

        //call
        GenericResponse<String> actualResponse = taskHouseKeepingResource.update(Optional.empty(), taskInstanceId, data);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
    }

    @Test
    public void getTask() {
        //arrange
        String taskInstanceId = "TASK_INSTANCE_ID";
        TaskByIdRequest taskByIdRequest = TaskByIdRequest.builder()
                .taskInstanceId(taskInstanceId)
                .build();
        DiscoveryTaskInstance expectedResponse = DiscoveryTaskInstance.builder().build();
        when(taskDiscoveryService.getById(taskByIdRequest)).thenReturn(expectedResponse);

        //call
        GenericResponse<DiscoveryTaskInstance> actualResponse = taskHouseKeepingResource.getTask(Optional.empty(), taskInstanceId);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse, actualResponse.getData());
    }

    @Test
    public void getTaskListing() {
        //arrange
        TaskListRequest taskListRequest = TaskListRequest.builder().build();
        TaskListResponse expectedResponse = TaskListResponse.builder().build();
        when(taskDiscoveryService.getTaskListing(taskListRequest)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskListResponse> actualResponse = taskHouseKeepingResource.getTaskListing(Optional.empty(), taskListRequest);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse, actualResponse.getData());
    }



    @Test
    public void bulkSectorUpdate() {
        //arrange
        List<String> sectorIds = new ArrayList<>();
       when(taskHousekeepingService.updateSectorAgainstSectorId("sectorId",0L)).thenReturn(true);

        //call
        GenericResponse<Boolean> actualResponse = taskHouseKeepingResource.bulkSectorUpdate(Optional.empty(), "sectorId", 0L);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
    }


    @Test
    public void fetchByActionId() {
        List<TaskDefinitionInstance> taskDefinitionInstances = new ArrayList<>();
        doReturn(taskDefinitionInstances).when(taskDefinitionService).fetchByActionId(Mockito.any());

        GenericResponse<List<TaskDefinitionInstance>> actualResponse = taskHouseKeepingResource.fetchByActionId(Optional.empty(), "ACTION_ID");

        Assert.assertTrue(actualResponse.isSuccess());
    }

    @Test
    public void searchTasks() {
        //Arrange
        UserDetails userDetails = getUserDetails();
        AssignedViewTaskFetchRequest taskSearchRequest = new AssignedViewTaskFetchRequest(userDetails.getExternalReferenceId(), false, null, null);
        taskSearchRequest.setPageNo(1);
        taskSearchRequest.setPageSize(10);
        taskSearchRequest.setLocation(EsLocationRequest.builder()
                .lon(0.0)
                .lat(0.0)
                .build());
        TaskSearchResponse expectedResponse = TaskSearchResponse.builder().build();
        when(taskDiscoveryService.search(userDetails.getExternalReferenceId(), taskSearchRequest))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<TaskSearchResponse> actualResponse = taskHouseKeepingResource.searchTasks(Optional.empty(), userDetails.getExternalReferenceId(), taskSearchRequest);

        //Assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse, actualResponse.getData());
    }

    @Test
    public void searchTasksForAUser() {
        //Arrange

        AssignedViewTaskFetchRequest taskSearchRequest = new AssignedViewTaskFetchRequest("actor", false, null, null);
        taskSearchRequest.setPageNo(1);
        taskSearchRequest.setPageSize(10);
        taskSearchRequest.setLocation(EsLocationRequest.builder()
                .lon(0.0)
                .lat(0.0)
                .build());
        TaskSearchResponse expectedResponse = TaskSearchResponse.builder().build();
        when(taskDiscoveryService.search("actor", taskSearchRequest))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<TaskSearchResponse> actualResponse = taskHouseKeepingResource.searchTasks("actor", taskSearchRequest);

        //Assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse, actualResponse.getData());
    }

    @Test
    public void syncHotspot() {
        when(hotspotService.syncHotspots(anyString(), any(HotspotSyncRequest.class))).thenReturn(null);
        GenericResponse<List<HotspotDto>> response = taskHouseKeepingResource.syncHotspots(null, new AgentProfile(), new HotspotSyncRequest());
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void deactivateHotspot() {
        when(hotspotService.deactivateHotspot(anyString(), anyString())).thenReturn(null);
        GenericResponse<HotspotDto> response = taskHouseKeepingResource.deactivateHotspots(null, new AgentProfile(), "");
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void getAccessDetails() {
        when(hotspotService.getHotspotAccessDetails(anyString(), any())).thenReturn(null);
        GenericResponse<HotspotAccessDetailsResponse> response = taskHouseKeepingResource.getHotspotAccessDetails(null, new AgentProfile(), "", null);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void getStoredHotspot() {
        when(hotspotService.getStoredHotspots(anyString(), anyString(), anyString())).thenReturn(null);
        GenericResponse<List<HotspotDto>> response = taskHouseKeepingResource.getStoredHotspots(null,  new AgentProfile(), "",  "", "");
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void clientTaskCreateAndAssign() throws Exception {
        ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                .assigneeId("my_manager")
                .campaignId("active_123")
                .entityId("SARANSH_KIRANA")
                .taskDefinitionId("T")
                .build();
        ClientTaskCreateAndAssignMessage message = new ClientTaskCreateAndAssignMessage(request);
        when(TaskActionMessagePublisher.createAndAssignTaskForClient(message)).thenReturn(true);
        GenericResponse<Boolean> response = taskHouseKeepingResource.createAndAssignClientTask(Optional.empty(), UserDetails.builder().userId("saransh_user").userType(UserType.USER).build(), null, request, null);
        Assert.assertTrue(response.isSuccess());
    }


    @Test
    public void getFromHouseKeeping() {
        //arrange
        String request = "TASK_INSTANCE_ID";
        TaskInstance expectedResponse = TaskInstance.builder().build();
        TaskByIdRequest taskByIdRequest = TaskByIdRequest.builder()
                .taskInstanceId(request)
                .build();
        when(taskInstanceManagementService.getById(taskByIdRequest)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskInstance> actualResponse = taskHouseKeepingResource.get(request);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(expectedResponse, actualResponse.getData());

        //arrange
        when(taskInstanceManagementService.getById(taskByIdRequest)).thenReturn(null);

        //call
        actualResponse = taskHouseKeepingResource.get(request);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());

    }

    @Test
    public void getEsDocsWithinGeoFenceTest() throws Exception {
        GeoFenceRemappingSearchRequest request = GeoFenceRemappingSearchRequest.builder()
                .limit(10)
                .filters(Collections.emptyList())
                .geoFenceCoordinates(List.of(List.of(0.0,0.0)))
                .build();
        EsDocScrollResponse esDocScrollResponse = EsDocScrollResponse.builder()
                .docs(Collections.emptyList())
                .scrollId("")
                .count(10L)
        .build();
        when(taskHousekeepingService.getEsDocsWithinGeoFence(request)).thenReturn(esDocScrollResponse);
        GenericResponse<EsDocScrollResponse> response = taskHouseKeepingResource.getEsDocsWithinGeoFence(Optional.empty(), request);
        Assert.assertTrue(response.isSuccess());
    }
}
