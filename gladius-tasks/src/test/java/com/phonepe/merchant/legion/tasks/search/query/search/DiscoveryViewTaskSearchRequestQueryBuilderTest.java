package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.UserRestrictionResponse;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DiscoveryViewTaskSearchRequestQueryBuilderTest extends LegionTaskBaseTest {

    private static LegionService legionService = mock(LegionService.class);
    private static DiscoveryViewTaskSearchRequestQueryBuilder discoveryViewTaskSearchRequestQueryBuilder;


    @BeforeEach
    public void init() throws Exception {
        Miscellaneous miscellaneous = new Miscellaneous();
        miscellaneous.setMaxGeoSortDistance(1000);
        discoveryViewTaskSearchRequestQueryBuilder
                = new DiscoveryViewTaskSearchRequestQueryBuilder(legionService, miscellaneous, mock(ViewKillSwitchExecutor.class), mock(RestrictionQueryBuilder.class));
        sectorGeofenceQueryBuilder = mock(SectorGeofenceQueryBuilder.class);
        TaskEsUtils.init(sectorGeofenceQueryBuilder);
        when(sectorGeofenceQueryBuilder.visit(any(EqualsFilter.class))).thenReturn(QueryBuilders.boolQuery());
        when(sectorGeofenceQueryBuilder.visit(any(InFilter.class))).thenReturn(QueryBuilders.boolQuery());
    }

    @AfterEach
    void afterEach() {
        Mockito.reset(legionService);
    }


    @Test
    void testEnrichFiltersWithEmptySectorList() {

        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.AGENT)
                .managerId("manager").sectors(List.of()).attributes(new ArrayList<>()).build();
        when(legionService.getAgentProfile(any())).thenReturn(agentProfile);
        when(legionService.getAllAccessibleSectors(anyString(), anyString())).thenReturn(List.of());
        List<Filter> filterList = new ArrayList<>();
        DiscoveryViewTaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder().build();
        request.setFilters(filterList);
        request.setLocation(EsLocationRequest.builder().lat(0.0).lon(0.0).build());
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder().build());
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());
        discoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        Assertions.assertTrue(true);
    }

}
