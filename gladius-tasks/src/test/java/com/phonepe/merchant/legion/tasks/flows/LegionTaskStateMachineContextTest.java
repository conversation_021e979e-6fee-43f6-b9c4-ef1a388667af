package com.phonepe.merchant.legion.tasks.flows;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.flows.models.LegionTaskStateMachineContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;


public class LegionTaskStateMachineContextTest {

    private LegionTaskStateMachineContext context;


    public LegionTaskStateMachineContextTest() {
        ObjectMapper mapper = new ObjectMapper();
        SerDe.init(mapper);
    }


    @Test
    void getLeadIntentWhenPresent() {
        List<TaskMetaInformation> taskMetaList = new ArrayList<>();
        taskMetaList.add(TaskMetaInformation.builder().type(TaskMetaType.LEAD_INTENT).value("HOT").build());
        taskMetaList.add(TaskMetaInformation.builder().type(TaskMetaType.REMARK).value("Merchant is interested").build());
        TaskInstanceMeta taskInstanceMeta = TaskInstanceMeta.builder().taskMetaList(taskMetaList).build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TI1")
                .instanceMeta(SerDe.writeValueAsBytes(taskInstanceMeta)).build();
        context = new LegionTaskStateMachineContext(LegionTaskStateMachineState.INITIATED, LegionTaskStateMachineState.CREATED,
                LegionTaskStateMachineEvent.CREATED, storedTaskInstance);
        Assertions.assertEquals("HOT", context.getLeadIntent());
    }

    @Test
    void getLeadIntentWhenStoredTaskInstanceNull() {
        context = new LegionTaskStateMachineContext(LegionTaskStateMachineState.INITIATED, LegionTaskStateMachineState.CREATED,
                LegionTaskStateMachineEvent.CREATED, null);
        Assertions.assertNull(context.getLeadIntent());
    }

    @Test
    void getLeadIntentWhenIntentNotPresent() {
        List<TaskMetaInformation> taskMetaList = new ArrayList<>();
        TaskInstanceMeta taskInstanceMeta = TaskInstanceMeta.builder().taskMetaList(taskMetaList).build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TI1")
                .instanceMeta(SerDe.writeValueAsBytes(taskInstanceMeta)).build();
        context = new LegionTaskStateMachineContext(LegionTaskStateMachineState.INITIATED, LegionTaskStateMachineState.CREATED,
                LegionTaskStateMachineEvent.CREATED, storedTaskInstance);
        Assertions.assertNull(context.getLeadIntent());
    }
}




