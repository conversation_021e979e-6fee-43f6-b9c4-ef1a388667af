package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.repository.impl.CommentsOnTaskRepositoryImpl;
import com.phonepe.merchant.legion.tasks.utils.IdUtils;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CommentsOnTaskRepositoryTest {

    private IdUtils idUtils;

    private LookupDao<StoredCommentsOnTask> commentsLookupDao;

    private RelationalDao<StoredCommentsOnTask> commentsRelationalDao;

    private TaskInstanceRepository taskInstanceRepository;

    private StoredTaskInstance storedTaskInstance;

    private CommentsOnTaskRepository commentsRepository;

    private FoxtrotEventIngestionService eventIngestionService;
    @BeforeEach
    public void setup() {
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        idUtils = mock(IdUtils.class);
        commentsRelationalDao = mock(RelationalDao.class);
        commentsLookupDao = mock(LookupDao.class);
        taskInstanceRepository = mock(TaskInstanceRepository.class);
        commentsRepository = new CommentsOnTaskRepositoryImpl(eventIngestionService,
                idUtils, commentsRelationalDao, commentsLookupDao,
                taskInstanceRepository, 1);
        storedTaskInstance = mock(StoredTaskInstance.class);
    }

    @Test
    public void testGenerateCommentId() {
        // Mock task instance id and generated comment ID
        String taskInstanceId = "task-123";
        String generatedCommentId = "comment-789";

        // Mock behavior
        when(idUtils.createCommentOnTaskId(taskInstanceId, commentsLookupDao)).thenReturn(generatedCommentId);

        // Test the method
        String result = commentsRepository.generateCommentId(taskInstanceId);

        // Verify behavior
        assertEquals(generatedCommentId, result);
        verify(idUtils).createCommentOnTaskId(taskInstanceId, commentsLookupDao);
    }

    @Test
    public void testSave_SuccessfulSave() throws Exception {
        // Mock input
        StoredCommentsOnTask commentsOnTask = StoredCommentsOnTask.builder()
                .taskInstanceId("task-123")
                .createdBy("user-456")
                .build();

        // Mock repository behavior
        when(taskInstanceRepository.get("task-123")).thenReturn(Optional.of(storedTaskInstance));
        when(commentsRelationalDao.select(eq("task-123"), any(), eq(0), eq(1000)))
                .thenReturn(new ArrayList<>()); // No previous comments
        when(commentsLookupDao.save(any(StoredCommentsOnTask.class)))
                .thenReturn(Optional.of(commentsOnTask));

        // Mock the task instance fields
        when(storedTaskInstance.getActionId()).thenReturn("action-789");
        when(storedTaskInstance.getEntityId()).thenReturn("entity-123");

        // Call the save method
        StoredCommentsOnTask result = commentsRepository.save(commentsOnTask);

        // Verify the result and behavior
        assertNotNull(result);
        assertEquals("task-123", result.getTaskInstanceId());
        verify(taskInstanceRepository).get("task-123");
        verify(commentsLookupDao).save(any(StoredCommentsOnTask.class));
    }

    @Test
    public void testSave_TaskDoesNotExist() throws Exception {
        // Mock input
        StoredCommentsOnTask commentsOnTask = StoredCommentsOnTask.builder()
                .taskInstanceId("task-123")
                .createdBy("user-456")
                .build();

        // Mock repository behavior - Task instance does not exist
        when(taskInstanceRepository.get("task-123")).thenReturn(Optional.empty());

        // Test the exception
        LegionException exception = assertThrows(LegionException.class, () -> commentsRepository.save(commentsOnTask));
        assertEquals(CoreErrorCode.DAO_ERROR, exception.getErrorCode());

        // Verify interactions
        verify(taskInstanceRepository).get("task-123");
        verify(commentsLookupDao, never()).save(any(StoredCommentsOnTask.class));
    }

    @Test
    public void testSave_CommentLimitExceeded() throws Exception {
        // Mock input
        StoredCommentsOnTask commentsOnTask = StoredCommentsOnTask.builder()
                .taskInstanceId("task-123")
                .createdBy("user-456")
                .build();

        // Mock repository behavior - Task exists but comment limit exceeded
        when(taskInstanceRepository.get("task-123")).thenReturn(Optional.of(storedTaskInstance));
        when(commentsRelationalDao.select(eq("task-123"), any(), eq(0), eq(1000)))
                .thenReturn(List.of(new StoredCommentsOnTask(), new StoredCommentsOnTask())); // Mock previous comments

        // Test the exception
        LegionException exception = assertThrows(LegionException.class, () -> commentsRepository.save(commentsOnTask));
        assertEquals(CoreErrorCode.COMMENTS_ADDITION_LIMIT_EXHAUSTED, exception.getErrorCode());

        // Verify interactions
        verify(taskInstanceRepository).get("task-123");
        verify(commentsLookupDao, never()).save(any(StoredCommentsOnTask.class));
    }

    @Test
    public void testGetFromTaskInstanceId() throws Exception {
        // Mock repository behavior
        List<StoredCommentsOnTask> mockComments = Arrays.asList(
                StoredCommentsOnTask.builder().commentId("comment-789").taskInstanceId("task-123").build(),
                StoredCommentsOnTask.builder().commentId("comment-790").taskInstanceId("task-123").build()
        );

        when(commentsRelationalDao.select(eq("task-123"), any(), eq(0), eq(10)))
                .thenReturn(mockComments);

        // Call the method
        List<StoredCommentsOnTask> result = commentsRepository.getFromTaskInstanceId("task-123", Sorter.CREATED_AT, 0, 10);

        // Verify behavior and result
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("comment-789", result.get(0).getCommentId());
        assertEquals("comment-790", result.get(1).getCommentId());

        verify(commentsRelationalDao).select(eq("task-123"), any(), eq(0), eq(10));
    }

    @Test
    public void testGetFromCreatedBy() throws Exception {
        // Mock repository behavior
        List<StoredCommentsOnTask> mockComments = Arrays.asList(
                StoredCommentsOnTask.builder().commentId("comment-789").taskInstanceId("task-123").createdBy("user-456").build(),
                StoredCommentsOnTask.builder().commentId("comment-790").taskInstanceId("task-123").createdBy("user-456").build()
        );

        when(commentsRelationalDao.select(eq("task-123"), any(), eq(0), eq(1000)))
                .thenReturn(mockComments);

        // Call the method
        List<StoredCommentsOnTask> result = commentsRepository.getFromCreatedBy("task-123", "user-456");

        // Verify behavior and result
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("comment-789", result.get(0).getCommentId());
        assertEquals("comment-790", result.get(1).getCommentId());

        verify(commentsRelationalDao).select(eq("task-123"), any(), eq(0), eq(1000));
    }
}

