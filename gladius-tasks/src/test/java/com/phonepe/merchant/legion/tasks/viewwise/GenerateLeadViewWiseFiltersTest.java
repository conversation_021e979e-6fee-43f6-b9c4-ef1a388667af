package com.phonepe.merchant.legion.tasks.viewwise;

import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateLeadViewWiseFilters;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType.LEAD_VIEW;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasks;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasksV2;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;

public class GenerateLeadViewWiseFiltersTest extends LegionTaskBaseTest {
    private static GenerateLeadViewWiseFilters generateLeadViewWiseFilters;

    public GenerateLeadViewWiseFiltersTest() {
        generateLeadViewWiseFilters = new GenerateLeadViewWiseFilters();
    }

    @Test
    void testLeadTaskFilterOptions() {
        TaskFilters taskFilters = getFiltersOnTasks();
        ViewWiseFilters expectedAssignedFilters = getFiltersOnTasks().getLeadFilterOptions();
        expectedAssignedFilters.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedAssignedFilters.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        List<FilterOptions> filterList = new ArrayList<>();
        filterOptions.put(OBJECTIVES, filterList);
        filterOptions.put(POINTS, filterList);
        filterOptions.put(ACTION_ID, filterList) ;
        ViewWiseFilters filters = generateLeadViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions,LEAD_VIEW);
        Assertions.assertEquals(filters, expectedAssignedFilters);
    }

    @Test
    void testLeadTaskFilterOptionsV2() {

        TaskFiltersV2 taskFilters = getFiltersOnTasksV2();
        ViewWiseFiltersV2 expectedAssignedFilters = getFiltersOnTasksV2().getLeadFilterOptions();
        expectedAssignedFilters.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedAssignedFilters.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        Map<String, List<FilterOptionsV2>> filterOptions = new HashMap<>();
        List<FilterOptionsV2> filterList = new ArrayList<>();
        filterOptions.put(OBJECTIVES, filterList);
        filterOptions.put(POINTS, filterList);
        filterOptions.put(ACTION_ID, filterList) ;
        filterOptions.put(LEAD_INTENT, filterList);
        ViewWiseFiltersV2 filters = generateLeadViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions,LEAD_VIEW);
        Assertions.assertEquals(filters, expectedAssignedFilters);
    }
}
