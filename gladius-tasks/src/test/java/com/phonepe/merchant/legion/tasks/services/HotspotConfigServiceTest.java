package com.phonepe.merchant.legion.tasks.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.filtercraft.internal.models.Filter;
import com.phonepe.merchant.filtercraft.internal.models.conditions.Condition;
import com.phonepe.merchant.filtercraft.internal.models.conditions.InCondition;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotConfigCreationRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotMetaData;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotConfigDto;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.EventConstants;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.cache.HotspotConfigCache;
import com.phonepe.merchant.legion.tasks.repository.HotspotConfigRepository;
import com.phonepe.merchant.legion.tasks.services.impl.HotspotConfigServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.ACTION_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HotspotConfigServiceTest {

    @Mock
    private HotspotConfigRepository hotspotConfigRepository;

    @Mock
    private FoxtrotEventIngestionService foxtrotEventIngestionService;

    private HotspotConfigServiceImpl hotspotConfigService;

    @Mock
    private HotspotConfigCache hotspotConfigCache;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        SerDe.init(objectMapper);
        hotspotConfigService = new HotspotConfigServiceImpl(hotspotConfigRepository, foxtrotEventIngestionService, hotspotConfigCache);
    }


    @Test
    public void testGetHotspotType_Success() {
        String hotspotType = "SS";
        Filter filtercraftConfig = createFilterCraftFilter();
        StoredHotspotConfig hotspotDefinition = new StoredHotspotConfig();
        hotspotDefinition.setHotspotMetadata(SerDe.writeValueAsString(HotspotMetaData.builder().filtercraftConfig(SerDe.toJsonNode(filtercraftConfig)).build()));
        hotspotDefinition.setHotspotType(hotspotType);

        when(hotspotConfigRepository.get(hotspotType)).thenReturn(Optional.of(hotspotDefinition));
        HotspotConfigDto result = hotspotConfigService.get(hotspotType);

        assertNotNull(result);
        assertEquals(hotspotType, result.getHotspotType());
        verify(hotspotConfigRepository, times(1)).get(hotspotType);
    }

    @Test
    public void testGetHotspotType_Failure() {
        String hotspotType = "testType";
        doThrow(new RuntimeException()).when(hotspotConfigRepository).get(hotspotType);
        Assertions.assertThrows(RuntimeException.class, () -> hotspotConfigService.get(hotspotType));
    }

    @Test
    public void testGetHotspotType_NotFound() {
        String hotspotType = "nonExistentType";

        when(hotspotConfigRepository.get(hotspotType)).thenReturn(Optional.empty());
        Assertions.assertThrows(LegionException.class, () -> hotspotConfigService.get(hotspotType));
    }

    @Test
    public void testDeactivateHotspotType_Success() {
        String actorId = "testActor";
        String hotspotType = "testType";
        StoredHotspotConfig storedHotspotConfig = new StoredHotspotConfig();
        storedHotspotConfig.setHotspotType(hotspotType);

        when(hotspotConfigRepository.get(hotspotType)).thenReturn(Optional.of(storedHotspotConfig));
        when(hotspotConfigRepository.deactivate(actorId, hotspotType)).thenReturn(StoredHotspotConfig.builder().hotspotType(hotspotType).build());
        HotspotConfigDto result = hotspotConfigService.deactivate(actorId, hotspotType);

        assertNotNull(result);
        assertEquals(hotspotType, result.getHotspotType());
        verify(hotspotConfigRepository, times(1)).deactivate(actorId, hotspotType);
        verify(hotspotConfigRepository, times(1)).get(hotspotType); // Called in both deactivate and post-deactivate-get
        verify(foxtrotEventIngestionService, times(1)).ingestHotspotConfigStatusChangeEvent(any(StoredHotspotConfig.class), eq(EventConstants.HotspotEvents.HOTSPOT_CONFIG_DEACTIVATED));
    }

    @Test
    public void testDeactivateHotspotType_NotFound() {
        String actorId = "testActor";
        String hotspotType = "nonExistentType";

        when(hotspotConfigRepository.get(hotspotType)).thenReturn(Optional.empty());
        Assertions.assertThrows(RuntimeException.class, () -> hotspotConfigService.deactivate(actorId, hotspotType));

    }


    @Test
    public void testSaveOrUpdateHotspotType_Success() {
        String actorId = "actor";
        HotspotConfigCreationRequest hotspotConfigCreationRequest = new HotspotConfigCreationRequest();
        hotspotConfigCreationRequest.setHotspotType("SS");
        hotspotConfigCreationRequest.setCategory("SmartSpeaker");
        hotspotConfigCreationRequest.setWhitelistedSectors(Set.of("sector-1"));
        hotspotConfigCreationRequest.setDefinitionIds(List.of("definition-id"));

        when(hotspotConfigRepository.saveOrUpdate(any(StoredHotspotConfig.class)))
                .thenReturn(StoredHotspotConfig.builder().hotspotType("SS").category("SmartSpeaker").build());
        when(hotspotConfigRepository.getAllActiveHotspotConfigs()).thenReturn(
                List.of(StoredHotspotConfig.builder().whitelistedSectors(Set.of("sector-1")).build()));
        doNothing().when(hotspotConfigCache).invalidate(hotspotConfigCreationRequest.getHotspotType());
        HotspotConfigDto result = hotspotConfigService.saveOrUpdate(actorId, hotspotConfigCreationRequest);

        assertNotNull(result);
        assertEquals(hotspotConfigCreationRequest.getHotspotType(), result.getHotspotType());
        assertEquals(hotspotConfigCreationRequest.getCategory(), result.getCategory());
        verify(hotspotConfigRepository, times(1)).saveOrUpdate(any(StoredHotspotConfig.class));
        verify(foxtrotEventIngestionService, times(1)).ingestHotspotConfigStatusChangeEvent(any(StoredHotspotConfig.class), eq(EventConstants.HotspotEvents.HOTSPOT_CONFIG_ACTIVE));
    }

    @Test
    public void testSaveOrUpdateHotspotConfig_Failure() {
        String actorId = "testActor";
        HotspotConfigCreationRequest hotspotConfigCreationRequest = new HotspotConfigCreationRequest();
        Assertions.assertThrows(RuntimeException.class, () -> hotspotConfigService.saveOrUpdate(actorId, hotspotConfigCreationRequest));
    }

    @Test
    public void testSaveOrUpdateHotspotConfig_ValidationFailure() {
        StoredHotspotConfig expectedConfig = StoredHotspotConfig.builder().hotspotType("hotspotType").whitelistedSectors(Set.of("sector1"))
                .category("SS").build();
        when(hotspotConfigRepository.getAllActiveHotspotConfigs()).thenReturn(List.of(expectedConfig));
        HotspotConfigCreationRequest hotspotConfigCreationRequest = HotspotConfigCreationRequest.builder().hotspotType("testType")
                .whitelistedSectors(Set.of("sector1")).category("SS").build();
        Assertions.assertThrows(RuntimeException.class, () -> hotspotConfigService.saveOrUpdate("testActor", hotspotConfigCreationRequest));
    }


    private Filter createFilterCraftFilter() {
        List<Condition> conditionList = new ArrayList<>();
        List<String>actions = new ArrayList<>();
        InCondition<String> actionCondition = new InCondition<>();
        actionCondition.setField(ACTION_ID);
        actionCondition.setValues(actions);
        conditionList.add(actionCondition);
        Filter filter = new Filter();
        filter.setOrConditions(Collections.singletonList(conditionList));
        return filter;
    }

}
