package com.phonepe.merchant.legion.tasks.search.executors;

import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.AssignedSectorMapViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import org.apache.commons.lang.NotImplementedException;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.ArrayList;

import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getGeoSort;
import static org.mockito.Mockito.mock;

public class SectorMapViewTaskSearchQueryExecutorTest extends LegionTaskBaseTest {

    private static SectorMapViewTaskSearchQueryExecutor sectorMapViewTaskSearchQueryExecutor;
    private static ESRepository esRepository;

    private final AssignedSectorMapViewTaskSearchRequest request = AssignedSectorMapViewTaskSearchRequest.builder()
            .taskSearchRequestType(TaskViewRequestType.ASSIGNED_VIEW).filters(new ArrayList<>())
            .pageNo(1).pageSize(15).location(EsLocationRequest.builder().lat(0.0).lon(0.0).build()).build();
    private final BoolQueryBuilder boolQueryBuilder = getBoolQuery(new ArrayList<>());

    @BeforeClass
    public static void setUpTest() {
        esRepository = mock(ESRepository.class);
        sectorMapViewTaskSearchQueryExecutor = new SectorMapViewTaskSearchQueryExecutor(esRepository);
    }

    @Before
    public void after() {
        Mockito.reset(esRepository);
    }

    @Test
    public void testVisitDueDate() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = sectorMapViewTaskSearchQueryExecutor.visitDueDate(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC);
    }

    @Test
    public void testVisitPoints() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder,
                        1, 15, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = sectorMapViewTaskSearchQueryExecutor.visitPoints(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder,
                1, 15, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC);
    }

    @Test
    public void testVisitLocation() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithGeoSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                getGeoSort(request.getLocation()))).thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = sectorMapViewTaskSearchQueryExecutor.visitLocation(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithGeoSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                getGeoSort(request.getLocation()));
    }

    @Test
    public void testVisitCreatedAt() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder,
                        1, 15, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT, SortOrder.DESC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = sectorMapViewTaskSearchQueryExecutor.visitCreatedAt(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder,
                1, 15, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT, SortOrder.DESC);
    }

    @Test(expected = NotImplementedException.class)
    public void testVisitCreatedAtAsc() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        sectorMapViewTaskSearchQueryExecutor.visitCreatedAtAsc(request, boolQueryBuilder);
    }

    @Test
    public void testVisitNone() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.search(TASK_INDEX, boolQueryBuilder, 0, 15)).thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = sectorMapViewTaskSearchQueryExecutor.visitNone(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).search(TASK_INDEX, boolQueryBuilder, 0, 15);
    }

    @Test
    public void testReschedule() {
        Assertions.assertNull(sectorMapViewTaskSearchQueryExecutor.visitReschedule(request, boolQueryBuilder));
    }


}
