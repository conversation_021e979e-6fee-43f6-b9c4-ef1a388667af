package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskTransition;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskOperationsMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskRecreationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCampaign;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.services.ClockWorkService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.services.impl.TaskRecreationServiceImpl;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.platform.clockwork.model.ClockworkResponse;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskRecreationServiceTest extends LegionTaskBaseTest {

    private static TaskRecreationService taskRecreationService;
    private static FoxtrotEventIngestionService foxtrotEventIngestionService = mock(FoxtrotEventIngestionService.class);
    private static TaskManagementService taskManagementService = mock(TaskManagementService.class);
    private static TaskInstanceManagementService taskInstanceManagementService = mock(TaskInstanceManagementService.class);
    private static ClockWorkService clockWorkService = mock(ClockWorkService.class);
    private static TaskInstance taskInstance = mock(TaskInstance.class);

    @BeforeClass
    public static void init() {
        taskRecreationService = new TaskRecreationServiceImpl(clockWorkService, taskInstanceRepository, taskDefinitionRepository, foxtrotEventIngestionService, taskManagementService, taskInstanceManagementService, campaignService, eventExecutor);
    }

    @Test(expected = Exception.class)
    public void wrongCurStateTask() {

        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(5)
                .maxRetries(2)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("TD123456789def")
                .createdBy("saransh")
                .points(2)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        StoredTaskInstance availableTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TI1234567890ins")
                .taskDefinitionId("TD123456789def")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.AVAILABLE)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .build();
        taskInstanceRepository.save(availableTaskInstance);

        TaskRecreationRequest availabletaskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("TI1234567890ins")
                .build();

        when(taskRecreationService.recreateTask(availabletaskRecreationRequest)).thenCallRealMethod();

    }

    @Test(expected = Test.None.class)
    public void lowMaxRetriesTask() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(5)
                .maxRetries(2)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("TD123456789990")
                .createdBy("saransh")
                .points(2)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, 11, 31, 59, 59, 59);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId("SARANSH_TEST1")
                .createdBy("saransh")
                .startDate(new Date(0L))
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(calendar.getTimeInMillis())
                .updatedBy("saransh")
                .build();
        campaignRepository.save(storedCampaign);

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TI888")
                .taskDefinitionId("TD123456789990")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .campaignId("SARANSH_TEST1")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .retriesDone(3)
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("TI888")
                .build();

        taskRecreationService.recreateTask(taskRecreationRequest);
    }


    @Test
    public void recreateTask() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(5)
                .maxRetries(4)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("taskDefintionID123")
                .createdBy("saransh")
                .points(3)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);


        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST")
                .taskDefinitionId("taskDefintionID123")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .retriesDone(0)
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        Date date = new Date();
        List<TaskTransition> transitions = new ArrayList<TaskTransition>();
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST").transitionId(1).fromState(LegionTaskStateMachineState.INITIATED).toState(LegionTaskStateMachineState.CREATED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST").transitionId(2).fromState(LegionTaskStateMachineState.CREATED).toState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST").transitionId(3).fromState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).toState(LegionTaskStateMachineState.COMPLETED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST").transitionId(4).fromState(LegionTaskStateMachineState.COMPLETED).toState(LegionTaskStateMachineState.VERIFICATION_FAILED).build());

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST")
                .build();

        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(TaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST")
                .taskDefinitionId("taskDefintionID123")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .active(true)
                .taskTransitions(transitions)
                .instanceMeta(TaskInstanceMeta.builder()
                        .retriesDone(0)
                        .build())
                .build());

        StoredTaskInstance actualResponse = StoredTaskInstance.builder()
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT_TEST")
                .entityId("entityId")
                .actionId("actionId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .active(true)
                .build();

        when(taskManagementService.command(any(TaskCreateRequest.class),eq("saransh"))).thenReturn(actualResponse);
        TaskInstance actualResponse1 = taskInstanceManagementService.getById(TaskByIdRequest.builder()
                .taskInstanceId(actualResponse.getTaskInstanceId())
                .build());
        Assert.assertEquals(actualResponse.getTaskInstanceId(), actualResponse1.getTaskInstanceId());
    }

    @Test
    public void clockworkScheduler() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(10)
                .maxRetries(7)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("saranshDefinition")
                .createdBy("saransh")
                .points(3)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);


        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance")
                .taskDefinitionId("saranshDefinition")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .retriesDone(3)
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        String taskInstanceId = "saranshInstance";

        ClockworkResponse<SchedulingResponse> expectedResponse = new ClockworkResponse<>();
        expectedResponse.setData(SchedulingResponse.builder()
                .jobId("saransh_job_id")
                .build());

        when(clockWorkService.scheduleClockworkForTaskRecreation(any(StoredTaskInstance.class), any(TaskOperationsMeta.class))).thenReturn(expectedResponse);

        SchedulingResponse actualResponse = taskRecreationService.clockworkScheduler(taskInstanceId);
        Assert.assertEquals(expectedResponse.getData(), actualResponse);
    }

    @Test(expected = Exception.class)
    public void taskRecreation() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(4)
                .maxRetries(4)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("saranshDef123")
                .createdBy("saransh")
                .points(3)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);


        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance123")
                .taskDefinitionId("saranshDef123")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .retriesDone(1)
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        Date date = new Date();
        List<TaskTransition> transitions = new ArrayList<TaskTransition>();
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance123").transitionId(1).fromState(LegionTaskStateMachineState.INITIATED).toState(LegionTaskStateMachineState.CREATED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance123").transitionId(2).fromState(LegionTaskStateMachineState.CREATED).toState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance123").transitionId(3).fromState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).toState(LegionTaskStateMachineState.COMPLETED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance123").transitionId(4).fromState(LegionTaskStateMachineState.COMPLETED).toState(LegionTaskStateMachineState.VERIFICATION_FAILED).build());

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("saranshInstance123")
                .build();

        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(TaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance123")
                .taskDefinitionId("saranshDef123")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .active(true)
                .taskTransitions(transitions)
                .instanceMeta(TaskInstanceMeta.builder()
                        .retriesDone(0)
                        .build())
                .build());

        when(taskRecreationService.recreateTask(taskRecreationRequest)).thenCallRealMethod();

    }

    @Test(expected = Test.None.class)
    public void verificationFailedTaskRecreationTest() {
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskDefinitionId("saransh322")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .createdBy("saransh")
                .priority(Priority.P0)
                .taskOperationsMeta(SerDe.writeValueAsBytes(TaskOperationsMeta.builder().maxRetries(4).recreationInterval(5).build()))
                .actionId("123")
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("saransh321")
                .taskDefinitionId("saransh322")
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .build();

        taskRecreationService.verificationFailedTaskRecreation(storedTaskInstance);
    }

    @Test(expected = Test.None.class)
    public void inactiveCampaign() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(5)
                .maxRetries(2)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("TD12345678999")
                .createdBy("saransh")
                .points(2)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        Calendar calendar = Calendar.getInstance();
        calendar.set(2021, 11, 31, 59, 59, 59);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId("SARANSH_TEST")
                .createdBy("saransh")
                .startDate(new Date(0L))
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(calendar.getTimeInMillis())
                .updatedBy("saransh")
                .build();
        campaignRepository.save(storedCampaign);

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TI1234567899")
                .taskDefinitionId("TD12345678999")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .campaignId("SARANSH_TEST")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .retriesDone(3)
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("TI1234567899")
                .build();

        when(taskRecreationService.recreateTask(taskRecreationRequest)).thenCallRealMethod();
    }

    @Test(expected = Test.None.class)
    public void relativeExpiryCampaign() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(5)
                .maxRetries(2)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("TD123456789991")
                .createdBy("saransh")
                .points(2)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId("SARANSH_TESTT")
                .createdBy("saransh")
                .startDate(new Date(0L))
                .updatedBy("saransh")
                .build();
        campaignRepository.save(storedCampaign);

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TI999")
                .taskDefinitionId("TD123456789991")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .campaignId("SARANSH_TESTT")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .retriesDone(3)
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("TI999")
                .build();

        when(taskRecreationService.recreateTask(taskRecreationRequest)).thenCallRealMethod();
    }

    @Test(expected = Test.None.class)
    public void taskRecreation_nullInstanceMeta() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(4)
                .maxRetries(4)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("saranshDef1234")
                .createdBy("saransh")
                .points(3)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId("SARANSH_TEST2")
                .createdBy("saransh")
                .startDate(new Date(0L))
                .updatedBy("saransh")
                .build();
        campaignRepository.save(storedCampaign);

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance1234")
                .taskDefinitionId("saranshDef1234")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .campaignId("SARANSH_TEST2")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        Date date = new Date();
        List<TaskTransition> transitions = new ArrayList<TaskTransition>();
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1234").transitionId(1).fromState(LegionTaskStateMachineState.INITIATED).toState(LegionTaskStateMachineState.CREATED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1234").transitionId(2).fromState(LegionTaskStateMachineState.CREATED).toState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1234").transitionId(3).fromState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).toState(LegionTaskStateMachineState.COMPLETED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1234").transitionId(4).fromState(LegionTaskStateMachineState.COMPLETED).toState(LegionTaskStateMachineState.VERIFICATION_FAILED).build());

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("saranshInstance1234")
                .build();

        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(TaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance1234")
                .taskDefinitionId("saranshDef1234")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .campaign("SARANSH_TEST2")
                .active(true)
                .taskTransitions(transitions)
                .build());

        when(taskManagementService.command(any(),any()))
                .thenReturn(StoredTaskInstance.builder()
                        .secondaryIndexSyncRequired(true)
                        .taskInstanceId("saranshInstance12341")
                        .taskDefinitionId("saranshDef1234")
                        .entityType(EntityType.SECTOR)
                        .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                        .entityId("entityId")
                        .campaignId("SARANSH_TEST2")
                        .namespace(Namespace.MERCHANT_ONBOARDING)
                        .actionId("actionId")
                        .build());

        taskRecreationService.recreateTask(taskRecreationRequest);

    }

    @Test(expected = Test.None.class)
    public void taskRecreation_nullRetriesDone() {
        TaskOperationsMeta taskOperationsMeta = TaskOperationsMeta.builder()
                .recreationInterval(4)
                .maxRetries(4)
                .build();

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskOperationsMeta(SerDe.writeValueAsBytes(taskOperationsMeta))
                .taskDefinitionId("saranshDef1235")
                .createdBy("saransh")
                .points(3)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefinition);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId("SARANSH_TEST3")
                .createdBy("saransh")
                .startDate(new Date(0L))
                .updatedBy("saransh")
                .build();
        campaignRepository.save(storedCampaign);

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance1235")
                .taskDefinitionId("saranshDef1235")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .campaignId("SARANSH_TEST3")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder()
                        .build()))
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        Date date = new Date();
        List<TaskTransition> transitions = new ArrayList<TaskTransition>();
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1235").transitionId(1).fromState(LegionTaskStateMachineState.INITIATED).toState(LegionTaskStateMachineState.CREATED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1235").transitionId(2).fromState(LegionTaskStateMachineState.CREATED).toState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1235").transitionId(3).fromState(LegionTaskStateMachineState.BOUNDED_ASSIGNED).toState(LegionTaskStateMachineState.COMPLETED).build());
        transitions.add(TaskTransition.builder().createdAt(date).taskInstanceId("saranshInstance1235").transitionId(4).fromState(LegionTaskStateMachineState.COMPLETED).toState(LegionTaskStateMachineState.VERIFICATION_FAILED).build());

        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId("saranshInstance1235")
                .build();

        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(TaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("saranshInstance1235")
                .taskDefinitionId("saranshDef1235")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.VERIFICATION_FAILED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .campaign("SARANSH_TEST3")
                .active(true)
                .taskTransitions(transitions)
                .instanceMeta(TaskInstanceMeta.builder()
                        .build())
                .build());

        taskRecreationService.recreateTask(taskRecreationRequest);

    }
}
