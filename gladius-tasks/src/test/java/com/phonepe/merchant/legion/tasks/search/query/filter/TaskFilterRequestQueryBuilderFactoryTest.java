package com.phonepe.merchant.legion.tasks.search.query.filter;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedHotspotTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedSectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryHotspotTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoverySectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.EscalationViewTaskFilterRequest;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.junit.jupiter.api.Test;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class TaskFilterRequestQueryBuilderFactoryTest extends LegionTaskBaseTest {

    private final DiscoveryTaskFilterRequestQueryBuilder discoveryViewFilterGenerator;
    private final SectorAssignedTaskFilterRequestQueryBuilder sectorAssignedViewFilterGenerator;
    private final SectorDiscoveryTaskFilterRequestQueryBuilder sectorDiscoveryViewFilterGenerator;
    private static TaskFilterRequestQueryBuilderFactory taskFilterRequestQueryBuilderFactory;
    private final EscalationViewTaskFilterRequestQueryBuilder escalationViewTaskFilterRequestQueryBuilder;
    private final BaseTaskFilterRequestQueryBuilder assignedViewFilterGenerator;
    private final HotspotRequestQueryBuilder hotspotRequestQueryGenerator;

    public TaskFilterRequestQueryBuilderFactoryTest() {

        System.out.println("Method called");
        assignedViewFilterGenerator = mock(BaseTaskFilterRequestQueryBuilder.class);
        discoveryViewFilterGenerator = mock(DiscoveryTaskFilterRequestQueryBuilder.class);
        sectorAssignedViewFilterGenerator = mock(SectorAssignedTaskFilterRequestQueryBuilder.class);
        sectorDiscoveryViewFilterGenerator = mock(SectorDiscoveryTaskFilterRequestQueryBuilder.class);
        hotspotRequestQueryGenerator  = mock(HotspotRequestQueryBuilder.class);
        escalationViewTaskFilterRequestQueryBuilder = mock(EscalationViewTaskFilterRequestQueryBuilder.class);
        taskFilterRequestQueryBuilderFactory = new TaskFilterRequestQueryBuilderFactory(assignedViewFilterGenerator, discoveryViewFilterGenerator,
                sectorAssignedViewFilterGenerator, sectorDiscoveryViewFilterGenerator, escalationViewTaskFilterRequestQueryBuilder, hotspotRequestQueryGenerator, null);
    }

    @Test
    void testEnrichAssignedView() {
        AssignedLocationTaskFilterRequest request = AssignedLocationTaskFilterRequest.builder()
                .location(EsLocationRequest.builder().lat(0.0).lon(0.0).build())
                .build();

        when(assignedViewFilterGenerator.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());

        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(assignedViewFilterGenerator).enrichFilters("agent", request);
    }

    @Test
    void testEnrichDiscoveryView() {
        DiscoveryLocationTaskFilterRequest request = DiscoveryLocationTaskFilterRequest.builder()
                .location(EsLocationRequest.builder().lat(0.0).lon(0.0).build()).build();

        when(discoveryViewFilterGenerator.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());

        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(discoveryViewFilterGenerator).enrichFilters("agent", request);
    }

    @Test
    void testEnrichDiscoveryViewV2() {
        DiscoverySectorTaskFilterRequest request = DiscoverySectorTaskFilterRequest.builder().sectorId("sector")
                .build();

        when(sectorDiscoveryViewFilterGenerator.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());

        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(sectorDiscoveryViewFilterGenerator).enrichFilters("agent", request);
    }

    @Test
    void testEnrichSectorAssignedView() {
        AssignedSectorTaskFilterRequest request = AssignedSectorTaskFilterRequest.builder().sectorId("sector")
                .build();

        when(sectorAssignedViewFilterGenerator.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());

        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(sectorAssignedViewFilterGenerator).enrichFilters("agent", request);
    }

    @Test
    void testEnrichEscalationView() {
        EscalationViewTaskFilterRequest request = new EscalationViewTaskFilterRequest();

        when(escalationViewTaskFilterRequestQueryBuilder.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());

        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(escalationViewTaskFilterRequestQueryBuilder).enrichFilters("agent", request);
    }

    @Test
    void testEnrichHotspotDiscoveryView() {
        DiscoveryHotspotTaskFilterRequest request = new DiscoveryHotspotTaskFilterRequest("hotspotId", "sectorId");

        when(hotspotRequestQueryGenerator.buildQuery(anyString(), anyString())).thenReturn(new BoolQueryBuilder());
        when(sectorDiscoveryViewFilterGenerator.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());


        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(sectorDiscoveryViewFilterGenerator).enrichFilters("agent", request);
    }

    @Test
    void testEnrichHotspotAssignedView() {
        AssignedHotspotTaskFilterRequest request = new AssignedHotspotTaskFilterRequest("hotspotId", "sectorId");

        when(hotspotRequestQueryGenerator.buildQuery(anyString(), anyString())).thenReturn(new BoolQueryBuilder());
        when(sectorAssignedViewFilterGenerator.enrichFilters("agent", request)).thenReturn(new BoolQueryBuilder());


        QueryBuilder actualResponse = taskFilterRequestQueryBuilderFactory.enrichFilter("agent", request);

        verify(sectorAssignedViewFilterGenerator).enrichFilters("agent", request);
    }

}
