package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;
import java.util.UUID;
import java.util.function.UnaryOperator;

@RunWith(MockitoJUnitRunner.class)
public class TaskTransitionRepositoryImplTest extends LegionTaskBaseTest {

  @Test(expected = LegionException.class)
  public void testSaveError() {
    StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
            .actor("ACTOR")
            .taskInstance(StoredTaskInstance.builder()
                    .taskInstanceId("viueabslisrlwgrng")
                    .build())
            .toState(LegionTaskStateMachineState.CREATED)
            .fromState(LegionTaskStateMachineState.INITIATED)
            .event(LegionTaskStateMachineEvent.CREATED)
            .transitionId(1)
            .build();
    taskTransitionRepository.save(storedTaskTransition);
  }

  @Test
  public void testSaveWithOneSideMissing() {
    String taskInstanceId = UUID.randomUUID().toString().substring(0, 8);

    StoredTaskInstance storedAgentTaskInstance = StoredTaskInstance.builder()
            .taskInstanceId(taskInstanceId)
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .actionId("MERCHANT_KYC_VERIFICATION")
            .entityType(EntityType.SECTOR)
            .secondaryIndexSyncRequired(false)
            .entityId("FKRT")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .partitionId(1)
            .curState(LegionTaskStateMachineState.INITIATED)
            .taskDefinitionId("TASK_DEFINITION_ID")
            .build();

    StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
            .actor("ACTOR")
            .taskInstance(storedAgentTaskInstance)
            .taskInstanceId(taskInstanceId)
            .toState(LegionTaskStateMachineState.CREATED)
            .fromState(LegionTaskStateMachineState.INITIATED)
            .event(LegionTaskStateMachineEvent.CREATED)
            .transitionId(1)
            .build();

    // saving only many side also saves the one side if not present
    taskInstanceRepository.save(storedAgentTaskInstance);
    taskTransitionRepository.save(storedTaskTransition);

    Optional<StoredTaskInstance> savedTaskInstanceWithStatus = taskInstanceRepository.get(taskInstanceId);
    Assert.assertEquals(1,savedTaskInstanceWithStatus.get().getTaskTransitions().size());
  }

  @Test
  public void testGet() {
    String taskInstanceId = UUID.randomUUID().toString().substring(0, 8);

    StoredTaskInstance storedAgentTaskInstance = StoredTaskInstance.builder()
            .taskInstanceId(taskInstanceId)
            .secondaryIndexSyncRequired(false)
            .entityId("FKRT")
            .entityType(EntityType.SECTOR)
            .actionId("MERCHANT_KYC_VERIFICATION")
            .curState(LegionTaskStateMachineState.INITIATED)
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .partitionId(1)
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .taskDefinitionId("TASK_DEFINITION_ID")
            .build();

    StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
            .actor("ACTOR")
            .taskInstance(storedAgentTaskInstance)
            .toState(LegionTaskStateMachineState.CREATED)
            .fromState(LegionTaskStateMachineState.INITIATED)
            .event(LegionTaskStateMachineEvent.CREATED)
            .transitionId(1)
            .build();

    // saving only many side also saves the one side if not present
    taskInstanceRepository.save(storedAgentTaskInstance);
    taskTransitionRepository.save(storedTaskTransition);

    Optional<StoredTaskTransition> storedTaskTransition1 = taskTransitionRepository.get(taskInstanceId, 1);
    Assert.assertSame(LegionTaskStateMachineState.CREATED, storedTaskTransition1.get().getToState());
  }

  @Test
  public void testUpdate() {
    String taskInstanceId = UUID.randomUUID().toString().substring(0, 8);

    StoredTaskInstance storedAgentTaskInstance = StoredTaskInstance.builder()
            .taskInstanceId(taskInstanceId)
            .actionId("MERCHANT_KYC_VERIFICATION")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .secondaryIndexSyncRequired(false)
            .entityId("FKRT")
            .curState(LegionTaskStateMachineState.INITIATED)
            .entityType(EntityType.SECTOR)
            .partitionId(1)
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .taskDefinitionId("TASK_DEFINITION_ID")
            .build();

    StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
            .actor("ACTOR")
            .taskInstance(storedAgentTaskInstance)
            .toState(LegionTaskStateMachineState.CREATED)
            .fromState(LegionTaskStateMachineState.INITIATED)
            .event(LegionTaskStateMachineEvent.CREATED)
            .transitionId(1)
            .build();

    // saving only many side also saves the one side if not present
    taskInstanceRepository.save(storedAgentTaskInstance);
    taskTransitionRepository.save(storedTaskTransition);
    taskTransitionRepository.update(taskInstanceId, 1, new UnaryOperator<StoredTaskTransition>() {
      @Override
      public StoredTaskTransition apply(StoredTaskTransition taskTransition) {
        taskTransition.setToState(LegionTaskStateMachineState.BOUNDED_ASSIGNED);
        taskTransition.setActor("ACTORSURAJ");
        return taskTransition;
      }
    });

    Optional<StoredTaskTransition> storedTaskTransition1 = taskTransitionRepository.get(taskInstanceId, 1);
    Assert.assertSame(LegionTaskStateMachineState.BOUNDED_ASSIGNED, storedTaskTransition1.get().getToState());
  }
}