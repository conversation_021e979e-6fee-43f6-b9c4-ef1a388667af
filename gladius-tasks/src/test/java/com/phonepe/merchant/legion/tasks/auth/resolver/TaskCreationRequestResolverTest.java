package com.phonepe.merchant.legion.tasks.auth.resolver;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionTenants;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsByTypeCache;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.authn.UserType;
import com.phonepe.olympus.im.models.user.SystemUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.ws.rs.container.ContainerRequestContext;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskCreationRequestResolverTest extends LegionTaskBaseTest {
    private static TaskDefinitionCache taskDefinitionCache = mock(TaskDefinitionCache.class);
    @Mock
    private static OlympusIMClient olympusIMClient;
    private static ContainerRequestContext containerRequestContext = mock(ContainerRequestContext.class);
    @Mock
    private static UserAuthDetails userAuthDetails;
    @Mock
    private static SystemUserDetails systemUserDetails;
    @Mock
    private static AccessAllowed accessAllowed;
    private static AccessResolverForClients accessResolver;
    private static TaskInstanceManagementServiceImpl taskInstanceManagementService = mock(TaskInstanceManagementServiceImpl.class);

    @BeforeClass
    public static void setUp() {
        SerDe.init(new ObjectMapper());
        accessResolver = new TaskCreationRequestResolver(()->olympusIMClient, taskDefinitionCache, taskInstanceManagementService, foxtrotEventIngestionService, mock(TaskDefinitionIdsByTypeCache.class));

        taskDefinitionRepository.save(StoredTaskDefinition.builder()
                        .taskDefinitionId("testDefinitionId")
                        .actionId("KYC")
                        .name("definitionID")
                        .createdBy("mohit")
                        .updatedBy("mohit")
                        .definitionAttributes(SerDe.writeValueAsBytes(TaskDefinitionAttributes.builder()
                                        .tenants(TaskDefinitionTenants.builder()
                                                .tenants(Set.of("testGroupId"))
                                                .build())
                                .build()))
                        .points(5)
                        .priority(Priority.P0)
                        .namespace(Namespace.LEGION)
                .build());
        taskDefinitionRepository.save(StoredTaskDefinition.builder()
                .taskDefinitionId("testDefinitionId1")
                .actionId("KYC")
                .name("definitionID")
                .createdBy("mohit")
                .updatedBy("mohit")
                .points(5)
                .priority(Priority.P0)
                .namespace(Namespace.LEGION)
                .build());
    }

    @org.junit.Test
    public void testIsAuthorized_ClientToken_Authorized() throws IOException {
        String definitionId = "testDefinitionId";
        String componentInstanceGroupId = "testGroupId";
        String[] permissions = {"READ"};
        when(containerRequestContext.hasEntity()).thenReturn(true);
        when(containerRequestContext.getEntityStream()).thenReturn(new ByteArrayInputStream("{\"taskDefinitionId\":\"testDefinitionId\"}".getBytes()));
        when(olympusIMClient.getUserAuthDetails(containerRequestContext)).thenReturn(userAuthDetails);
        when(userAuthDetails.getUserDetails()).thenReturn(systemUserDetails);
        when(systemUserDetails.getUserType()).thenReturn(UserType.SYSTEM);
        when(systemUserDetails.getComponentInstanceGroupId()).thenReturn(componentInstanceGroupId);
        when(accessAllowed.permissions()).thenReturn(permissions);
        when(taskDefinitionCache.get(any())).thenReturn(TaskDefinitionInstance.builder()
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .tenants(TaskDefinitionTenants.builder()
                                .tenants(Set.of("testGroupId"))
                                .build())
                        .build()).build());        when(olympusIMClient.verifyPermissions(any(), any(), any())).thenReturn(true);

        Optional<Boolean> result = accessResolver.isAuthorized(containerRequestContext, accessAllowed);

        assertTrue(result.isPresent());
        assertTrue(result.get());
    }

    @org.junit.Test
    public void testIsAuthorized_ClientToken_Unauthorized() throws IOException {
        String definitionId = "testDefinitionId";
        String componentInstanceGroupId = "testGroupIds";
        String[] permissions = {"READ"};
        when(containerRequestContext.hasEntity()).thenReturn(true);
        when(containerRequestContext.getEntityStream()).thenReturn(new ByteArrayInputStream("{\"taskDefinitionId\":\"testDefinitionId\"}".getBytes()));
        when(olympusIMClient.getUserAuthDetails(containerRequestContext)).thenReturn(userAuthDetails);
        when(userAuthDetails.getUserDetails()).thenReturn(systemUserDetails);
        when(systemUserDetails.getUserType()).thenReturn(UserType.SYSTEM);
        when(systemUserDetails.getComponentInstanceGroupId()).thenReturn(componentInstanceGroupId);
        when(accessAllowed.permissions()).thenReturn(permissions);
        when(taskDefinitionCache.get(any())).thenReturn(TaskDefinitionInstance.builder()
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .tenants(TaskDefinitionTenants.builder()
                                .tenants(Set.of("testGroupId"))
                                .build())
                        .build()).build());
        Optional<Boolean> result = accessResolver.isAuthorized(containerRequestContext, accessAllowed);

        assertTrue(result.isPresent());
        assertFalse(result.get());
    }

    @Test
    public void testIsAuthorized_ClientToken_NoClients() throws IOException {
        ClientTaskCreateAndAssignRequest request = new ClientTaskCreateAndAssignRequest();
        String testDefinitionId = request.getDefinitionId();
        when(containerRequestContext.hasEntity()).thenReturn(true);
        when(containerRequestContext.getEntityStream()).thenReturn(new ByteArrayInputStream("{\"taskDefinitionId\":\"testDefinitionId1\"}".getBytes()));
        when(olympusIMClient.getUserAuthDetails(containerRequestContext)).thenReturn(userAuthDetails);
        when(olympusIMClient.verifyPermissions(any(), any(), any())).thenReturn(Boolean.TRUE);
        when(userAuthDetails.getUserDetails()).thenReturn(systemUserDetails);
        when(systemUserDetails.getUserType()).thenReturn(UserType.SYSTEM);
        when(taskDefinitionCache.get(any())).thenReturn(TaskDefinitionInstance.builder().build());
        Optional<Boolean> result = accessResolver.isAuthorized(containerRequestContext, accessAllowed);

        assertTrue(result.isPresent());
        assertTrue(result.get());

    }

    @Test
    public void testIsAuthorized_Exception() {
        when(containerRequestContext.hasEntity()).thenReturn(true);
        when(containerRequestContext.getEntityStream()).thenReturn(null);
        when(olympusIMClient.getUserAuthDetails(containerRequestContext)).thenReturn(userAuthDetails);
        when(userAuthDetails.getUserDetails()).thenReturn(systemUserDetails);
        when(systemUserDetails.getUserType()).thenReturn(UserType.SYSTEM);
        assertThrows(Exception.class, () -> accessResolver.isAuthorized(containerRequestContext, accessAllowed));
    }
}