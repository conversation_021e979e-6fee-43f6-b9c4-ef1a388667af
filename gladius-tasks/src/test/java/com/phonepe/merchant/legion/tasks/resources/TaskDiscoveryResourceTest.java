package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.request.AgentTaskEligibilityRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TaskStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TasksStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.AssignedSectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.AgentTaskEligibilityResponse;
import com.phonepe.merchant.gladius.models.tasks.response.EntityStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDetailResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskStateStatsByAction;
import com.phonepe.merchant.gladius.models.tasks.response.UserStatsAndFilters;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.models.response.GenericResponse;
import lombok.val;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskDiscoveryResourceTest {

    private static TaskDiscoveryService taskDiscoveryService;
    private static TaskDiscoveryResource taskDiscoveryResource;
    private final static String SECTOR_ID = "sectorId";
    private final static String ENTITY_ID = "entityId";

    @BeforeClass
    public static void init() {
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        taskDiscoveryResource = new TaskDiscoveryResource(taskDiscoveryService, mock(ValidationService.class));
    }

    @Test
    public void searchTasks() {
        //Arrange
        UserDetails userDetails = getUserDetails();
        AssignedViewTaskFetchRequest taskSearchRequest = new AssignedViewTaskFetchRequest(userDetails.getExternalReferenceId(), false, null, null);
        taskSearchRequest.setPageNo(1);
        taskSearchRequest.setPageSize(10);
        taskSearchRequest.setLocation(EsLocationRequest.builder()
                .lon(0.0)
                .lat(0.0)
                .build());
        TaskSearchResponse expectedResponse = TaskSearchResponse.builder().build();
        when(taskDiscoveryService.search(userDetails.getExternalReferenceId(),taskSearchRequest))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<TaskSearchResponse> actualResponse = taskDiscoveryResource.searchTasks(Optional.empty(), userDetails,null, taskSearchRequest, null);

        //Assert
        Assert.assertTrue(actualResponse.isSuccess());
        assertSame(expectedResponse,actualResponse.getData());
    }

    @Test
    public void getTaskListingForAgent() {
        //Arrange
        UserDetails userDetails = getUserDetails();
        String entityId = "random entity id";
        TaskSearchResponse expectedResponse = TaskSearchResponse.builder()
                .taskCount(0)
                .taskList(new ArrayList<>())
                .build();
        TaskListingRequest request = TaskListingRequest.builder()
                .entityId(entityId)
                .pageNo(1)
                .pageSize(100)
                .requestType(TaskViewRequestType.ASSIGNED_VIEW)
                .build();
        when(taskDiscoveryService.getAgentTaskListing(any(),any()))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<TaskSearchResponse> actualResponse2 = taskDiscoveryResource.getTaskListingForAgent(Optional.empty(), userDetails, null ,request, null);

        //Assert
        Assert.assertTrue(actualResponse2.isSuccess());
        assertSame(expectedResponse,actualResponse2.getData());
    }

    @Test
    public void getTaskListForAgentInAssignedAndDiscoveryViewTest() {
        String entityId = "random entity id";
        TaskSearchResponse expectedResponse = TaskSearchResponse.builder()
                .taskCount(0)
                .taskList(new ArrayList<>())
                .build();
        EntityTaskListingRequest request = EntityTaskListingRequest.builder()
                .entityId(entityId)
                .pageNo(1)
                .pageSize(100)
                .build();
        when(taskDiscoveryService.getAllEntityActiveTasks(any()))
                .thenReturn(expectedResponse);
        GenericResponse<TaskSearchResponse> actualResponse1 = taskDiscoveryResource.getTaskListForEntity(Optional.empty(), request);
        //Assert
        Assert.assertTrue(actualResponse1.isSuccess());
        assertSame(expectedResponse.getTaskList(), actualResponse1.getData().getTaskList());
    }


    @Test
    public void getAgentStatsPOST() {
        //Arrange
        String actor = "actor";
        TaskStatsRequest request = TaskStatsRequest.builder()
                .assignedTo("Mohit")
                .build();
        UserDetails userDetails = getUserDetails();
        UserStatsAndFilters expectedResponse = UserStatsAndFilters.builder().build();
        when(taskDiscoveryService.getUserStats(request))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<UserStatsAndFilters> actualResponse = taskDiscoveryResource.getUserStats(Optional.empty(), userDetails, null, request, null);

        //Assert
        Assert.assertTrue(actualResponse.isSuccess());
        assertSame(expectedResponse,actualResponse.getData());
    }

    @Test
    public void getEntityStats() {
        //Arrange
        String entityId = "entityId";
        UserDetails userDetails = getUserDetails();
        EntityStatsResponse expectedResponse = EntityStatsResponse.builder().build();
        when(taskDiscoveryService.getEntityStats(userDetails.getExternalReferenceId(),entityId))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<EntityStatsResponse> actualResponse = taskDiscoveryResource.getEntityStats(Optional.empty(), entityId,userDetails, null, null);

        //Assert
        Assert.assertTrue(actualResponse.isSuccess());
        assertSame(expectedResponse,actualResponse.getData());
    }

    @Test
    public void getFilterOptions() {
        //Arrange
        ViewWiseFilters expectedAssignedFilters = ViewWiseFilters.builder().build();
        ViewWiseFilters expectedDiscoveryFilters = ViewWiseFilters.builder().build();
        when(taskDiscoveryService.getTaskFilterOptions(TaskSearchRequestType.ASSIGNED_VIEW))
                .thenReturn(expectedAssignedFilters);
        when(taskDiscoveryService.getTaskFilterOptions(TaskSearchRequestType.DISCOVERY_VIEW))
                .thenReturn(expectedDiscoveryFilters);

        //Call
        GenericResponse<ViewWiseFilters> actualResponseAssigned = taskDiscoveryResource.getFilterOptions(Optional.empty(), TaskSearchRequestType.ASSIGNED_VIEW);
        GenericResponse<ViewWiseFilters> actualResponseDiscovery = taskDiscoveryResource.getFilterOptions(Optional.empty(), TaskSearchRequestType.DISCOVERY_VIEW);


        //Assert
        Assert.assertTrue(actualResponseAssigned.isSuccess());
        assertSame(expectedAssignedFilters,actualResponseAssigned.getData());
        Assert.assertTrue(actualResponseDiscovery.isSuccess());
        assertSame(expectedDiscoveryFilters,actualResponseDiscovery.getData());
    }


    @Test
    public void getTaskList() {
        //Request Data

        UserDetails userDetails = getUserDetails();
        AssignedSectorMapViewTaskSearchRequest sectorTaskSearchRequest = AssignedSectorMapViewTaskSearchRequest.builder().build();
        sectorTaskSearchRequest.setPageNo(1);
        sectorTaskSearchRequest.setPageSize(10);
        sectorTaskSearchRequest.setSectorId(SECTOR_ID);
        SectorTaskSearchResponse expectedResponse = SectorTaskSearchResponse.builder().build();
        when(taskDiscoveryService.getTaskList(userDetails.getExternalReferenceId(), sectorTaskSearchRequest))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<SectorTaskSearchResponse> actualResponse = taskDiscoveryResource.getTasksForMapView(Optional.empty(), userDetails, null, sectorTaskSearchRequest, null);

        //Assertion
        Assert.assertTrue(actualResponse.isSuccess());
        assertSame(expectedResponse,actualResponse.getData());
    }


    @Test
    public void escalationViewTestCase() {
        //Request Data

        UserDetails userDetails = getUserDetails();
        EscalatedViewTaskSearchRequest escalatedViewTaskSearchRequest = EscalatedViewTaskSearchRequest.builder()
                .build();

        TaskSearchResponse expectedResponse = TaskSearchResponse.builder().build();
        when(taskDiscoveryService.search(userDetails.getExternalReferenceId(), escalatedViewTaskSearchRequest))
                .thenReturn(expectedResponse);

        //Call
        GenericResponse<TaskSearchResponse> actualResponse = taskDiscoveryResource.escalationSearchTask(Optional.empty(),
                userDetails, null, escalatedViewTaskSearchRequest, null, "TOKEN");

        //Assertion
        Assert.assertTrue(actualResponse.isSuccess());
        assertSame(expectedResponse,actualResponse.getData());
    }

    @Test
    public void getServiceBaseTasks() {
        when(taskDiscoveryService.getServiceBaseTasks(ENTITY_ID))
                .thenReturn(List.of(DiscoveryTaskInstance.builder().entityId(ENTITY_ID).build()));
        GenericResponse<List<DiscoveryTaskInstance>> actualResponse = taskDiscoveryResource.getServiceBaseTasks(Optional.empty(), ENTITY_ID);
        Assert.assertEquals(ENTITY_ID, actualResponse.getData().get(0).getEntityId());
    }

    @Test
    public void getTaskDetails() {
        TaskDetailResponse expectedResponse = TaskDetailResponse.builder().description("ABC").build();
        when(taskDiscoveryService.getTaskDetailsById(any())).thenReturn(expectedResponse);
        GenericResponse<TaskDetailResponse> actualResponse = taskDiscoveryResource.getTaskDetails(Optional.empty(), "TID1314151");
        Assert.assertEquals("ABC",actualResponse.getData().getDescription());
    }

    @Test
    public void getOpenTasksStats() {
        List<TaskStateStatsByAction> taskStateStatsByActionList = new ArrayList<>();
        when(taskDiscoveryService.getTasksStateStatsByAction(any())).thenReturn(taskStateStatsByActionList);
        TasksStatsRequest tasksStatsRequest = new TasksStatsRequest();
        GenericResponse<List<TaskStateStatsByAction>> actualResponse = taskDiscoveryResource.getTasksStateStats(null, tasksStatsRequest);
        assertSame(taskStateStatsByActionList, actualResponse.getData());
    }

    @Test
    public void fetchAgentsEligibleForTask_Success() {
        UserDetails userDetails = getUserDetails();
        AgentTaskEligibilityRequest agentTaskEligibilityRequest = AgentTaskEligibilityRequest.builder().taskInstanceId("task-instance-id").build();
        val expected = AgentTaskEligibilityResponse.builder().build();
        when(taskDiscoveryService.fetchAgentsEligibleForTask(userDetails.getExternalReferenceId(), agentTaskEligibilityRequest)).thenReturn(AgentTaskEligibilityResponse.builder().build());
        val actual = taskDiscoveryResource.fetchAgentsEligibleForTask(userDetails, null, agentTaskEligibilityRequest, null);
        Assertions.assertEquals(expected, actual.getData());
    }

}