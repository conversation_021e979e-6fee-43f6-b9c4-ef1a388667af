package com.phonepe.merchant.legion.tasks.actions.validators;

import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.UnknownValidationConfig;

import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class UnknownValidatorTest {

    @InjectMocks
    private  UnknownValidator<TaskAssignRequest> unknownValidator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void test_Success() {
        UnknownValidationConfig unknownValidationConfig = new UnknownValidationConfig();
        unknownValidator.validate(EntityType.TASK, unknownValidationConfig);
        ValidatorResponse expectedResponse = ValidatorResponse.builder().validated(true)
                .build();
        ValidatorResponse actualResponse = unknownValidator.validate(new TaskAssignRequest(), unknownValidationConfig);
        Assertions.assertEquals(expectedResponse, actualResponse);
    }

}