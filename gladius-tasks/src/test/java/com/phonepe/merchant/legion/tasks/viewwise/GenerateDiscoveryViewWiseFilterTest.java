package com.phonepe.merchant.legion.tasks.viewwise;

import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateDiscoveryViewWiseFilters;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType.DISCOVERY_VIEW;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasks;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;


 class GenerateDiscoveryViewWiseFilterTest extends LegionTaskBaseTest {

    private static GenerateDiscoveryViewWiseFilters generateDiscoveryViewWiseFilters;

    @BeforeEach
    public void setUp()  {
        generateDiscoveryViewWiseFilters = new GenerateDiscoveryViewWiseFilters();
        System.out.println("Method called");
    }

    @Test
     void testDiscoveryViewTaskFilterOptions(){
        TaskFilters taskFilters = getFiltersOnTasks();
        ViewWiseFilters expectedDiscoveryFilters = getFiltersOnTasks().getDiscoveryFilterOptions();
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        List<FilterOptions> filterList = new ArrayList<>();
        filterOptions.put(OBJECTIVES, filterList);
        filterOptions.put(POINTS, filterList);
        filterOptions.put(ACTION_ID, filterList);
        ViewWiseFilters filters = generateDiscoveryViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions,DISCOVERY_VIEW );
        Assertions.assertEquals(expectedDiscoveryFilters, filters);
    }

}
