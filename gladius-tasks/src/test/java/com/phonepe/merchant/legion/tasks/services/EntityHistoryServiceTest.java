package com.phonepe.merchant.legion.tasks.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.gladius.models.tasks.enums.ChangeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskHistoryRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.response.EntityTaskHistoryResponse;
import com.phonepe.merchant.gladius.models.tasks.response.GroupedResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceAuditResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.converters.TaskInstanceAuditConverter;
import com.phonepe.merchant.legion.tasks.repository.CommentsOnTaskRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.services.impl.EntityHistoryServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDiscoveryServiceImpl;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.phonepe.merchant.gladius.models.tasks.response.GroupedResponse.groupBy;
import static com.phonepe.merchant.legion.core.utils.DateUtils.convertDate;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.DATE_FORMAT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class EntityHistoryServiceTest {


    @Mock
    private TaskDiscoveryServiceImpl taskDiscoveryService;

    @Mock
    private TaskInstanceRepository taskInstanceRepository;

    @Mock
    private CommentsOnTaskRepository commentsOnTaskRepository;

    @Mock
    private TaskInstanceAuditConverter taskInstanceAuditConverter;

    @Mock
    private LegionService legionService;

    private EntityHistoryService entityHistoryService;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        SerDe.init(new ObjectMapper());
        entityHistoryService = new EntityHistoryServiceImpl(taskInstanceRepository, commentsOnTaskRepository, taskInstanceAuditConverter,
                legionService, taskDiscoveryService);
    }


    @Test
    void testEntityTaskHistory() {
        EntityTaskHistoryRequest request = EntityTaskHistoryRequest.builder()
                .sorter(Sorter.UPDATED_AT)
                .entityId("entity-id")
                .actionId("action-id")
                .pageNo(1)
                .pageSize(10).build();
        TaskListingRequest taskListingRequest = TaskListingRequest.builder()
                .entityId(request.getEntityId())
                .requestType(TaskViewRequestType.ENTITY_HISTORY_VIEW)
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
        List<TaskInstanceAuditResponse> expectedAudits = List.of(TaskInstanceAuditResponse.builder().fieldName("field1").changeType(ChangeType.MODIFIED).updatedAt(new Date())
                .updatedById("agent2").build());
        List<GroupedResponse<TaskInstanceAuditResponse>> groupedResponse = groupBy(expectedAudits, (TaskInstanceAuditResponse t) -> convertDate(DATE_FORMAT, t.getUpdatedAt()));
        EntityTaskHistoryResponse response = EntityTaskHistoryResponse.builder()
                .entityId("entity-id").history(groupedResponse).historyCount(3).build();
        when(taskDiscoveryService.getAgentTaskListing("actor", taskListingRequest))
                .thenReturn(TaskSearchResponse.builder().taskList(List.of(getTaskMetaResponse())).build());
        StoredTaskInstance storedTaskInstance1 = StoredTaskInstance.builder().updatedBy("agent1").build();
        StoredTaskInstance storedTaskInstance2 = StoredTaskInstance.builder().updatedBy("agent2").build();
        Audit<StoredTaskInstance> audit1 = new Audit<>(storedTaskInstance1, "v1", RevisionType.ADD, new Date());
        Audit<StoredTaskInstance> audit2 = new Audit<>(storedTaskInstance2, "v2", RevisionType.MOD, new Date());

        List<Audit<StoredTaskInstance>> audits = Arrays.asList(audit1, audit2);

        StoredCommentsOnTask comment1 = StoredCommentsOnTask.builder().content("comment1").createdBy("agent1").updatedBy("agent1").updatedAt(new Date()).build();
        StoredCommentsOnTask comment2 = StoredCommentsOnTask.builder().content("comment2").createdBy("agent2").updatedBy("agent2").updatedAt(new Date()).build();
        List<StoredCommentsOnTask> comments = Arrays.asList(comment1, comment2);

        AgentProfile agentProfile1 = AgentProfile.builder().name("Jane").agentId("agent1").agentType(AgentType.AGENT).build();

        // Mock behavior
        when(taskInstanceRepository.audit(any())).thenReturn(audits);
        when(commentsOnTaskRepository.getFromTaskInstanceId(anyString(), any(Sorter.class), anyInt(), anyInt())).thenReturn(comments);
        when(legionService.getAgentProfile("agent1")).thenReturn(agentProfile1);
        when(legionService.getAgentProfile("agent2")).thenThrow(LegionException.error(CoreErrorCode.INTERNAL_ERROR));
        List<TaskInstanceAuditResponse> mockAuditResponses = Arrays.asList(
                TaskInstanceAuditResponse.builder().fieldName("field1").changeType(ChangeType.MODIFIED).updatedAt(new Date())
                        .updatedById("agent2")
                        .build()
        );
        when(taskInstanceAuditConverter.convert(any(), any(), any(), any(), any())).thenReturn(mockAuditResponses);


        EntityTaskHistoryResponse actual = entityHistoryService.getEntityTaskHistory("actor", request);
        Assertions.assertEquals(response.getHistory().size(), actual.getHistory().size());
        Assertions.assertEquals(response.getHistoryCount(), actual.getHistoryCount());

    }

    @Test
    void testEntityTaskHistory_EmptyTaskList() {
        EntityTaskHistoryRequest request = EntityTaskHistoryRequest.builder()
                .sorter(Sorter.UPDATED_AT)
                .entityId("entity-id")
                .actionId("action-id")
                .pageNo(1)
                .pageSize(10).build();
        TaskListingRequest taskListingRequest = TaskListingRequest.builder()
                .entityId(request.getEntityId())
                .requestType(TaskViewRequestType.ENTITY_HISTORY_VIEW)
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
        EntityTaskHistoryResponse response = EntityTaskHistoryResponse.builder()
                .entityId("entity-id").historyCount(0).build();
        when(taskDiscoveryService.getAgentTaskListing("actor", taskListingRequest))
                .thenReturn(TaskSearchResponse.builder().taskList(List.of()).build());
        EntityTaskHistoryResponse actual = entityHistoryService.getEntityTaskHistory("actor", request);
        Assertions.assertEquals(response.getHistoryCount(), actual.getHistoryCount());

    }

    @Test
    void testEntityTaskHistory_NotNullCategory() {
        EntityTaskHistoryRequest request = EntityTaskHistoryRequest.builder()
                .sorter(Sorter.UPDATED_AT)
                .entityId("entity-id")
                .actionId("action-id")
                .category(Category.SMARTSPEAKER)
                .pageNo(1)
                .pageSize(10).build();
        TaskListingRequest taskListingRequest = TaskListingRequest.builder()
                .entityId(request.getEntityId())
                .requestType(TaskViewRequestType.ENTITY_HISTORY_VIEW)
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
        List<TaskInstanceAuditResponse> expectedAudits = List.of(TaskInstanceAuditResponse.builder().fieldName("field1").changeType(ChangeType.MODIFIED).updatedAt(new Date())
                .updatedById("agent2").build());
        List<GroupedResponse<TaskInstanceAuditResponse>> groupedResponse = groupBy(expectedAudits, (TaskInstanceAuditResponse t) -> convertDate(DATE_FORMAT, t.getUpdatedAt()));
        EntityTaskHistoryResponse response = EntityTaskHistoryResponse.builder()
                .entityId("entity-id").history(groupedResponse).historyCount(3).build();
        TaskMetaResponse taskMetaResponse = getTaskMetaResponse();
        taskMetaResponse.setCategory(Category.SMARTSPEAKER);
        when(taskDiscoveryService.getAgentTaskListing("actor", taskListingRequest))
                .thenReturn(TaskSearchResponse.builder().taskList(List.of(taskMetaResponse)).build());
        StoredTaskInstance storedTaskInstance1 = StoredTaskInstance.builder().updatedBy("agent1").build();
        StoredTaskInstance storedTaskInstance2 = StoredTaskInstance.builder().updatedBy("agent2").build();
        Audit<StoredTaskInstance> audit1 = new Audit<>(storedTaskInstance1, "v1", RevisionType.ADD, new Date());
        Audit<StoredTaskInstance> audit2 = new Audit<>(storedTaskInstance2, "v2", RevisionType.MOD, new Date());

        List<Audit<StoredTaskInstance>> audits = Arrays.asList(audit1, audit2);

        StoredCommentsOnTask comment1 = StoredCommentsOnTask.builder().content("comment1").createdBy("agent1").updatedBy("agent1").updatedAt(new Date()).build();
        StoredCommentsOnTask comment2 = StoredCommentsOnTask.builder().content("comment2").createdBy("agent2").updatedBy("agent2").updatedAt(new Date()).build();
        List<StoredCommentsOnTask> comments = Arrays.asList(comment1, comment2);

        AgentProfile agentProfile1 = AgentProfile.builder().name("Jane").agentId("agent1").agentType(AgentType.AGENT).build();

        // Mock behavior
        when(taskInstanceRepository.audit(any())).thenReturn(audits);
        when(commentsOnTaskRepository.getFromTaskInstanceId(anyString(), any(Sorter.class), anyInt(), anyInt())).thenReturn(comments);
        when(legionService.getAgentProfile("agent1")).thenReturn(agentProfile1);
        when(legionService.getAgentProfile("agent2")).thenThrow(LegionException.error(CoreErrorCode.INTERNAL_ERROR));
        List<TaskInstanceAuditResponse> mockAuditResponses = Arrays.asList(
                TaskInstanceAuditResponse.builder().fieldName("field1").changeType(ChangeType.MODIFIED).updatedAt(new Date())
                        .updatedById("agent2")
                        .build()
        );
        when(taskInstanceAuditConverter.convert(any(), any(), any(), any(), any())).thenReturn(mockAuditResponses);


        EntityTaskHistoryResponse actual = entityHistoryService.getEntityTaskHistory("actor", request);
        Assertions.assertEquals(response.getHistory().size(), actual.getHistory().size());
        Assertions.assertEquals(response.getHistoryCount(), actual.getHistoryCount());

    }

    private TaskMetaResponse getTaskMetaResponse() {
        return TaskMetaResponse.builder()
                .taskInstanceId("task-instance")
                .description("action")
                .taskType("action-id")
                .entityId("entity-id")
                .category(Category.ALL).build();
    }
}
