package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Optional;
import java.util.function.UnaryOperator;

@RunWith(MockitoJUnitRunner.class)
public class TaskDefinitionRepositoryTest extends LegionTaskBaseTest {

  @Test(expected = Test.None.class)
  public void testSave() {
    StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
            .taskDefinitionId("TASK_DEFINITION_ID_1")
            .actionId("MERCHANT_KYC_VERIFICATION")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .name("FIRST TASK DEFINITION")
            .createdBy("SurajTestUser")
            .priority(Priority.P1)
            .build();
    taskDefinitionRepository.save(storedTask);
  }

  @Test
  public void testGet() {
    StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
            .taskDefinitionId("TASK_DEFINITION_ID_2")
            .actionId("MERCHANT_KYC_VERIFICATION")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .name("FIRST TASK DEFINITION")
            .createdBy("SurajTestUser")
            .priority(Priority.P1)
            .build();
    taskDefinitionRepository.save(storedTask);
    Optional<StoredTaskDefinition> task_definition_id_2 = taskDefinitionRepository.get("TASK_DEFINITION_ID_2");
    Assert.assertTrue(task_definition_id_2.get().getTaskDefinitionId().equals("TASK_DEFINITION_ID_2"));
  }


  @Test
  public void testGetAll() {
    StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
            .taskDefinitionId("TASK_DEFINITION_ID_M")
            .actionId("MERCHANT_KYC_VERIFICATION")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .name("FIRST TASK DEFINITION")
            .createdBy("SurajTestUser")
            .priority(Priority.P1)
            .build();
    taskDefinitionRepository.save(storedTask);
    List<StoredTaskDefinition> task_definition_id_2 = taskDefinitionRepository.getAll();
    Assert.assertNotNull(task_definition_id_2);
    Assert.assertTrue(task_definition_id_2.size()>0);
    Assert.assertEquals(8, task_definition_id_2.size());
  }

  @Test
  public void testUpdate() {
    StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
            .taskDefinitionId("TASK_DEFINITION_ID_3")
            .actionId("MERCHANT_KYC_VERIFICATION")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .name("FIRST TASK DEFINITION")
            .createdBy("SurajTestUser")
            .priority(Priority.P1)
            .build();
    taskDefinitionRepository.save(storedTask);
    taskDefinitionRepository.update("TASK_DEFINITION_ID_3", new UnaryOperator<StoredTaskDefinition>() {
      @Override
      public StoredTaskDefinition apply(StoredTaskDefinition storedTask) {
        storedTask.setPoints(12);
        storedTask.setPriority(Priority.P2);
        return storedTask;
      }
    });
    Optional<StoredTaskDefinition> task_definition_id_2 = taskDefinitionRepository.get("TASK_DEFINITION_ID_3");
    Assert.assertTrue(task_definition_id_2.get().getPriority().equals(Priority.P2));
  }

  @Test
  public void testFetch() {
    StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
            .taskDefinitionId("TASK_DEFINITION_ID_4")
            .actionId("MERCHANT_KYC_VERIFICATION_1")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .name("FIRST TASK DEFINITION")
            .createdBy("SurajTestUser")
            .priority(Priority.P1)
            .build();
    taskDefinitionRepository.save(storedTask);
    List<StoredTaskDefinition> task_definition_list = taskDefinitionRepository.fetchByField(LegionTaskConstants.ACTION_ID,"MERCHANT_KYC_VERIFICATION_1");
    Assert.assertEquals("TASK_DEFINITION_ID_4", task_definition_list.get(0).getTaskDefinitionId());
  }
}