package com.phonepe.merchant.legion.tasks.requestFactory;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.request.userattribute.UserBaseAttribute;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.executors.TaskSearchQueryExecutor;
import com.phonepe.merchant.legion.tasks.utils.ProfileUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

 class RequestSorterFactoryTest extends LegionTaskBaseTest {

    private static TaskSearchQueryExecutor taskSearchQueryExecutor;
    private static LegionService legionService;
    private static SearchResponse searchResponse;
    private static ESRepository esRepository;

    @BeforeEach
    public void setUpTest() {
        System.out.println("Method called");
        esRepository = mock(ESRepository.class);
        searchResponse = mock(SearchResponse.class);
        taskSearchQueryExecutor = mock(TaskSearchQueryExecutor.class);
        legionService = mock(LegionService.class);
    }

    @Test
     void testEnrichAssignedView() {
        AssignedViewTaskFetchRequest request = AssignedViewTaskFetchRequest.builder().build();
        request.setTaskSearchRequestType(TaskSearchRequestType.ASSIGNED_VIEW);
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setPageNo(1);
        request.setPageSize(15);
        request.setSorter(Sorter.DUE_DATE);
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        SearchResponse expectedSearchResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, request.getPageNo(), request.getPageSize(), DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC);
        SearchResponse actualResponse = request.getSorter().accept(request, boolQueryBuilder, taskSearchQueryExecutor);
        Assertions.assertEquals(actualResponse, expectedSearchResponse);
    }

    @Test
     void testEnrichDiscoveryView() {
        DiscoveryViewTaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder().build();
        request.setTaskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW);
        request.setAddLocationCheck(false);
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setSorter(Sorter.DUE_DATE);
        List<UserBaseAttribute> attributes = new ArrayList<>();
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.AGENT).managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).build();
        agentProfile.setAttributes(attributes);
        when(legionService.getAgentProfile(any())).thenReturn(agentProfile);
        BoolQueryBuilder boolQueryBuilder = TaskEsUtils.getTagFilter(ProfileUtils.tagEnricher(agentProfile));
        boolQueryBuilder.filter(TaskEsUtils.getAllowedActionsFilter(List.of()));
        boolQueryBuilder.filter(TaskEsUtils.getDisabledAttributeFilter(AttributeType.OBJECTIVE));
//        boolQueryBuilder.filter(TaskEsUtils.getKycRequiredAndCompletedFilter());
        request.setBoolQueryBuilder(boolQueryBuilder);
        BoolQueryBuilder expectedBoolQueryBuilder = getBoolQuery(filters);
        expectedBoolQueryBuilder.must(boolQueryBuilder);
        SearchResponse expectedResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC);
        SearchResponse actualResponse = request.getSorter().accept(request, expectedBoolQueryBuilder, taskSearchQueryExecutor);
        Assertions.assertEquals(actualResponse, expectedResponse);
    }

    @Test
     void testEnrichSectorAssignedView() {
        SectorAssignedViewTaskSearchRequest request = SectorAssignedViewTaskSearchRequest.builder().build();
        request.setTaskSearchRequestType(TaskSearchRequestType.SECTOR_ASSIGNED_VIEW);
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setPageNo(1);
        request.setPageSize(15);
        request.setSorter(Sorter.DUE_DATE);
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        SearchResponse expectedSearchResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, request.getPageNo(), request.getPageSize(), DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC);
        SearchResponse actualResponse = request.getSorter().accept(request, boolQueryBuilder, taskSearchQueryExecutor);
        Assertions.assertEquals(actualResponse, expectedSearchResponse);
    }


    @Test
     void testEnrichSectorDiscoveryView() {
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder().build();
        request.setTaskSearchRequestType(TaskSearchRequestType.SECTOR_DISCOVERY_VIEW);
        request.setAddLocationCheck(false);
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setSorter(Sorter.DUE_DATE);
        List<UserBaseAttribute> attributes = new ArrayList<>();
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.AGENT).managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).build();
        agentProfile.setAttributes(attributes);
        when(legionService.getAgentProfile(any())).thenReturn(agentProfile);
        BoolQueryBuilder boolQueryBuilder = TaskEsUtils.getTagFilter(ProfileUtils.tagEnricher(agentProfile));
        request.setBoolQueryBuilder(boolQueryBuilder);
        BoolQueryBuilder expectedBoolQueryBuilder = getBoolQuery(filters);
        expectedBoolQueryBuilder.must(boolQueryBuilder);
        SearchResponse expectedResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC);
        SearchResponse actualResponse = request.getSorter().accept(request, expectedBoolQueryBuilder, taskSearchQueryExecutor);
        Assertions.assertEquals(actualResponse, expectedResponse);
    }

}
