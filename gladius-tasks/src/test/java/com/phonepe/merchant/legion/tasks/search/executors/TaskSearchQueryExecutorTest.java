package com.phonepe.merchant.legion.tasks.search.executors;

import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.ArrayList;

import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;

public class TaskSearchQueryExecutorTest extends LegionTaskBaseTest {

    private static TaskSearchQueryExecutor taskSearchQueryExecutor;
    private static ESRepository esRepository;

    private final DiscoveryViewTaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder().pageNo(1)
            .pageSize(15).location(EsLocationRequest.builder().lat(0.0).lon(0.0).build()).build();
    BoolQueryBuilder boolQueryBuilder = TaskEsUtils.getBoolQuery(new ArrayList<>());

    @BeforeClass
    public static void setUpTest() {
        esRepository = Mockito.mock(ESRepository.class);
        taskSearchQueryExecutor = new TaskSearchQueryExecutor(esRepository);
    }

    @Before
    public void after() {
        Mockito.reset(esRepository);
    }


    @Test
    public void testVisitDueDate() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitDueDate(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, SortOrder.ASC);
    }

    @Test
    public void testVisitCreatedAt() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT, SortOrder.DESC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitCreatedAt(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT, SortOrder.DESC);
    }

    @Test
    public void testVisitCreatedAtAsc() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT, SortOrder.ASC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitCreatedAtAsc(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT, SortOrder.ASC);
    }

    @Test
    public void testVisitLocation() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithGeoSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        TaskEsUtils.getGeoSort(request.getLocation())))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitLocation(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithGeoSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                TaskEsUtils.getGeoSort(request.getLocation()));
    }

    @Test
    public void testVisitNone() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.search(TASK_INDEX, boolQueryBuilder, 0, 15))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitNone(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).search(TASK_INDEX, boolQueryBuilder, 0, 15);
    }

    @Test
    public void testVisitPoints() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitPoints(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC);
    }

    @Test
    public void testVisitReschedule() {
        SearchResponse expectedSearchResponse = Mockito.mock(SearchResponse.class);
        Mockito.when(esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                        DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.RESCHEDULED_AT, SortOrder.ASC))
                .thenReturn(expectedSearchResponse);
        SearchResponse actualSearchResponse = taskSearchQueryExecutor.visitReschedule(request, boolQueryBuilder);
        Assertions.assertEquals(expectedSearchResponse, actualSearchResponse);
        Mockito.verify(esRepository).searchWithSorting(TASK_INDEX, boolQueryBuilder, 1, 15,
                DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.RESCHEDULED_AT, SortOrder.ASC);
    }

}
