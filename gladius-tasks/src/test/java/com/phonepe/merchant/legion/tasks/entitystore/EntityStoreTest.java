package com.phonepe.merchant.legion.tasks.entitystore;

import com.phonepe.merchant.gladius.models.entitystore.AgentEntity;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.tasks.DiscoveryTestUtils;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.TaskTestUtils;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.EntityType;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getStoredTaskInstance;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.entityToEntityMeta;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.taskToTaskEntity;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class EntityStoreTest extends LegionTaskBaseTest {

    private static EntityDataFetchVisitor entityDataFetchVisitor;
    private static EntityMetaListFetchVisitor entityMetaListFetchVisitor;
    private static AtlasService atlasService;
    private static EntityStore entityStore;
    private static ESRepository esRepository;
    private static ExternalEntityService externalEntityService;
    private static LegionService profileCRUDService;

    @BeforeClass
    public static void init() {
        atlasService = mock(AtlasService.class);
        esRepository = mock(ESRepository.class);
        externalEntityService = mock(ExternalEntityService.class);
        profileCRUDService = mock(LegionService.class);
        entityDataFetchVisitor = new EntityDataFetchVisitor(merchantService, atlasService, intelService, taskInstanceRepository, externalEntityService, profileCRUDService);
        entityMetaListFetchVisitor = new EntityMetaListFetchVisitor(merchantService, intelService, esRepository, externalEntityService, profileCRUDService);
        entityStore = new EntityStore(appConfig,entityDataFetchVisitor,entityMetaListFetchVisitor);
        when(appConfig.getEntitySourceMetaMapping()).thenReturn(new HashMap<>());
    }

    @Test
    public void getMerchantById() {
        //arrange
        com.phonepe.merchant.gladius.models.entitystore.MerchantEntity expectedResponse = DiscoveryTestUtils.getMerchantEntity();
        MerchantProfile merchantProfile = DiscoveryTestUtils.toMerchantProfile(expectedResponse);
        List<PhysicalStore> physicalStores = Arrays.asList(new PhysicalStore[]{DiscoveryTestUtils.toPhysicalStore(expectedResponse)});
        when(merchantService.getMerchantDetails(merchantProfile.getMerchantId())).thenReturn(merchantProfile);
        when(merchantService.getListOfMerchantStores(merchantProfile.getMerchantId(),1)).thenReturn(physicalStores);
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.MERCHANT)
                .referenceId(expectedResponse.getEntityId())
                .build();

        //call
        com.phonepe.merchant.gladius.models.entitystore.Entity actualResponse = entityStore.getById(request).get();

        //assert
        Assert.assertEquals(expectedResponse,actualResponse);
    }

    @Test(expected = LegionException.class)
    public void getMerchantWithNoStoreById() {
        //arrange
        com.phonepe.merchant.gladius.models.entitystore.MerchantEntity merchantEntity = DiscoveryTestUtils.getMerchantEntity();
        MerchantProfile merchantProfile = DiscoveryTestUtils.toMerchantProfile(merchantEntity);
        when(merchantService.getMerchantDetails(merchantProfile.getMerchantId())).thenReturn(merchantProfile);
        when(merchantService.getListOfMerchantStores(merchantProfile.getMerchantId(),1)).thenReturn(new ArrayList<>());
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.MERCHANT)
                .referenceId(merchantEntity.getEntityId())
                .build();

        //call
        entityStore.getById(request);
    }

    @Test
    public void getStoreById() {
        //arrange
        com.phonepe.merchant.gladius.models.entitystore.StoreEntity expectedResponse = DiscoveryTestUtils.getStoreEntity();
        MerchantProfile merchantProfile = DiscoveryTestUtils.toMerchantProfile(expectedResponse);
        PhysicalStore physicalStore = DiscoveryTestUtils.toPhysicalStore(expectedResponse);
        when(merchantService.getMerchantDetails(merchantProfile.getMerchantId())).thenReturn(merchantProfile);
        when(merchantService.getStoreDetails(merchantProfile.getMerchantId(),physicalStore.getStoreId())).thenReturn(physicalStore);
        when(atlasService.getSectorIdByLatLong(anyDouble(), anyDouble())).thenReturn(Collections.emptyList());
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.STORE)
                .referenceId(expectedResponse.getEntityId())
                .build();

        //call
        com.phonepe.merchant.gladius.models.entitystore.Entity actualResponse = entityStore.getById(request).get();

        //assert
        Assert.assertEquals(expectedResponse,actualResponse);
    }

    @Test(expected = LegionException.class)
    public void getStoreByIdInvalidEntityId() {
        //arrange
        String entityId = "InvalidEntityId";
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.STORE)
                .referenceId(entityId)
                .build();

        //call
        entityStore.getById(request);
    }

    @Test
    public void getSectorById() {
        //arrange
        com.phonepe.merchant.gladius.models.entitystore.SectorEntity expectedResponse = DiscoveryTestUtils.getSectorEntity();
        expectedResponse.setLocation(EsLocationRequest.builder()
                .lat(2.0)
                .lon(2.0)
                .build());
        when(atlasService.getSectorCoordinates(expectedResponse.getSectorId()))
                .thenReturn(TaskTestUtils.getShapeForSectorId());
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.SECTOR)
                .referenceId(expectedResponse.getEntityId())
                .build();

        //call
        com.phonepe.merchant.gladius.models.entitystore.Entity actualResponse = entityStore.getById(request).get();

        //assert
        Assert.assertEquals(expectedResponse.getLocation().getLat() , actualResponse.getLocation().getLat());
        Assert.assertEquals(expectedResponse.getLocation().getLon() , actualResponse.getLocation().getLon());
    }

    @Test(expected = LegionException.class)
    public void getSectorByIdInvalidEntityId() {
        //arrange
        String entityId = "InvalidEntityId";
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.SECTOR)
                .referenceId(entityId)
                .build();
        when(atlasService.getSectorCoordinates(entityId)).thenThrow(
                LegionException.error(CoreErrorCode.NOT_FOUND)
        );

        //call
        entityStore.getById(request);
    }

    @Test
    public void getTaskById() {
        //arrange
        StoredTaskInstance taskInstance = getStoredTaskInstance();
        taskInstance.setCurActor("RANDOM GUY");
        taskInstance = taskInstanceRepository.save(taskInstance);
        com.phonepe.merchant.gladius.models.entitystore.TaskEntity expectedResponse = taskToTaskEntity(taskInstance);
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.TASK)
                .referenceId(expectedResponse.getEntityId())
                .build();

        //call
        com.phonepe.merchant.gladius.models.entitystore.Entity actualResponse = entityStore.getById(request).get();

        //assert
        Assert.assertEquals(expectedResponse,actualResponse);
    }

    @Test(expected = LegionException.class)
    public void getTaskByIdInvalidEntityId() {
        //arrange
        String entityId = "InvalidEntityId";
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.TASK)
                .referenceId(entityId)
                .build();

        //call
        entityStore.getById(request);
    }

    @Test
    public void getVpaById() {
        //arrange
        com.phonepe.merchant.gladius.models.entitystore.VpaEntity expectedResponse = DiscoveryTestUtils.getVpaEntity();
        CompetitionQrResponse competitionQrResponse = DiscoveryTestUtils.toCompetitionQrResponse(expectedResponse);
        when(intelService.getVpaDetails(competitionQrResponse.getVpa()))
                .thenReturn(competitionQrResponse);
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.VPA)
                .referenceId(expectedResponse.getEntityId())
                .build();

        //call
        com.phonepe.merchant.gladius.models.entitystore.Entity actualResponse = entityStore.getById(request).get();

        //assert
        Assert.assertEquals(expectedResponse,actualResponse);
    }

    @Test(expected = LegionException.class)
    public void getVpaByIdInvalidEntityId() {
        //arrange
        String entityId = "InvalidEntityId";
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .entityType(EntityType.TASK)
                .referenceId(entityId)
                .build();
        when(intelService.getVpaDetails(entityId)).thenThrow(
                LegionException.error(CoreErrorCode.NOT_FOUND)
        );

        //call
        entityStore.getById(request);
    }

    @Test
    public void getOtherEntitiesById() {
        com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest request = com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest.builder()
                .referenceId("entityId")
                .build();

        //arrange
        request.setEntityType(EntityType.PP_USER);
        //call
        Boolean actualResponse = entityStore.getById(request).isPresent();
        //assert
        Assert.assertFalse(actualResponse);

        //arrange
        request.setEntityType(EntityType.NONE);
        //call
        actualResponse = entityStore.getById(request).isPresent();
        //assert
        Assert.assertFalse(actualResponse);
    }

    @Test
    public void getEntityMetaMap() {
        //arrange
        Map<EntityType, Set<String>> request = new EnumMap<>(EntityType.class);
        Map<EntityType, Set<String>> taskInstanceIdRequest = new EnumMap<>(EntityType.class);
        for (EntityType entityType: EnumSet.allOf(EntityType.class)) {
            request.put(entityType,new HashSet<>());
            taskInstanceIdRequest.put(entityType,new HashSet<>());
        }

        List<com.phonepe.merchant.gladius.models.entitystore.MerchantEntity> merchantEntities = DiscoveryTestUtils.getMerchantEntityList(10);
        List<com.phonepe.merchant.gladius.models.entitystore.StoreEntity> storeEntities = DiscoveryTestUtils.getStoreEntityList(5);
        List<com.phonepe.merchant.gladius.models.entitystore.TaskEntity> taskEntities = DiscoveryTestUtils.getTaskEntityList(3);
        List<com.phonepe.merchant.gladius.models.entitystore.SectorEntity> sectorEntities = DiscoveryTestUtils.getSectorEntityList(3);
        List<com.phonepe.merchant.gladius.models.entitystore.VpaEntity> vpaEntities = DiscoveryTestUtils.getVpaEntityList(3);
        List<com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity> phoneNumberEntities = DiscoveryTestUtils.getPhoneNumberEntityList(1);
        List<AgentEntity> agentEntities = DiscoveryTestUtils.getAgentEntityList(3);

        request.put(EntityType.MERCHANT,merchantEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));
        request.put(EntityType.STORE,storeEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));
        request.put(EntityType.SECTOR,sectorEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));
        request.put(EntityType.TASK,taskEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));
        request.put(EntityType.VPA,vpaEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));
        request.put(EntityType.AGENT,agentEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));
        taskInstanceIdRequest.put(EntityType.PHONE_NUMBER,phoneNumberEntities.stream().map(com.phonepe.merchant.gladius.models.entitystore.Entity::getEntityId).collect(Collectors.toSet()));

        Map<String, com.phonepe.merchant.gladius.models.entitystore.EntityMeta> expectedResponse = new HashMap<>();
        merchantEntities.stream()
                .forEach(merchantEntity -> expectedResponse.put(merchantEntity.getEntityId(),entityToEntityMeta(merchantEntity)));
        storeEntities.stream()
                .forEach(storeEntity -> expectedResponse.put(storeEntity.getEntityId(),entityToEntityMeta(storeEntity)));
        taskEntities.stream()
                .forEach(taskEntity -> expectedResponse.put(taskEntity.getEntityId(),entityToEntityMeta(taskEntity)));
        sectorEntities.stream()
                .forEach(sectorEntity -> expectedResponse.put(sectorEntity.getEntityId(),entityToEntityMeta(sectorEntity)));
        vpaEntities.stream()
                .forEach(vpaEntity -> expectedResponse.put(vpaEntity.getEntityId(),entityToEntityMeta(vpaEntity)));
        agentEntities.stream()
                .forEach(agentEntity -> expectedResponse.put(agentEntity.getEntityId(),entityToEntityMeta(agentEntity)));

        when(merchantService.getListOfStores(any()))
                .thenReturn(DiscoveryTestUtils.getTaskStoreMetaList(storeEntities));
        when(merchantService.getListOfMerchants(any()))
                .thenReturn(DiscoveryTestUtils.getTaskMerchantMetaList(merchantEntities));
        when(esRepository.search(eq(TASK_INDEX),any(),anyInt(),anyInt(), eq(DiscoveryTaskInstance.class)))
                .thenReturn(DiscoveryTestUtils.getDiscoveryTaskInstanceList(taskEntities));
        when(intelService.getListOfVpas(any()))
                .thenReturn(DiscoveryTestUtils.getTaskVpaMetaList(vpaEntities));
        when(profileCRUDService.getAgentsMeta(any()))
                .thenReturn(DiscoveryTestUtils.getAgentProfilesList(agentEntities));

        //call
        Map<String, com.phonepe.merchant.gladius.models.entitystore.EntityMeta> actualResponse = entityStore.getEntityMetaMap(request, taskInstanceIdRequest);

        //assert
        Assert.assertEquals(expectedResponse,actualResponse);
    }

}
