package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAttributeCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.services.impl.TaskAttributeServiceImpl;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;

import java.util.List;

import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class TaskAttributeServiceTest extends LegionTaskBaseTest {

    private static TaskAttributeService taskAttributeService;
    private static TaskAttributeCreateRequest taskAttributeCreateRequest;

    @BeforeClass
    public static void init() {
        taskAttributeService = new TaskAttributeServiceImpl(taskAttributeRepository, mock(FoxtrotEventIngestionService.class), cacheUtils);
        taskAttributeCreateRequest = TaskAttributeCreateRequest.builder()
                .attributeType(AttributeType.OBJECTIVE)
                .attributeValue("SARANSH")
                .name("TEsT")
                .build();
        taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, "saransh");
    }

    @Test
    public void getAttributeTest() {

        TaskAttributeInstance taskAttributeInstance = taskAttributeService.getFromCache("SARANSH");

        Assert.assertEquals(taskAttributeCreateRequest.getAttributeValue(), taskAttributeInstance.getTaskattributeValue());
    }

    @Test
    public void saveOrUpdateTest() {
        taskAttributeCreateRequest.setName("changed name");
        TaskAttributeInstance taskAttributeInstance = taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, "saransh");
        Assert.assertEquals("changed name", taskAttributeInstance.getName());
    }

    @Test(expected = LegionException.class)
    public void getAttributeNotFoundTest() {
        taskAttributeService.getFromCache("ABSENT");
    }

    @Test
    public void getAllAttributeTest() {
        List<TaskAttributeInstance> expectedOutput = taskAttributeService.getAllAttributes();
        Assert.assertNotNull(expectedOutput);
    }

    @Test
    public void getFromDBTest() {
        taskAttributeService.saveOrUpdate(TaskAttributeCreateRequest.builder().attributeType(AttributeType.OBJECTIVE).name("Xyz").attributeValue("XYZ").build(), "me");
        TaskAttributeInstance expectedOutput = taskAttributeService.getFromDB("XYZ");
        Assert.assertNotNull(expectedOutput);
    }

    @Test(expected = LegionException.class)
    public void getFromDBNotFoundTest() {
        taskAttributeService.saveOrUpdate(TaskAttributeCreateRequest.builder().attributeType(AttributeType.OBJECTIVE).name("Xyz").attributeValue("XYZ").build(), "me");
        TaskAttributeInstance expectedOutput = taskAttributeService.getFromDB("XYZ_NF");
        Assert.assertNotNull(expectedOutput);
    }
}
