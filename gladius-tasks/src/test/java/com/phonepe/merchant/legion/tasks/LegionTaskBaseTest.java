package com.phonepe.merchant.legion.tasks;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.health.HealthCheckRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.hystrix.configurator.config.HystrixConfig;
import com.hystrix.configurator.core.HystrixConfigurationFactory;
import com.phonepe.frontend.chimera.lite.ChimeraLite;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFeedback;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFormConfig;
import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredActionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCampaign;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredDefinitionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAttribute;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.daos.AuditDao;
import com.phonepe.merchant.legion.core.daos.AuditDaoImpl;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.flows.factories.LegionTransitionManager;
import com.phonepe.merchant.legion.core.flows.factories.LegionTransitionManagerImpl;
import com.phonepe.merchant.legion.core.models.ChimeraLiteConfig;
import com.phonepe.merchant.legion.core.models.GladiusConfig;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.BrickbatService;
import com.phonepe.merchant.legion.core.services.ClockWorkService;
import com.phonepe.merchant.legion.core.services.SeerService;
import com.phonepe.merchant.legion.core.services.FortunaService;
import com.phonepe.merchant.legion.core.services.FoxtrotService;
import com.phonepe.merchant.legion.core.services.GeminiService;
import com.phonepe.merchant.legion.core.services.HermodService;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.MerchantOnboardingService;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.core.services.OdinService;
import com.phonepe.merchant.legion.core.services.ParadoxService;
import com.phonepe.merchant.legion.core.services.TmsService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionVerificationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.ActionVerificationProcessorImpl;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.AssignmentActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.cache.CampaignQuestionsCache;
import com.phonepe.merchant.legion.tasks.cache.FormConfigCache;
import com.phonepe.merchant.legion.tasks.cache.TaskActionCache;
import com.phonepe.merchant.legion.tasks.cache.TaskActionIdsCache;
import com.phonepe.merchant.legion.tasks.cache.TaskAttributeCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionCache;
import com.phonepe.merchant.legion.tasks.cache.TaskFiltersCache;
import com.phonepe.merchant.legion.tasks.flows.TaskEngine;
import com.phonepe.merchant.legion.tasks.flows.TaskEngineImpl;
import com.phonepe.merchant.legion.tasks.flows.TransitionValidator;
import com.phonepe.merchant.legion.tasks.flows.Validations;
import com.phonepe.merchant.legion.tasks.flows.transitions.AvailableTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.CreateTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.DeletedTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.ExpiredTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.ManualVerificationTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.SelfAssignTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.StartTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.repository.CampaignRepository;
import com.phonepe.merchant.legion.tasks.repository.FeedbackRepository;
import com.phonepe.merchant.legion.tasks.repository.FormConfigRepository;
import com.phonepe.merchant.legion.tasks.repository.StoredTagRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskAttributeRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskTransitionRepository;
import com.phonepe.merchant.legion.tasks.repository.impl.CampaignRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.FeedbackRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.FormConfigRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.StoredTagRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskActionRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskAttributeRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskDefinitionRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskInstanceRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskTransitionRepositoryImpl;
import com.phonepe.merchant.legion.tasks.search.executors.TaskSearchQueryExecutor;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.DiscoveryViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.SectorDiscoveryViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.services.CampaignService;
import com.phonepe.merchant.legion.tasks.services.FeedbackService;
import com.phonepe.merchant.legion.tasks.services.FormConfigService;
import com.phonepe.merchant.legion.tasks.services.SectorStatsService;
import com.phonepe.merchant.legion.tasks.services.TagService;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskDuplicateValidationService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskVerificationService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.services.impl.CampaignServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.FeedbackServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.FormConfigServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskAttributeServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDuplicateValidationServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskVerificationServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.merchant.legion.tasks.utils.IdUtils;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.config.ShardedHibernateFactory;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import io.appform.dropwizard.sharding.sharding.impl.ConsistentHashBucketIdExtractor;
import io.appform.dropwizard.sharding.utils.ShardCalculator;
import io.dropwizard.Configuration;
import io.dropwizard.db.DataSourceFactory;
import io.dropwizard.jersey.DropwizardResourceConfig;
import io.dropwizard.jersey.setup.JerseyEnvironment;
import io.dropwizard.lifecycle.setup.LifecycleEnvironment;
import io.dropwizard.setup.AdminEnvironment;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import org.junit.BeforeClass;
import org.mockito.Mockito;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.core.cache.CacheName.AGENT_ACTION;
import static com.phonepe.merchant.legion.core.cache.CacheName.CAMPAIGN_QUESTIONS;
import static com.phonepe.merchant.legion.core.cache.CacheName.FORM_CONFIG;
import static com.phonepe.merchant.legion.core.cache.CacheName.TASK_ACTION_IDS;
import static com.phonepe.merchant.legion.core.cache.CacheName.TASK_ATTRIBUTES;
import static com.phonepe.merchant.legion.core.cache.CacheName.TASK_DEFINITION;
import static com.phonepe.merchant.legion.core.cache.CacheName.TASK_FILTERS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public abstract class LegionTaskBaseTest {

    protected static class DBConfig extends Configuration {
        protected ShardedHibernateFactory shards = new ShardedHibernateFactory();
    }

    private static final DBConfig dbConfig = new DBConfig();
    private static final HealthCheckRegistry healthChecks = mock(HealthCheckRegistry.class);
    private static JerseyEnvironment jerseyEnvironment;
    private static final LifecycleEnvironment lifecycleEnvironment = mock(LifecycleEnvironment.class);
    private static final Environment environment = mock(Environment.class);
    protected static AdminEnvironment admin = mock(AdminEnvironment.class);
    protected static final MetricRegistry metricRegistry = new MetricRegistry();
    protected static final CacheUtils cacheUtils = new CacheUtils();
    private static final Bootstrap<?> bootstrap = mock(Bootstrap.class);

    protected static final GladiusConfig gladiusConfig = GladiusConfig.builder()
            .env("test")
            .namespace("phonepe")
            .rangerName("legion")
            .build();
    protected static TaskTransitionRepository taskTransitionRepository;
    protected static TaskInstanceRepository taskInstanceRepository;
    protected static TaskActionRepository taskActionRepository;
    protected static TaskActionService taskActionService;
    protected static TaskDefinitionRepository taskDefinitionRepository;
    protected static TaskEngine taskEngine;
    protected static StoredTagRepository storedTagRepository;
    protected static LegionTransitionManager legionTransitionManager;
    protected static FoxtrotEventExecutor eventExecutor;
    protected static CreateTaskLegionTransition createTaskLegionTransition;
    protected static ExpiredTaskLegionTransition expiredTaskLegionTransition;
    protected static StartTaskLegionTransition startTaskLegionTransition;
    protected static DeletedTaskLegionTransition deletedTaskLegionTransition;
    protected static SelfAssignTaskLegionTransition selfAssignTaskLegionTransition;
    protected static AvailableTaskLegionTransition availableTaskLegionTransition;
    protected static TaskInstanceManagementService taskInstanceManagementService;
    protected static RelationalDao<StoredTaskTransition> transitionRelationalDao;
    protected static RelationalDao<StoreTag> storeTagRelationalDao;
    protected static Map<CacheName, CacheConfig> cacheConfigs = new HashMap<>();
    protected static ActionVerificationProcessor actionVerificationProcessor;
    protected static ActionValidationProcessor<TaskStartRequest> startActionValidationProcessor;
    protected static ManualVerificationTaskLegionTransition manualVerificationTaskLegionTransition;
    protected static ActionValidationProcessor<TaskCompleteRequest> completeActionValidationProcessor;
    protected static ActionValidationProcessor<TaskAssignRequest> assignActionValidationProcessor;
    protected static AssignmentActionValidationProcessor assignActionValidationProcessor1;

    protected static MerchantOnboardingService merchantOnboardingService;
    protected static ClockWorkService clockWorkService;
    protected static TaskVerificationService taskVerificationService;
    protected static FoxtrotEventIngestionService foxtrotEventIngestionService;
    protected static MerchantService merchantService;
    protected static AppConfig appConfig;
    protected static AtlasService atlasService;
    protected static FoxtrotEventIngestionService eventIngestionService;
    protected static FoxtrotService foxtrotService;
    protected static IntelService intelService;
    protected static CampaignRepository campaignRepository;
    protected static CampaignService campaignService;
    protected static TaskActionCache actionCache;
    protected static CampaignQuestionsCache campaignQuestionsCache;
    protected static TaskDefinitionCache taskDefinitionCache;
    protected static TaskFiltersCache taskFiltersCache;
    protected static BrickbatService brickbatService;
    protected static OdinService odinService;
    protected static TaskDiscoveryService taskDiscoveryService = mock(TaskDiscoveryService.class);
    protected static SectorStatsService sectorStatsService = mock(SectorStatsService.class);
    protected static ParadoxService paradoxService;
    protected static SeerService seerService;
    protected static LegionService legionService;
    protected static LookupDao<StoredTaskInstance> storedTaskInstance;
    protected static FortunaService fortunaService;
    protected static TaskAttributeService taskAttributeService;
    protected static TransitionValidator transitionValidator;
    protected static TaskAttributeCache taskAttributeCache;
    protected static TaskActionIdsCache taskActionIdsCache;
    protected static ActionValidationProcessor actionValidationProcessor;
    protected static TaskAttributeRepository taskAttributeRepository;
    protected static HermodService hermodService;
    protected static TaskSearchQueryExecutor taskSearchQueryExecutor;
    protected static DiscoveryViewTaskSearchRequestQueryBuilder discoveryViewTaskSearchRequestQueryBuilder;
    protected static TaskDuplicateValidationService taskDuplicateValidationService;
    protected static ValidationService validationService;
    protected static TaskESRepository taskESRepository;
    protected static Validations validations;
    protected static LeadConfig leadConfig;
    protected static LeadManagementConfiguration leadManagementConfiguration;
    protected static Miscellaneous miscellaneous;
    protected static ChimeraLiteConfig chimeraLiteConfig;
    protected static ChimeraLite chimeraLite;
    protected static ChimeraLiteRepository chimeraLiteRepository;
    protected static SectorDiscoveryViewTaskSearchRequestQueryBuilder sectorDiscoveryViewTaskSearchRequestQueryBuilder;
    protected static TmsService tmsService;
    protected static GeminiService geminiService;
    protected static FormConfigRepository formConfigRepository;
    protected static FeedbackRepository feedbackRepository;
    protected static FormConfigService formConfigService;
    protected static FeedbackService feedbackService;
    protected static FormConfigCache formConfigCache;
    protected static RelationalDao<StoredFormConfig> storedFormConfigRelationalDao;
    protected static RelationalDao<StoredFeedback> storedFeedbackRelationalDao;

    public static ObjectMapper mapper;
    protected static SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

  protected static DBShardingBundle<DBConfig> shardingBundle = new DBShardingBundle<DBConfig>(
          StoredTaskDefinition.class,
          StoredTaskInstance.class,
          StoredTaskTransition.class,
          StoredTaskAction.class,
          StoredCampaign.class,
          StoreTag.class,
          StoredTaskAttribute.class,
          StoredDefinitionAttributeMappings.class,
          StoredActionAttributeMappings.class,
          StoredFormConfig.class,
          StoredFeedback.class
  ) {
    @Override
    protected ShardedHibernateFactory getConfig(DBConfig config) {
      return dbConfig.shards;
    }
  };

    protected static DataSourceFactory createConfig(String dbName) {
        Map<String, String> properties = Maps.newHashMap();
        properties.put("hibernate.dialect", "org.hibernate.dialect.H2Dialect");
        properties.put("hibernate.hbm2ddl.auto", "create");
        DataSourceFactory shard = new DataSourceFactory();
        shard.setDriverClass("org.h2.Driver");
        shard.setUrl("jdbc:h2:mem:" + dbName);
        shard.setValidationQuery("select 1");
        shard.setProperties(properties);
        return shard;
    }

    @BeforeClass
    public static void setup() throws Exception {

        mapper = new ObjectMapper();

        SerDe.init(mapper);
        buildCacheConfig();

        System.setProperty("localConfig", "true");
        jerseyEnvironment = new JerseyEnvironment(null, new DropwizardResourceConfig());
        when(environment.jersey()).thenReturn(jerseyEnvironment);
        when(environment.lifecycle()).thenReturn(lifecycleEnvironment);
        when(environment.healthChecks()).thenReturn(healthChecks);
        when(bootstrap.getHealthCheckRegistry()).thenReturn(healthChecks);
        when(environment.admin()).thenReturn(admin);
        when(environment.metrics()).thenReturn(metricRegistry);
        when(bootstrap.getHealthCheckRegistry()).thenReturn(Mockito.mock(HealthCheckRegistry.class));

        HystrixConfig hystrixConfig = new HystrixConfig();
        hystrixConfig.getDefaultConfig().getThreadPool().setTimeout(10000);
        HystrixConfigurationFactory.init(hystrixConfig);

        Logger root = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
        root.setLevel(Level.INFO);
        dbConfig.shards.setShards(List.of(createConfig("legion"), createConfig("legion")));
        shardingBundle.initialize(bootstrap);
        shardingBundle.initBundles(bootstrap);
        shardingBundle.runBundles(dbConfig, environment);
        shardingBundle.run(dbConfig, environment);
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        storedTaskInstance = shardingBundle.createParentObjectDao(StoredTaskInstance.class);
        transitionRelationalDao = shardingBundle.createRelatedObjectDao(StoredTaskTransition.class);
        storeTagRelationalDao = shardingBundle.createRelatedObjectDao(StoreTag.class);
        LookupDao<StoredTaskDefinition> storedTask = shardingBundle.createParentObjectDao(StoredTaskDefinition.class);
        RelationalDao<StoredTaskDefinition> definitionRelationalDao = shardingBundle.createRelatedObjectDao(StoredTaskDefinition.class);
        LookupDao<StoredTaskAction> storedTaskAction = shardingBundle.createParentObjectDao(StoredTaskAction.class);
        LookupDao<StoreTag> tagLookupDao = shardingBundle.createParentObjectDao(StoreTag.class);
        LookupDao<StoredCampaign> campaignLookupDao = shardingBundle.createParentObjectDao(StoredCampaign.class);
        AuditDao<StoredTaskInstance> auditDao = new AuditDaoImpl<>(shardingBundle.getSessionFactories(), StoredTaskInstance.class, new ShardCalculator<>(shardingBundle.getShardManager(), new ConsistentHashBucketIdExtractor<>(shardingBundle.getShardManager())));
        taskActionRepository = new TaskActionRepositoryImpl(storedTaskAction, eventIngestionService);
        taskESRepository = mock(TaskESRepository.class);
        taskInstanceRepository = new TaskInstanceRepositoryImpl(storedTaskInstance, auditDao, eventIngestionService, new IdUtils());
        merchantOnboardingService = mock(MerchantOnboardingService.class);
        taskTransitionRepository = new TaskTransitionRepositoryImpl(transitionRelationalDao, storedTaskInstance, eventIngestionService);
        taskDefinitionRepository = new TaskDefinitionRepositoryImpl(storedTask, eventIngestionService);
        campaignRepository = new CampaignRepositoryImpl(campaignLookupDao, eventIngestionService);
        storedTagRepository = new StoredTagRepositoryImpl(tagLookupDao, storeTagRelationalDao, foxtrotEventIngestionService);
        taskEngine = mock(TaskEngineImpl.class);
        brickbatService = mock(BrickbatService.class);
        legionService = mock(LegionService.class);
        taskFiltersCache = new TaskFiltersCache(cacheConfigs, () -> taskDiscoveryService, metricRegistry, cacheUtils);
        taskActionIdsCache = new TaskActionIdsCache(cacheConfigs, () -> taskActionRepository, metricRegistry, cacheUtils);
        actionCache = new TaskActionCache(cacheConfigs, () -> taskActionRepository, metricRegistry, cacheUtils);
        taskDefinitionCache = new TaskDefinitionCache(cacheConfigs, () -> taskDefinitionRepository, metricRegistry, cacheUtils);
        campaignQuestionsCache = new CampaignQuestionsCache(cacheConfigs, () -> brickbatService, metricRegistry, cacheUtils);
        legionTransitionManager = mock(LegionTransitionManagerImpl.class);
        eventExecutor = mock(FoxtrotEventExecutor.class);
        createTaskLegionTransition = mock(CreateTaskLegionTransition.class);
        expiredTaskLegionTransition = mock(ExpiredTaskLegionTransition.class);
        taskInstanceManagementService = mock(TaskInstanceManagementServiceImpl.class);
        taskVerificationService = mock(TaskVerificationServiceImpl.class);
        clockWorkService = mock(ClockWorkService.class);
        actionVerificationProcessor = mock(ActionVerificationProcessorImpl.class);
        startActionValidationProcessor = mock(ActionValidationProcessor.class);
        completeActionValidationProcessor = mock(ActionValidationProcessor.class);
        assignActionValidationProcessor = mock(ActionValidationProcessor.class);
        merchantService = mock(MerchantService.class);
        validations = mock(Validations.class);
        atlasService = mock(AtlasService.class);
        appConfig = mock(AppConfig.class);
        leadConfig = mock(LeadConfig.class);
        miscellaneous = mock(Miscellaneous.class);
        geminiService = mock(GeminiService.class);
        leadManagementConfiguration = LeadManagementConfiguration.create(List.of(
                ActionToRemarkConfig.builder()
                        .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
                        .taskType(UserGenTaskType.USER_CREATED_SS_DEPLOYMENT)
                        .config(List.of(IntentWithRemarks.builder()
                                        .allowMarkTaskComplete(true)
                                        .intent("NOT_INTERESTED")
                                .build()))
                        .build(),
                ActionToRemarkConfig.builder()
                        .actionId("PHONEPE_MERCHANT_LENDING")
                        .taskType(UserGenTaskType.USER_CREATED_LENDING_DEPLOYMENT)
                        .config(List.of(IntentWithRemarks.builder()
                                .allowMarkTaskComplete(true)
                                .intent("NOT_INTERESTED")
                                .build()))
                        .build()
        ), List.of());
        foxtrotService = mock(FoxtrotService.class);
        intelService = mock(IntelService.class);
        taskAttributeCache = new TaskAttributeCache(cacheConfigs, () -> taskAttributeRepository, metricRegistry, cacheUtils);
        taskActionService = new TaskActionServiceImpl(taskActionRepository, () -> actionVerificationProcessor,
                startActionValidationProcessor, completeActionValidationProcessor, cacheUtils, taskAttributeCache, taskActionIdsCache);
        transitionValidator = mock(TransitionValidator.class);
        campaignService = new CampaignServiceImpl(campaignRepository, eventExecutor, mock(TagService.class));
        paradoxService = mock(ParadoxService.class);
        fortunaService = mock(FortunaService.class);
        hermodService = mock(HermodService.class);
        validationService = mock(ValidationService.class);
        LookupDao<StoredTaskAttribute> taskAttributeLookupDao = shardingBundle.createParentObjectDao(StoredTaskAttribute.class);
        taskAttributeRepository = new TaskAttributeRepositoryImpl(taskAttributeLookupDao, eventIngestionService);
        taskAttributeService =  new TaskAttributeServiceImpl(taskAttributeRepository, eventIngestionService, mock(CacheUtils.class));
        actionValidationProcessor = mock(ActionValidationProcessor.class);
        taskDuplicateValidationService = mock(TaskDuplicateValidationServiceImpl.class);
        SerDe.init(mapper);
        taskSearchQueryExecutor = mock(TaskSearchQueryExecutor.class);
        discoveryViewTaskSearchRequestQueryBuilder = mock(DiscoveryViewTaskSearchRequestQueryBuilder.class);
        sectorDiscoveryViewTaskSearchRequestQueryBuilder = mock(SectorDiscoveryViewTaskSearchRequestQueryBuilder.class);

        chimeraLiteRepository = mock(ChimeraLiteRepository.class);
        storedFormConfigRelationalDao = shardingBundle.createRelatedObjectDao(StoredFormConfig.class);
        storedFeedbackRelationalDao = shardingBundle.createRelatedObjectDao(StoredFeedback.class);
        formConfigRepository = new FormConfigRepositoryImpl(storedFormConfigRelationalDao, eventIngestionService);
        feedbackRepository = new FeedbackRepositoryImpl(storedFeedbackRelationalDao, eventIngestionService);
        formConfigCache = new FormConfigCache(cacheConfigs, () -> formConfigRepository, metricRegistry, cacheUtils);
        formConfigService = mock(FormConfigServiceImpl.class);
        feedbackService = mock(FeedbackServiceImpl.class);
        sectorGeofenceQueryBuilder = mock(SectorGeofenceQueryBuilder.class);
        TaskEsUtils.init(sectorGeofenceQueryBuilder);
    }

  static void buildCacheConfig(){
    cacheConfigs.put(AGENT_ACTION, CacheConfig.builder()
            .expiryInSeconds(1_000)
            .refreshInSeconds(1_000)
            .concurrency(5)
            .maxElements(5)
            .build());
    cacheConfigs.put(CAMPAIGN_QUESTIONS, CacheConfig.builder()
            .expiryInSeconds(1_000)
            .refreshInSeconds(1_000)
            .concurrency(5)
            .maxElements(5)
            .build());
    cacheConfigs.put(TASK_FILTERS, CacheConfig.builder()
            .expiryInSeconds(1_000)
            .refreshInSeconds(1_000)
            .concurrency(5)
            .maxElements(15)
            .build());
    cacheConfigs.put(TASK_ATTRIBUTES, CacheConfig.builder()
            .expiryInSeconds(1_000)
            .refreshInSeconds(1_000)
            .concurrency(5)
            .maxElements(15)
            .build());
    cacheConfigs.put(TASK_DEFINITION, CacheConfig.builder()
            .expiryInSeconds(1_000)
            .refreshInSeconds(1_000)
            .concurrency(5)
            .maxElements(5)
            .build());
    cacheConfigs.put(TASK_ACTION_IDS, CacheConfig.builder()
            .expiryInSeconds(1_000)
            .refreshInSeconds(1_000)
            .concurrency(5)
            .maxElements(5)
            .build());
      cacheConfigs.put(FORM_CONFIG, CacheConfig.builder()
              .expiryInSeconds(1_000)
              .refreshInSeconds(1_000)
              .concurrency(5)
              .maxElements(5)
              .build());
  }
}
