package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tags.TagRequest;
import com.phonepe.merchant.gladius.models.tags.TagResponse;
import com.phonepe.merchant.gladius.models.tags.TagsTenant;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.flows.TagTenantValidator;
import com.phonepe.merchant.legion.tasks.services.impl.TagServiceImpl;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import static org.mockito.Mockito.mock;


public class TagServiceTest extends LegionTaskBaseTest {

    private static TagService tagService;
    private static TagTenantValidator tagTenantValidator;
    @BeforeClass
    public static void init() {
        StoreTag storeTag = StoreTag.builder()
                .tagId("service")
                .tenantId("campaign")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        tagTenantValidator = mock(TagTenantValidator.class);
        tagService = new TagServiceImpl(storedTagRepository, mock(FoxtrotEventExecutor.class), tagTenantValidator);
    }


    @Test
    public void getTag(){
        TagResponse tagResponse = tagService.getTag("service");
        Assert.assertTrue(tagResponse.isActive());
        Assert.assertEquals("campaign", tagResponse.getTenantId());
    }


    @Test
    public void getTagsForTenant(){
        List<StoreTag> storeTags = tagService.getTagsForTenant("campaign", true);
        Assert.assertTrue(storeTags.size()>0);
        Assert.assertEquals("campaign", storeTags.get(0).getTenantId());
    }

    @Test
    public void getTagsFromDifferentTenants(){
        Set<String> tags = tagService.getTagsFromDifferentTenants(Collections.singletonList("campaign"));
        List<String> tagsList = new LinkedList<>(tags);
        Assert.assertTrue(tags.size()>0);
        Assert.assertEquals("service", tagsList.get(0));
    }


    @Test
    public void saveTag(){
        TagRequest tagRequest = TagRequest.builder()
                .tagId("tagService")
                .tenantId("CAMPAIGN")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("its mohit bro")
                .updatedBy("Its mohit bro")
                .build();
        TagResponse tagResponse = tagService.saveTag(tagRequest);
        Assert.assertEquals("tagService", tagResponse.getTagId());
    }

    @Test(expected = LegionException.class)
    public void saveDuplicateTag(){
        TagRequest tagRequest = TagRequest.builder()
                .tagId("tagService1")
                .tenantId("CAMPAIGN")
                .tenantType(TagsTenant.CAMPAIGN)
                .createdBy("its mohit bro")
                .active(true)
                .updatedBy("Its mohit bro")
                .build();
        tagService.saveTag(tagRequest);
        tagRequest = TagRequest.builder()
                .tagId("tagService1")
                .tenantId("CAMPAIGN")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("its mohit bro")
                .updatedBy("Its mohit bro")
                .build();
        tagService.saveTag(tagRequest);
    }

    @Test
    public void getTenantTag(){
        StoreTag  tag = tagService.getTenantTag("campaign", "service");
        Assert.assertNotNull(tag);
        Assert.assertEquals(TagsTenant.CAMPAIGN, tag.getTenantType());
    }

    @Test
    public void toggleActive(){

        tagService.toggleActive( "service", "campaign");
        StoreTag storeTag = tagService.getTenantTag("campaign", "service");
        Assert.assertFalse(storeTag.isActive());
    }
}
