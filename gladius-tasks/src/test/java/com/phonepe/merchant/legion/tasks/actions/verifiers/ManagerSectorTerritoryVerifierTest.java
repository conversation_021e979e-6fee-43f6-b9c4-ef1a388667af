package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.ManagerSectorTerritoryVerifierConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ManagerSectorTerritoryVerifierTest {

    private LegionService legionService;
    private AtlasService atlasService;
    private ManagerSectorTerritoryVerifier verifier;
    private ManagerSectorTerritoryVerifierConfig config;

    @BeforeEach
    void setUp() {
        legionService = Mockito.mock(LegionService.class);
        atlasService = Mockito.mock(AtlasService.class);
        verifier = new ManagerSectorTerritoryVerifier(legionService, atlasService);
        config = new ManagerSectorTerritoryVerifierConfig();
    }

    @Test
    void validate_withPhoneNumber_shouldNotThrow() {
        assertDoesNotThrow(() -> verifier.validate(EntityType.PHONE_NUMBER, config));
    }

    @Test
    void validate_withInvalidEntityType_shouldThrow() {
        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validate(EntityType.STORE, config));
        assertEquals(INVALID_TASK_VERIFIER, ex.getErrorCode());
    }

    @Test
    void verify_shouldReturnVerifiedTrueWithContext() {
        Map<String, Object> context = Map.of("key", "value");

        VerifierResponse response = verifier.verify(
                new TaskCompleteRequest(), config, context);

        assertTrue(response.getVerified());
        assertEquals(context, response.getContext());
    }

    @Test
    void validateTaskCreation_withValidManagerAndSectors_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setCreatedBy("agent123");
        request.setTransactionLocation(EsLocationRequest.builder().lat(12.34).lon(56.78).build());

        AgentProfile agentProfile = new AgentProfile();
        agentProfile.setManagerId("manager123");

        AgentProfile managerProfile = new AgentProfile();
        managerProfile.setSectors(List.of("sector1", "sector2"));

        when(legionService.getAgentProfile("agent123")).thenReturn(agentProfile);
        when(legionService.getAgentProfile("manager123")).thenReturn(managerProfile);
        when(atlasService.getSectorIdByLatLong(12.34, 56.78)).thenReturn(List.of("sector1"));

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertTrue(result);
    }

    @Test
    void validateTaskCreation_withMissingAgentProfile_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setCreatedBy("agent123");

        when(legionService.getAgentProfile("agent123")).thenReturn(null);

        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validateTaskCreation(request, new TaskActionInstance()));
        assertEquals(AGENT_PROFILE_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void validateTaskCreation_withMissingManagerProfile_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setCreatedBy("agent123");

        AgentProfile agentProfile = new AgentProfile();
        agentProfile.setManagerId("manager123");

        when(legionService.getAgentProfile("agent123")).thenReturn(agentProfile);
        when(legionService.getAgentProfile("manager123")).thenReturn(null);

        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validateTaskCreation(request, new TaskActionInstance()));
        assertEquals(MANAGER_PROFILE_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void validateTaskCreation_withNoManagerSectors_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setCreatedBy("agent123");

        AgentProfile agentProfile = new AgentProfile();
        agentProfile.setManagerId("manager123");

        AgentProfile managerProfile = new AgentProfile();
        managerProfile.setSectors(null);

        when(legionService.getAgentProfile("agent123")).thenReturn(agentProfile);
        when(legionService.getAgentProfile("manager123")).thenReturn(managerProfile);

        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validateTaskCreation(request, new TaskActionInstance()));
        assertEquals(NO_MANAGER_SECTORS, ex.getErrorCode());
    }

    @Test
    void validateTaskCreation_withSectorsOutsideManagerTerritory_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setCreatedBy("agent123");
        request.setTransactionLocation(EsLocationRequest.builder().lat(12.34).lon(56.78).build());

        AgentProfile agentProfile = new AgentProfile();
        agentProfile.setManagerId("manager123");

        AgentProfile managerProfile = new AgentProfile();
        managerProfile.setSectors(List.of("sector1", "sector2"));

        when(legionService.getAgentProfile("agent123")).thenReturn(agentProfile);
        when(legionService.getAgentProfile("manager123")).thenReturn(managerProfile);
        when(atlasService.getSectorIdByLatLong(12.34, 56.78)).thenReturn(List.of("sector3"));

        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validateTaskCreation(request, new TaskActionInstance()));
        assertEquals(TASK_CREATION_OUTSIDE_MANAGER_TERRITORY, ex.getErrorCode());
    }
}