package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.configs.chimeraconfig.LeadIntents;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAttributeCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadTaskConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadUpdationConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.SysAsyncVerificationStrategy;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.cache.LeadIntentCache;
import com.phonepe.merchant.legion.tasks.cache.TaskAttributeCache;
import com.phonepe.merchant.legion.tasks.repository.TaskAttributeRepository;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDefinitionServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import edu.emory.mathcs.backport.java.util.Arrays;
import edu.emory.mathcs.backport.java.util.Collections;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.STEP_KEY_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskDefinitionServiceTest extends LegionTaskBaseTest {

    private static TaskDefinitionService taskDefinitionService;
    private static TaskAttributeCache taskAttributeCache;
    private static TaskAttributeService taskAttributeService = mock(TaskAttributeService.class);
    private static LeadIntentCache leadIntentCache = mock(LeadIntentCache.class);
    private static LeadCreationConfig leadCreationConfig = mock(LeadCreationConfig.class);
    private static CacheUtils cacheUtils = mock(CacheUtils.class);


    @BeforeClass
    public static void init() {
        validationService = mock(ValidationService.class);
        taskAttributeCache = mock(TaskAttributeCache.class);
        taskAttributeRepository = mock(TaskAttributeRepository.class);
        taskDefinitionService = new TaskDefinitionServiceImpl(taskDefinitionRepository, taskActionService, eventExecutor, mock(TagService.class), taskAttributeCache, cacheUtils, leadIntentCache, validationService, leadCreationConfig);
    }

    @Test
    public void saveAndGet() {
        //arrange
        String actionId = "ACTION_ID123";
        String name = "Name";
        String actionDescription = "action description";
        String taskDefinitionDescription = "task definition description";

        TaskAttributeCreateRequest taskAttributeCreateRequest = TaskAttributeCreateRequest.builder()
                .attributeType(AttributeType.OBJECTIVE)
                .attributeValue("SARANSH")
                .name("TEsT")
                .build();
        taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, "saransh");
        HashMap<String, Set<String>> attributeTypeSetHashMap = new HashMap<>();

        attributeTypeSetHashMap.put(AttributeType.OBJECTIVE.getName(), Set.of("SARANSH"));

        taskActionService.save(TaskActionInstance.builder()
                .actionId(actionId)
                .verificationStrategy(new SysAsyncVerificationStrategy())
                .createdBy("jakshat")
                .description(actionDescription)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build());
        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .tags(Collections.emptySet())
                .taskDefinitionId("TD_test")
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{
                                        Map.of(STEP_KEY_NAME,"2"),
                                        Map.of(STEP_KEY_NAME,"3")}))
                        .build())
                .attributes(attributeTypeSetHashMap)
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .taskType("SS_DEPLOYMENTS").build())
                .build();

        when(taskAttributeCache.get("SARANSH")).thenReturn(TaskAttributeInstance.builder()
                .taskattributeValue("SARANSH")
                .attributeType(AttributeType.OBJECTIVE)
                .name("TEsT")
                .createdBy("SOMEONE")
                .updatedBy("me")
                .createdAt(new Date())
                .updatedAt(new Date())
                .build());
        //call
        TaskDefinitionInstance actualResponse1 = taskDefinitionService.save(saveRequest);
        String taskDefinitionId = actualResponse1.getTaskDefinitionId();
        TaskDefinitionInstance actualResponse2 = taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId)
                .build());

        //assert
        Assert.assertEquals(actionId,actualResponse1.getActionId());
        Assert.assertEquals(name,actualResponse1.getName());
        Assert.assertEquals(actualResponse1.getTaskDefinitionId(),actualResponse2.getTaskDefinitionId());
    }

    @Test
    public void test_saveAndGetDefinitionWithIntent() {
        //arrange
        String actionId = "ACTION_ID_TEST";
        String name = "Name";
        String actionDescription = "action description";
        String taskDefinitionDescription = "task definition description";

        TaskAttributeCreateRequest taskAttributeCreateRequest = TaskAttributeCreateRequest.builder()
                .attributeType(AttributeType.OBJECTIVE)
                .attributeValue("SARANSH")
                .name("TEsT")
                .build();
        taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, "saransh");
        when(leadIntentCache.get("DEFAULT_KEY")).thenReturn(LeadIntents.builder().intents(List.of("HOT", "WARM", "COLD", "NOT_INTERESTED")).build());
        List<ActionToRemarkConfig> leadCreation = List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder()
                .intent("WARM").build())).build());

        List<ActionToRemarkConfig> leadUpdation = List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder()
                .intent("HOT").build())).build());
        ;
        HashMap<String, Set<String>> attributeTypeSetHashMap = new HashMap<>();

        attributeTypeSetHashMap.put(AttributeType.OBJECTIVE.getName(), Set.of("SARANSH"));

        taskActionService.save(TaskActionInstance.builder()
                .actionId(actionId)
                .verificationStrategy(new SysAsyncVerificationStrategy())
                .createdBy("jakshat")
                .description(actionDescription)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build());
        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .tags(Collections.emptySet())
                .taskDefinitionId("TD_test-v2")
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{
                                        Map.of(STEP_KEY_NAME,"2"),
                                        Map.of(STEP_KEY_NAME,"3")}))
                        .build())
                .attributes(attributeTypeSetHashMap)
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .taskType("SS_DEPLOYMENTS")
                        .leadConfig(LeadConfig.builder().leadUpdation(leadUpdation).leadCreation(leadCreation).build())
                        .build())
                .build();

        when(taskAttributeCache.get("SARANSH")).thenReturn(TaskAttributeInstance.builder()
                .taskattributeValue("SARANSH")
                .attributeType(AttributeType.OBJECTIVE)
                .name("TEsT")
                .createdBy("SOMEONE")
                .updatedBy("me")
                .createdAt(new Date())
                .updatedAt(new Date())
                .build());


        //call
        TaskDefinitionInstance actualResponse1 = taskDefinitionService.save(saveRequest);
        String taskDefinitionId = actualResponse1.getTaskDefinitionId();
        TaskDefinitionInstance actualResponse2 = taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId)
                .build());

        //assert
        Assert.assertEquals(actionId,actualResponse1.getActionId());
        Assert.assertEquals(name,actualResponse1.getName());
        Assert.assertEquals(actualResponse1.getTaskDefinitionId(),actualResponse2.getTaskDefinitionId());
    }

    @Test
    public void test_saveAndGetDefinitionWithInvalidIntent() {
        //arrange
        String actionId = "ACTION_ID_TEST_V2";
        String name = "Name";
        String actionDescription = "action description";
        String taskDefinitionDescription = "task definition description";

        TaskAttributeCreateRequest taskAttributeCreateRequest = TaskAttributeCreateRequest.builder()
                .attributeType(AttributeType.OBJECTIVE)
                .attributeValue("SARANSH")
                .name("TEsT")
                .build();
        taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, "saransh");
        when(leadIntentCache.get("DEFAULT_KEY")).thenReturn(LeadIntents.builder().intents(List.of("HOT", "WARM", "COLD", "NOT_INTERESTED")).build());
        List<ActionToRemarkConfig> leadCreation = List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder()
                .intent("TEST").build())).build());

        List<ActionToRemarkConfig> leadUpdation = List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder()
                .intent("HOT").build())).build());
        ;
        HashMap<String, Set<String>> attributeTypeSetHashMap = new HashMap<>();

        attributeTypeSetHashMap.put(AttributeType.OBJECTIVE.getName(), Set.of("SARANSH"));

        taskActionService.save(TaskActionInstance.builder()
                .actionId(actionId)
                .verificationStrategy(new SysAsyncVerificationStrategy())
                .createdBy("jakshat")
                .description(actionDescription)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build());
        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .tags(Collections.emptySet())
                .taskDefinitionId("TD_test-v4")
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{
                                        Map.of(STEP_KEY_NAME,"2"),
                                        Map.of(STEP_KEY_NAME,"3")}))
                        .build())
                .attributes(attributeTypeSetHashMap)
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .taskType("SS_DEPLOYMENTS")
                        .leadConfig(LeadConfig.builder().leadUpdation(leadUpdation).leadCreation(leadCreation).build())
                        .build())
                .build();

        when(taskAttributeCache.get("SARANSH")).thenReturn(TaskAttributeInstance.builder()
                .taskattributeValue("SARANSH")
                .attributeType(AttributeType.OBJECTIVE)
                .name("TEsT")
                .createdBy("SOMEONE")
                .updatedBy("me")
                .createdAt(new Date())
                .updatedAt(new Date())
                .build());


        Assertions.assertThrows(LegionException.class , () -> taskDefinitionService.save(saveRequest));
    }

    @Test(expected = LegionException.class)
    public void saveTaskDefinitionWithInvalidSubstepCase1() {
        String actionId = "ACTION_ID_1";
        String name = "Name";
        String taskDefinitionDescription = "task definition description";

        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{Map.of(
                                        "key","value")}))
                        .build())
                .build();

        //call
        taskDefinitionService.save(saveRequest);
    }

    @Test(expected = LegionException.class)
    public void saveTaskDefinitionWithInvalidSubstepCase2() {
        String actionId = "ACTION_ID_2";
        String name = "Name";
        String taskDefinitionDescription = "task definition description";

        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{Map.of(
                                        STEP_KEY_NAME,"value")}))
                        .build())
                .build();

        //call
        taskDefinitionService.save(saveRequest);
    }

    @Test(expected = LegionException.class)
    public void saveInvalidActionException() {
        //arrange
        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId("ACTION_ID_1")
                .name("Name")
                .build();

        //call
        taskDefinitionService.save(saveRequest);
    }

    @Test(expected = LegionException.class)
    public void getNotFound() {
        //arrange
        TaskDefinitionFetchByIdRequest request = TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId("TASK_DEF_ID")
                .build();

        //call
        taskDefinitionService.getFromDb(request);
    }

    @Test
    public void fetchByActionId(){
        String actionId = "ACTION_ID_1";
        String name = "Name";
        String actionDescription = "action description";
        String taskDefinitionDescription = "task definition description";

        taskActionService.save(TaskActionInstance.builder()
                .actionId(actionId)
                .verificationStrategy(new SysAsyncVerificationStrategy())
                .createdBy("ps")
                .description(actionDescription)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build());
        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .taskDefinitionId("TD2")
                .tags(Collections.emptySet())
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .taskType("SS_DEPLOYMENTS").build())
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{
                                        Map.of(STEP_KEY_NAME,"2"),
                                        Map.of(STEP_KEY_NAME,"3")}))
                        .build())
                .build();

        TaskDefinitionInstance response1 = taskDefinitionService.save(saveRequest);
        List<TaskDefinitionInstance> response2 = taskDefinitionService.fetchByActionId(actionId);

        Assert.assertEquals(response1.getTaskDefinitionId(),response2.get(0).getTaskDefinitionId());
    }

    @Test
    public void updateTest() {
        String actionId = "ACTION_ID_2";
        String name = "Saransh";
        String actionDescription = "action description";
        String taskDefinitionDescription = "task definition description";
        taskActionService.save(TaskActionInstance.builder()
                .actionId(actionId)
                .verificationStrategy(new SysAsyncVerificationStrategy())
                .createdBy("ps")
                .description(actionDescription)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build());
        Map<String, Set<String>> attributeMap = new HashMap<>();
        attributeMap.put("OBJECTIVE", Set.of("SARANSH"));
        TaskDefinitionCreateRequest saveRequest = TaskDefinitionCreateRequest.builder()
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .taskDefinitionId("TD1")
                .name(name)
                .tags(Collections.emptySet())
                .attributes(attributeMap)
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{
                                        Map.of(STEP_KEY_NAME,"2"),
                                        Map.of(STEP_KEY_NAME,"3")}))
                        .build())
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .taskType("SS_DEPLOYMENTS").build())
                .build();
        when(taskAttributeCache.get("SARANSH")).thenReturn(TaskAttributeInstance.builder().taskattributeValue("SARANSH").build());
        TaskDefinitionInstance savedTaskDefinition = taskDefinitionService.save(saveRequest);
        String taskDefId = savedTaskDefinition.getTaskDefinitionId();

        Map<String, Set<String>> updatedAttributeMap = new HashMap<>();
        attributeMap.put("OBJECTIVE", Set.of("SARANSH_AGARWAL"));

        TaskDefinitionInstance updateRequest = TaskDefinitionInstance.builder()
                .taskDefinitionId(taskDefId)
                .points(1)
                .createdBy("SOMEONE")
                .priority(Priority.P1)
                .actionId(actionId)
                .name(name)
                .tags(Collections.emptySet())
                .attributes(updatedAttributeMap)
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .taskType("SS_DEPLOYMENTS").build())
                .taskDefinitionMeta(TaskDefinitionMeta.builder()
                        .description(taskDefinitionDescription)
                        .substeps(
                                Arrays.asList(new Map[]{
                                        Map.of(STEP_KEY_NAME,"2"),
                                        Map.of(STEP_KEY_NAME,"3")}))
                        .build())
                .build();
        when(taskAttributeCache.get("SARANSH_AGARWAL")).thenReturn(TaskAttributeInstance.builder().taskattributeValue("SARANSH_AGARWAL").build());
        taskDefinitionService.update(updateRequest);
        Assert.assertNotNull(taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder()
                        .taskDefinitionId(taskDefId)
                .build()));
    }

    @Test
    public void getLeadCreationConfigTest(){
        LeadTaskConfig leadTaskConfig = LeadTaskConfig.builder()
                .taskType("SS_DEPLOYMENTS")
                .displayText("mohit puri")
                .campaignId("CID")
                .version(1)
                .definitionId("DID")
                .leadCreationIntents(List.of(IntentWithRemarks.builder().build()))
                .build();
        LeadTaskConfig secondLeadTaskConfig = LeadTaskConfig.builder()
                .taskType("EDC_DEPLOYMENTS")
                .displayText("mohit puri")
                .campaignId("CID")
                .version(2)
                .definitionId("DID1")
                .leadCreationIntents(List.of(IntentWithRemarks.builder().build()))
                .build();;
        when(leadCreationConfig.getLeadTaskConfigs()).thenReturn(List.of(leadTaskConfig, secondLeadTaskConfig));
        when(cacheUtils.getValue("DID", CacheName.TASK_DEFINITION))
                .thenReturn(TaskDefinitionInstance.builder()
                        .taskDefinitionId("DID")
                        .updatedBy("mohit")
                        .createdBy("mohit")
                        .actionId("KYC")
                        .points(1)
                        .priority(Priority.P0)
                        .definitionAttributes(TaskDefinitionAttributes.builder()
                                .leadConfig(LeadConfig.builder()
                                        .leadCreation(List.of(ActionToRemarkConfig.builder()
                                                .actionId("ActionId").config(List.of(IntentWithRemarks.builder()
                                                        .intent("HOT").build())).build()))
                                        .leadUpdation(List.of(ActionToRemarkConfig.builder()
                                                .actionId("ActionId").build()))
                                        .build()).build())
                        .namespace(Namespace.LEGION)
                        .name("name").build());

        LeadCreationConfig leadCreationConfig1 = taskDefinitionService.getLeadCreationConfig(1);
        Assertions.assertNotNull(leadCreationConfig1);
        assertEquals("SS_DEPLOYMENTS", leadCreationConfig1.getLeadTaskConfigs().get(0).getTaskType());
        assertEquals("DID", leadCreationConfig1.getLeadTaskConfigs().get(0).getDefinitionId());
        assertEquals(1, leadCreationConfig1.getLeadTaskConfigs().get(0).getVersion());
    }

    @Test
    public void getLeadCreationConfig_EmptyList_Test(){
        when(leadCreationConfig.getLeadTaskConfigs()).thenReturn(List.of(LeadTaskConfig.builder()
                .taskType("SS_DEPLOYMENTS")
                .displayText("mohit puri")
                .campaignId("CID")
                .definitionId("DID")
                .leadCreationIntents(List.of(IntentWithRemarks.builder().build()))
                .build()));
        when(cacheUtils.getValue("DID", CacheName.TASK_DEFINITION))
                .thenReturn(TaskDefinitionInstance.builder()
                        .taskDefinitionId("DID")
                        .updatedBy("mohit")
                        .createdBy("mohit")
                        .actionId("KYC")
                        .points(1)
                        .priority(Priority.P0)
                        .definitionAttributes(TaskDefinitionAttributes.builder()
                                .leadConfig(LeadConfig.builder().build()
                                        ).build())
                        .namespace(Namespace.LEGION)
                        .name("name").build());

        LeadCreationConfig leadCreationConfig1 = taskDefinitionService.getLeadCreationConfig(1);
        Assertions.assertNotNull(leadCreationConfig1);
    }

    @Test
    public void getLeadUpdationConfigTest(){
        TaskDefinitionAttributes attributes = TaskDefinitionAttributes.builder().taskType("test").category(Category.LENDING)
                .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder().config(List.of(IntentWithRemarks.builder().build())).actionId("action-id").build()))
                        .leadCreation(List.of()).build()).build();
        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder().taskDefinitionId("DID").build()))
                .thenReturn(TaskDefinitionInstance.builder()
                        .taskDefinitionId("DID")
                        .updatedBy("mohit")
                        .createdBy("mohit")
                        .actionId("KYC")
                        .points(1)
                        .priority(Priority.P0)
                        .definitionAttributes(attributes)
                        .namespace(Namespace.LEGION)
                        .name("name").build());
        LeadUpdationConfig leadUpdationConfig = taskDefinitionService.getLeadUpdationConfig("DID");
        Assertions.assertNotNull(leadUpdationConfig);
    }

    @Test
    public void getLeadUpdationConfigTest_failure(){
        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder().taskDefinitionId("DID").build()))
                .thenReturn(TaskDefinitionInstance.builder()
                        .taskDefinitionId("DID")
                        .updatedBy("mohit")
                        .createdBy("mohit")
                        .actionId("KYC")
                        .points(1)
                        .priority(Priority.P0)
                        .namespace(Namespace.LEGION)
                        .name("name").build());
        Assertions.assertThrows(LegionException.class, () ->  taskDefinitionService.getLeadUpdationConfig("DID"));
    }
}
