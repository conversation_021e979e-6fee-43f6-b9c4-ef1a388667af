package com.phonepe.merchant.legion.tasks.search.query.scoring;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClient;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.TaskTestUtils;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.search.query.scoring.models.PrioritiseStrategyConfig;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FunctionalQueryBuilderTest extends LegionTaskBaseTest {

    private FunctionalQueryBuilder functionalQueryBuilder;
    private ChimeraLiteRepository chimeraLiteRepository;
    private static final String CHIMERA_KEY = "gladius_pre_filter_sorting_strategy_experiment_config";
    private static final String CHIMERA_PRIORITY_KEY = "chimera_priority_key";
    private FoxtrotEventExecutor foxtrotEventExecutor;
    private final FilterCraftClient filterCraftClient;

    private final FilterCraftBuilderCache filterCraftBuilderCache;

    public FunctionalQueryBuilderTest() throws IOException {
        chimeraLiteRepository = mock(ChimeraLiteRepository.class);
        foxtrotEventExecutor = mock(FoxtrotEventExecutor.class);
        foxtrotEventIngestionService = mock(FoxtrotEventIngestionService.class);
        filterCraftBuilderCache = mock(FilterCraftBuilderCache.class);
        filterCraftClient = mock(FilterCraftClient.class);
        when(chimeraLiteRepository.getChimeraConfigString(CHIMERA_PRIORITY_KEY)).thenReturn("priority-config");
        when(filterCraftBuilderCache.get(FilterCraftBuilderCacheKey.builder().config("priority-config").build())).thenReturn(filterCraftClient);
        when(filterCraftClient.convertToESFilter()).thenReturn(QueryBuilders.boolQuery());

        mapper = new ObjectMapper();
        SerDe.init(mapper);
        functionalQueryBuilder= new FunctionalQueryBuilder(
                chimeraLiteRepository, foxtrotEventIngestionService, filterCraftBuilderCache);
    }

    @Test
    void FunctionalQueryBuilderTest() {
        when(chimeraLiteRepository.getChimeraConfig(CHIMERA_KEY, "AGENT1", PrioritiseStrategyConfig.class))
                .thenReturn(TaskTestUtils.getPrioritiseStrategyConfig(CHIMERA_PRIORITY_KEY));
        QueryBuilder queryBuilder = functionalQueryBuilder.createFunctionalQuery(QueryBuilders.boolQuery(), "AGENT1", EsLocationRequest.builder().lat(0.0).lon(0.0).build());
        Assertions.assertNotNull(queryBuilder);
    }

    @Test
    void FunctionalQueryBuilderDisabledTest() {
        when(chimeraLiteRepository.getChimeraConfig(CHIMERA_KEY, "AGENT1", PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().functionScoringEnabled(false).build());
        QueryBuilder queryBuilder = functionalQueryBuilder.createFunctionalQuery(QueryBuilders.boolQuery(), "AGENT1", EsLocationRequest.builder().lat(0.0).lon(0.0).build());
        Assertions.assertNotNull(queryBuilder);
    }
}
