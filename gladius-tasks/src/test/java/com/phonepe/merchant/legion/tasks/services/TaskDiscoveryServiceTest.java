package com.phonepe.merchant.legion.tasks.services;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.gladius.models.entitystore.AgentEntity;
import com.phonepe.merchant.gladius.models.entitystore.AgentEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.EntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntity;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.MerchantEntity;
import com.phonepe.merchant.gladius.models.entitystore.MerchantEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.SectorEntity;
import com.phonepe.merchant.gladius.models.entitystore.SectorEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.StoreEntity;
import com.phonepe.merchant.gladius.models.entitystore.StoreEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.TaskEntity;
import com.phonepe.merchant.gladius.models.entitystore.TaskEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.VpaEntity;
import com.phonepe.merchant.gladius.models.entitystore.VpaEntityMeta;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.StatFilter;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.AgentTaskEligibilityRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.ListingRequestMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDetailRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TaskStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TasksStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.AgentTaskEligibilityResponse;
import com.phonepe.merchant.gladius.models.tasks.response.EntityStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDetailResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMapViewDetails;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaAttribute;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskStateStatsByAction;
import com.phonepe.merchant.gladius.models.tasks.response.UserStatsAndFilters;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.gladius.models.utils.LegionModelsConstants;
import com.phonepe.merchant.legion.core.config.AttributeInfoConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.enums.AgentStatus;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.request.userattribute.TagsAttribute;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.AgentSectorResponse;
import com.phonepe.merchant.legion.tasks.DiscoveryTestUtils;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.TaskTestUtils;
import com.phonepe.merchant.legion.tasks.cache.TaskFiltersCache;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.flows.TransitionValidator;
import com.phonepe.merchant.legion.tasks.flows.Validations;
import com.phonepe.merchant.legion.tasks.search.executors.SectorMapViewTaskSearchQueryExecutor;
import com.phonepe.merchant.legion.tasks.search.executors.TaskSearchQueryExecutor;
import com.phonepe.merchant.legion.tasks.search.query.QueryEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.RequestTaskListingQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.query.mapview.SectorMapViewTaskSearchRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.query.scoring.FunctionalQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.scoring.models.PrioritiseStrategyConfig;
import com.phonepe.merchant.legion.tasks.search.query.search.BadgeConfigRequestExecutor;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskSearchRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.response.filter.RequestViewWiseFilterFactory;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDiscoveryServiceImpl;
import com.phonepe.models.merchants.BusinessUnit;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.BOUNDED_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.COMPLETED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.SELF_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.STARTED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.VERIFICATION_FAILED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.VERIFICATION_SUCCESS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ATTRIBUTES;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_DEFINITION_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.DISTANCE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.DISTANCE_UNIT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POINTS_UNIT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASKS_UNIT;
import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getAgentTaskSearchRequest;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskInstance;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskInstanceList;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskInstanceListMultipleEntities;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskSearchRequest;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskSearchRequestWithLocationSort;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDummyStatsAggregation;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getEsSearchResponse;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getSectorDiscoveryTaskSearchRequest;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getSectorDiscoveryTaskSearchRequestWithLocationSort;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getTaskDistance;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getTaskFilters;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.SECTOR_MISMATCH;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.STEP_KEY_NAME;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.ENTITY_TYPE;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.OBJECTIVES_KEYWORD;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.STATUS;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskStateStatFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils.discoveryTaskInstanceToTaskMeta;
import static com.phonepe.models.merchants.tasks.EntityType.STORE;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskDiscoveryServiceTest extends LegionTaskBaseTest {

    private static TaskDiscoveryService taskDiscoveryService;
    private static ChimeraLiteRepository chimeraLiteRepository;
    private static TaskAttributeService taskAttributeService = mock(TaskAttributeService.class);
    private static ESRepository esRepository;
    private static EntityStore entityStore;
    private static QueryEnricher enricher;
    private static TaskSearchQueryExecutor taskSearchQueryExecutor;
    private static TaskFiltersCache taskFiltersCache;
    private static LegionService legionService;
    private static AtlasService atlasService;
    private static TaskActionService taskActionService;
    private static TaskDefinitionService taskDefinitionService = mock(TaskDefinitionService.class);
    private static LegionService profileCRUDService;
    private static TransitionValidator transitionValidator;
    private static Validations validations;
    private static SectorMapViewTaskSearchQueryExecutor sectorMapViewTaskSearchQueryExecutor = mock(SectorMapViewTaskSearchQueryExecutor.class);
    private static TaskSearchRequestQueryBuilderFactory taskSearchRequestQueryBuilderFactory = mock(TaskSearchRequestQueryBuilderFactory.class);
    private static RequestTaskListingQueryBuilderFactory requestTaskListingQueryBuilderFactory = mock(RequestTaskListingQueryBuilderFactory.class);
    private static SectorMapViewTaskSearchRequestQueryBuilderFactory sectorMapViewTaskSearchRequestQueryBuilderFactory = mock(SectorMapViewTaskSearchRequestQueryBuilderFactory.class);
    private static RequestViewWiseFilterFactory requestViewWiseFilterFactory = mock(RequestViewWiseFilterFactory.class);
    private static List<LegionTaskStateMachineState> assignedStates;
    private static FoxtrotEventExecutor eventExecutor;
    private static TagService tagService = mock(TagService.class);
    private static ValidationService validationService = mock(ValidationService.class);
    private static SearchResponse searchResponse = mock(SearchResponse.class);
    private static Map<String, AttributeInfoConfig> attributeInfo;
    private static final String ACTOR = "actor";
    private static final String ENTITY_ID = "entityId";
    private static final String TASK_TYPE_FILTERS_DEFAULT_KEY = "DEFAULT_KEY";
    public static Miscellaneous miscellaneous;
    public static FunctionalQueryBuilder functionalQueryBuilder;

    @BeforeClass
    public static void init() {
        miscellaneous = new Miscellaneous(25, 10, 90, 10, 50);
        esRepository = mock(ESRepository.class);
        taskSearchQueryExecutor = new TaskSearchQueryExecutor(esRepository);
        legionService = mock(LegionService.class);
        atlasService = mock(AtlasService.class);
        searchResponse = mock(SearchResponse.class);
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        taskAttributeService = mock(TaskAttributeService.class);
        taskFiltersCache = new TaskFiltersCache(cacheConfigs, () -> taskDiscoveryService, metricRegistry, cacheUtils);
        entityStore = mock(EntityStore.class);
        taskActionService = mock(TaskActionService.class);
        profileCRUDService = mock(LegionService.class);
        enricher = mock(QueryEnricher.class);
        transitionValidator = mock(TransitionValidator.class);
        validations = mock(Validations.class);
        functionalQueryBuilder = mock(FunctionalQueryBuilder.class);
        assignedStates = LegionTaskStateMachineState.getAssignedStates();
        attributeInfo = new HashMap<>();
        attributeInfo.put(LegionModelsConstants.DISTANCE, AttributeInfoConfig.builder().icon("icon").unit("dist").build());
        attributeInfo.put(LegionModelsConstants.POINTS, AttributeInfoConfig.builder().icon("points").unit("pt").build());
        attributeInfo.put(LegionModelsConstants.START_DUE_DATE, AttributeInfoConfig.builder().icon("StartDueDate").unit("date").build());
        attributeInfo.put(LegionModelsConstants.COMPLETED_ON, AttributeInfoConfig.builder().icon("").unit("").build());

        eventExecutor = mock(FoxtrotEventExecutor.class);
        taskDiscoveryService = new TaskDiscoveryServiceImpl(esRepository, entityStore, enricher, taskActionService, taskDefinitionService,
                sectorMapViewTaskSearchQueryExecutor, taskFiltersCache, taskActionRepository,
                taskSearchQueryExecutor, validations, taskInstanceManagementService,
                taskSearchRequestQueryBuilderFactory, requestViewWiseFilterFactory, taskAttributeService, miscellaneous, functionalQueryBuilder, mock(BadgeConfigRequestExecutor.class),legionService, atlasService, validationService, attributeInfo);
        sectorMapViewTaskSearchQueryExecutor = new SectorMapViewTaskSearchQueryExecutor(esRepository);
        chimeraLiteRepository = mock(ChimeraLiteRepository.class);
        taskDiscoveryService = new TaskDiscoveryServiceImpl(esRepository, entityStore, enricher, taskActionService, taskDefinitionService, sectorMapViewTaskSearchQueryExecutor, taskFiltersCache, taskActionRepository,
                taskSearchQueryExecutor, validations, taskInstanceManagementService, taskSearchRequestQueryBuilderFactory, requestViewWiseFilterFactory, taskAttributeService, miscellaneous, functionalQueryBuilder, mock(BadgeConfigRequestExecutor.class),legionService, atlasService, validationService, attributeInfo);
        when(sectorGeofenceQueryBuilder.visit(any(EqualsFilter.class))).thenReturn(QueryBuilders.boolQuery());
        when(sectorGeofenceQueryBuilder.visit(any(InFilter.class))).thenReturn(QueryBuilders.boolQuery());
    }

    @AfterEach
    void afterEach() {
        Mockito.reset(legionService, entityStore);
    }

    private void setUpDefaultEs(List<DiscoveryTaskInstance> tasks, boolean includeLocationOutputInGeoSort) {
        when(esRepository.search(eq(TASK_INDEX), any(), anyInt(), anyInt(), eq(DiscoveryTaskInstance.class))).thenReturn(tasks);
        when(esRepository.search(eq(TASK_INDEX), any(), anyInt(), anyInt())).thenReturn(getEsSearchResponse(tasks, false));
        when(esRepository.searchWithGeoSorting(eq(TASK_INDEX), any(), anyInt(), anyInt(), any())).thenReturn(getEsSearchResponse(tasks, includeLocationOutputInGeoSort));
        when(esRepository.searchWithSorting(eq(TASK_INDEX), any(), anyInt(), anyInt(), any(), any())).thenReturn(getEsSearchResponse(tasks, false));
    }

    private TaskSearchResponse setupSearchTest(TaskSearchRequest request, int numOfTasksPerEntityType, boolean includeLocation) {
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, numOfTasksPerEntityType);
        Map<String, EntityMeta> entityIdToMeta = new HashMap<>();
        Map<EntityType, Set<String>> entityTypeListMap = new EnumMap<>(EntityType.class);
        List<TaskMetaResponse> taskMetaResponses = new ArrayList<>();
        tasks.forEach(taskInstance -> {
            TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                    .actionId(taskInstance.getActionId())
                    .subSteps(Arrays.asList(
                            Map.of(STEP_KEY_NAME, "1"),
                            Map.of(STEP_KEY_NAME, "3")
                    ))
                    .description("action description")
                    .build();
            taskDefinitionRepository.save(StoredTaskDefinition.builder()
                    .namespace(taskInstance.getNamespace())
                    .name("name")
                    .createdBy("someone")
                    .points(taskInstance.getPoints().intValue())
                    .actionId(taskInstance.getActionId())
                    .priority(Priority.P1)
                    .taskDefinitionId(taskInstance.getTaskDefinitionId())
                    .build());
            TaskDefinitionInstance taskDefinitionInstance = taskDefinitionService.getFromCache(
                    TaskDefinitionFetchByIdRequest.builder()
                            .taskDefinitionId(taskInstance.getTaskDefinitionId())
                            .build());
            when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                    .taskActionId(taskInstance.getActionId())
                    .build()))
                    .thenReturn(taskActionInstance);
            EntityMeta entityMeta = taskInstance.getEntityType().accept(new EntityType.EntityTypeVisitor<EntityMeta, DiscoveryTaskInstance>() {
                @Override
                public EntityMeta visitUser(DiscoveryTaskInstance discoveryTaskInstance) {
                    return null;
                }

                @Override
                public EntityMeta visitSector(DiscoveryTaskInstance discoveryTaskInstance) {
                    return new SectorEntityMeta(SectorEntity.builder()
                            .location(EsLocationRequest.builder()
                                    .lat(0.0)
                                    .lon(0.0)
                                    .build())
                            .sectorId(discoveryTaskInstance.getEntityId())
                            .build());
                }

                @Override
                public EntityMeta visitMerchant(DiscoveryTaskInstance discoveryTaskInstance) {
                    return new MerchantEntityMeta(MerchantEntity.builder()
                            .merchantId(discoveryTaskInstance.getEntityId())
                            .build());
                }

                @Override
                public EntityMeta visitNone(DiscoveryTaskInstance discoveryTaskInstance) {
                    return null;
                }

                @Override
                public EntityMeta visitStore(DiscoveryTaskInstance discoveryTaskInstance) {
                    String[] data = discoveryTaskInstance.getEntityId().split("_", 2);
                    return new StoreEntityMeta(StoreEntity.builder()
                            .storeName("abcd")
                            .merchantId(data[0])
                            .storeId(data[1])
                            .build());
                }

                @Override
                public EntityMeta visitTask(DiscoveryTaskInstance discoveryTaskInstance) {
                    return new TaskEntityMeta(TaskEntity.builder()
                            .completedOn(new Date())
                            .completedBy("me")
                            .taskInstanceId(taskInstance.getEntityId())
                            .build());
                }

                @Override
                public EntityMeta visitVpa(DiscoveryTaskInstance discoveryTaskInstance) {
                    return new VpaEntityMeta(VpaEntity.builder()
                            .shopName("name")
                            .location(EsLocationRequest.builder()
                                    .lat(0.0)
                                    .lon(0.0)
                                    .build())
                            .providerName("P-PAY")
                            .vpaId(taskInstance.getEntityId())
                            .polygonIds(taskInstance.getPolygonIds())
                            .build());
                }

                @Override
                public EntityMeta visitExternal(DiscoveryTaskInstance discoveryTaskInstance) {
                    return new ExternalEntityMeta(ExternalEntity.builder()
                            .location(EsLocationRequest.builder()
                                    .lat(0.0)
                                    .lon(0.0)
                                    .build())
                            .clientId("CLIENT_ID")
                            .externalEntityId(taskInstance.getEntityId())
                            .sectorIds(taskInstance.getPolygonIds())
                            .name("External Shop")
                            .clientProvidedId("My_id")
                            .createdBy("Someone")
                            .updatedBy("Someone")
                            .build());
                }

                @Override
                public EntityMeta visitAgent(DiscoveryTaskInstance discoveryTaskInstance) {
                    return new AgentEntityMeta(AgentEntity.builder()
                            .phoneNumber("123")
                            .emailId("a@bcd")
                            .agentId(taskInstance.getEntityId())
                            .polygonIds(taskInstance.getPolygonIds())
                            .agentName("Agent Name")
                            .managerId("Manager ID")
                            .active(true)
                            .build());
                }

                @Override
                public EntityMeta visitPhoneNumber(DiscoveryTaskInstance discoveryTaskInstance) {
                    return null;
                }
            }, taskInstance);
            if (!entityTypeListMap.containsKey(taskInstance.getEntityType())) {
                Set<String> ids = new HashSet<>();
                entityTypeListMap.put(taskInstance.getEntityType(), ids);
            }
            entityTypeListMap.get(taskInstance.getEntityType()).add(taskInstance.getEntityId());
            entityIdToMeta.put(taskInstance.getEntityId(), entityMeta);
            TaskMetaResponse taskMeta = discoveryTaskInstanceToTaskMeta(taskInstance, taskDefinitionInstance, null);
            if (includeLocation) {
                AttributeInfoConfig distanceInfo = attributeInfo.get(DISTANCE);
                taskMeta.addAttribute(DISTANCE_UNIT, distanceInfo.getIcon(), distanceInfo.getUnit(), getTaskDistance(taskInstance));
            }
            if (isNullOrEmpty(entityMeta.getName())) {
                entityMeta.setName(taskMeta.getDescription());
            }
            AttributeInfoConfig pointInfo = attributeInfo.get(LegionModelsConstants.POINTS);
            taskMeta.addAttribute(POINTS_UNIT, pointInfo.getIcon(), pointInfo.getUnit(), taskInstance.getPoints());
            taskMeta.setEntityMeta(entityMeta);
            taskMeta.setSteps(null);
            taskMetaResponses.add(taskMeta);
        });
        setUpDefaultEs(tasks, includeLocation);
        when(entityStore.getEntityMetaMap(entityTypeListMap, null)).thenReturn(entityIdToMeta);
        return TaskSearchResponse.builder()
                .taskCount(taskMetaResponses.size())
                .taskList(taskMetaResponses)
                .build();
    }

    private List<DiscoveryTaskInstance> getAgentListingTestTasks() {
        List<DiscoveryTaskInstance> tasks = new ArrayList<>();

        EnumSet.allOf(EntityType.class).stream().forEach(entityType -> {
            String actionId = IdGenerator.generate("A").getId();
            String entityId = IdGenerator.generate("E").getId();

            List<Object> actionSubSteps = new ArrayList<>();
            actionSubSteps.add(Map.of(STEP_KEY_NAME, "2"));
            List<Object> taskDefSubSteps = new ArrayList<>();
            taskDefSubSteps.add(Map.of(STEP_KEY_NAME, "1"));
            taskDefSubSteps.add(Map.of(STEP_KEY_NAME, "3"));
            List<Object> expectedOverallSubsteps = new ArrayList<>();
            expectedOverallSubsteps.add(Map.of(STEP_KEY_NAME, "1"));
            expectedOverallSubsteps.add(Map.of(STEP_KEY_NAME, "2"));
            expectedOverallSubsteps.add(Map.of(STEP_KEY_NAME, "3"));
            String taskDefDescription = "task definition description";

            TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                    .actionId(actionId)
                    .entityType(entityType)
                    .namespace(Namespace.MERCHANT_ONBOARDING)
                    .createdBy("someone")
                    .updatedBy("someone")
                    .description("action description")
                    .subSteps(actionSubSteps)
                    .build();
            when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                    .taskActionId(actionId).build())).thenReturn(taskActionInstance);
            List<DiscoveryTaskInstance> tempTasks = getDiscoveryTaskInstanceList(LegionTaskStateMachineState.COMPLETED, 10);

            tempTasks.forEach(taskInstance -> {
                taskDefinitionRepository.save(StoredTaskDefinition.builder()
                        .namespace(taskInstance.getNamespace())
                        .name("name")
                        .createdBy("someone")
                        .points(taskInstance.getPoints().intValue())
                        .actionId(taskInstance.getActionId())
                        .priority(Priority.P1)
                        .taskDefinitionId(taskInstance.getTaskDefinitionId())
                        .taskDefinitionMeta(SerDe.writeValueAsBytes(
                                TaskDefinitionMeta.builder()
                                        .description(taskDefDescription)
                                        .substeps(taskDefSubSteps)
                                        .build()
                        ))
                        .build());
                taskInstance.setEntityId(entityId);
                taskInstance.setEntityType(STORE);
                taskInstance.setAssignedTo(ACTOR);
                taskInstance.setActionId(actionId);
                taskInstance.setCompletedOn(1736231974L);
            });

            tasks.addAll(tempTasks);
        });

        return tasks;
    }

    @Test
    public void getAgentTaskListing() {
        //arrange
        String entityId = "entity id";
        TaskMetaInformation taskMetaInformation = new TaskMetaInformation(TaskMetaType.DEVICE_ID, "RANDOM_DEVICE_ID", true);
        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(taskMetaInformation);
        taskMetaInformationList.add(new TaskMetaInformation(TaskMetaType.LEAD_INTENT, "HOT", true));

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        setUpDefaultEs(tasks, false);
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class)))
                .thenReturn(TaskInstance.builder()
                        .actionId("EDC_ORDER_PLACEMENT")
                        .instanceMeta(TaskInstanceMeta.builder()
                                .taskMetaList(taskMetaInformationList)
                                .build())
                        .build());

        List<TaskMetaResponse> expectedResponse = new ArrayList<>();
        tasks.forEach(taskInstance ->
                expectedResponse.add(
                        discoveryTaskInstanceToTaskMeta(taskInstance,
                                taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                                        .taskDefinitionId(taskInstance.getTaskDefinitionId())
                                        .build()
                                ),
                                TaskInstance.builder()
                                        .instanceMeta(TaskInstanceMeta.builder()
                                                .taskMetaList(taskMetaInformationList)
                                                .build())
                                        .build())
                ));
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(TASK_DEFINITION_ID, "TASK_DEFINITION"));
        TaskListingRequest request = TaskListingRequest.builder()
                .requestType(TaskViewRequestType.ASSIGNED_VIEW)
                .entityId(entityId)
                .pageSize(1000)
                .pageNo(1)
                .listingRequestMeta(ListingRequestMeta.builder()
                        .additionalFilters(filters)
                        .build())
                .build();

        //call
        TaskSearchResponse actualResponse = taskDiscoveryService.getAgentTaskListing(ACTOR, request);

        //assert
        assertEquals(expectedResponse.size(), actualResponse.getTaskList().size());

    }

    @Test
    public void getTaskListingForAssignedAndDiscoveryViewTest() {
        //arrange
        String entityId = "entity id";
        TaskMetaInformation taskMetaInformation = new TaskMetaInformation(TaskMetaType.DEVICE_ID, "RANDOM_DEVICE_ID", true);
        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(taskMetaInformation);
        taskMetaInformationList.add(new TaskMetaInformation(TaskMetaType.LEAD_INTENT, "HOT", true));
        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().build());

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        setUpDefaultEs(tasks, false);
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class)))
                .thenReturn(TaskInstance.builder()
                        .actionId("EDC_ORDER_PLACEMENT")
                        .instanceMeta(TaskInstanceMeta.builder()
                                .taskMetaList(taskMetaInformationList)
                                .build())
                        .build());

        List<TaskMetaResponse> expectedResponse = new ArrayList<>();
        when(taskDefinitionService.getFromCache(any())).thenReturn(TaskDefinitionInstance.builder()
                        .taskDefinitionId("TASK_DEFINITION")
                        .definitionAttributes(TaskDefinitionAttributes.builder().leadConfig(LeadConfig.builder().leadCreation(List.of())
                                .leadUpdation(List.of()).build()).build())
                        .name("TASK_DEFINITION")
                .build());
        tasks.forEach(taskInstance ->
                expectedResponse.add(
                        discoveryTaskInstanceToTaskMeta(taskInstance,
                                taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                                        .taskDefinitionId(taskInstance.getTaskDefinitionId())
                                        .build()
                                ),
                                TaskInstance.builder()
                                        .instanceMeta(TaskInstanceMeta.builder()
                                                .taskMetaList(taskMetaInformationList)
                                                .build())
                                        .build())
                ));
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(TASK_DEFINITION_ID, "TASK_DEFINITION"));
        EntityTaskListingRequest request = EntityTaskListingRequest.builder()
                .entityId(entityId)
                .pageSize(1000)
                .pageNo(1)
                .listingRequestMeta(ListingRequestMeta.builder()
                        .additionalFilters(filters)
                        .build())
                .build();

        TaskSearchResponse actualResponse = taskDiscoveryService.getAllEntityActiveTasks(request);
        assertEquals(expectedResponse.size(), actualResponse.getTaskList().size());
    }

    //

    @Test
    public void testGetAllActiveTasksForCompletion() {
        //arrange
        EntityTaskListingRequest request = EntityTaskListingRequest.builder()
                .pageSize(10)
                .entityId("MID_SID")
                .pageNo(1)
                .build();
        TaskInstance taskInstance = TaskInstance.builder()
                .instanceMeta(TaskInstanceMeta.builder()
                        .build())
                .namespace(Namespace.CONSUMER)
                .build();
        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId("ActionId")
                .entityType(STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .createdBy("someone")
                .updatedBy("someone")
                .description("action description")
                .build();
        when(taskActionService.getFromCache(any())).thenReturn(taskActionInstance);
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceList(LegionTaskStateMachineState.AVAILABLE, 10);
        setUpDefaultEs(tasks, false);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        when(taskDefinitionService.getFromCache(any())).thenReturn(TaskDefinitionInstance.builder().taskDefinitionId("tdid").build());
        when(enricher.buildTaskTypeFilterQuery(any(), any())).thenReturn(new BoolQueryBuilder());
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(taskInstance);
        //call
        TaskSearchResponse response = taskDiscoveryService.getAllActiveTasksForCompletion(request, boolQueryBuilder);

        //assert
        Assert.assertEquals(10, response.getTaskCount());
        Assert.assertEquals(10, response.getTaskList().size());
    }
    @Test
    public void getTaskListing() {
        //arrangeA
        TaskListRequest request = TaskListRequest.builder()
                .pageSize(10)
                .pageNo(1)
                .build();
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceList(LegionTaskStateMachineState.COMPLETED, 10);
        setUpDefaultEs(tasks, false);

        //call
        TaskListResponse response = taskDiscoveryService.getTaskListing(request);

        //assert
        assertEquals(10, response.getCount());
        assertEquals(10, response.getTasks().size());
        assertEquals(request.getPageNo(), response.getPageNo());
        assertEquals(request.getPageSize(), response.getPageSize());
    }

    @Ignore(value = "This will be fixed later")
    public void searchAssignedViewSuccess() {
        TaskSearchRequest request = getAgentTaskSearchRequest(ACTOR, 1, 1);
        request.setSorter(Sorter.NONE);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, 1);
        Assertions.assertThrows(LegionException.class, () -> {
            taskDiscoveryService.search(ACTOR, request);
        });
    }


    @Test
    public void searchDiscoveryViewSuccess() {
        //No sorting
        //arrange
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getDiscoveryTaskSearchRequest(1, 1, TaskSearchRequestType.DISCOVERY_VIEW);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());

        request.setSorter(Sorter.NONE);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        //call
        Assertions.assertNotNull(taskDiscoveryService.search(ACTOR, request));
    }

    @Test
    public void searchEscalatedViewSuccess() {
        //No sorting
        //arrange
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        when(legionService.getAgentProfile(anyString())).thenReturn(AgentProfile.builder().name("mohit").build());
        when(atlasService.getSectorIds(any())).thenReturn(List.of("SECTOR"));
        TaskSearchRequest request = getDiscoveryTaskSearchRequest(1, 1, TaskSearchRequestType.ESCALATED_VIEW);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());

        request.setSorter(Sorter.NONE);
        when(taskDefinitionService.getFromCache(any())).thenReturn(TaskDefinitionInstance.builder()
                .taskDefinitionId("TASK_DEFINITION")
                .name("TASK_DEFINITION")
                .build());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        //call
        Assertions.assertNotNull(taskDiscoveryService.search(ACTOR, request));
    }

    @Test
    public void searchDiscoveryViewSuccessWithLocationSorter() {
        //Location sorting
        //arrange
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getDiscoveryTaskSearchRequest(1, 1, TaskSearchRequestType.DISCOVERY_VIEW);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());

        request.setSorter(Sorter.LOCATION);
        request.setLocation(EsLocationRequest.builder().lat(12.9245312).lon(77.6700838).build());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        setupSearchTest(request, 10, false);
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, 1);
        //call
        when(functionalQueryBuilder.getPrioritiseStrategyConfig(anyString())).thenReturn(PrioritiseStrategyConfig.builder()
                .functionScoringEnabled(false).build());
        TaskSearchResponse actualResponse = taskDiscoveryService.search(ACTOR, request);
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    public void searchDiscoveryTaskPrioritySuccess() {
        when(functionalQueryBuilder.getPrioritiseStrategyConfig(anyString()))
                .thenReturn(PrioritiseStrategyConfig.builder().functionScoringEnabled(true).build());
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getSectorDiscoveryTaskSearchRequestWithLocationSort(1, 1);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());
        SearchResponse searchResponse = Mockito.mock(SearchResponse.class);
        SearchHits searchHits = Mockito.mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getTotalHits()).thenReturn(new TotalHits(10, TotalHits.Relation.EQUAL_TO));
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);

        when(esRepository.searchWithPrioritySorting(anyString(), any(), anyInt(), anyInt())).thenReturn(searchResponse);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        TaskSearchResponse finalSearchResponse = taskDiscoveryService.search(ACTOR, request);
        Assertions.assertNotNull(finalSearchResponse);
    }

    @Test
    public void searchNonSectorDiscoveryTaskPrioritySuccess() {
        when(functionalQueryBuilder.getPrioritiseStrategyConfig(anyString()))
                .thenReturn(PrioritiseStrategyConfig.builder().functionScoringEnabled(true).build());
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getDiscoveryTaskSearchRequestWithLocationSort(1, 1);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());
        SearchResponse searchResponse = Mockito.mock(SearchResponse.class);
        SearchHits searchHits = Mockito.mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getTotalHits()).thenReturn(new TotalHits(10, TotalHits.Relation.EQUAL_TO));
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);

        when(esRepository.searchWithPrioritySorting(anyString(), any(), anyInt(), anyInt())).thenReturn(searchResponse);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        TaskSearchResponse finalSearchResponse = taskDiscoveryService.search(ACTOR, request);
        Assertions.assertNotNull(finalSearchResponse);
    }

    @Test
    public void searchDiscoveryTaskLocationSuccess() {
        when(functionalQueryBuilder.getPrioritiseStrategyConfig(anyString()))
                .thenReturn(PrioritiseStrategyConfig.builder().functionScoringEnabled(false).build());
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getDiscoveryTaskSearchRequestWithLocationSort(1, 1);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());
        SearchResponse searchResponse = Mockito.mock(SearchResponse.class);
        SearchHits searchHits = Mockito.mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getTotalHits()).thenReturn(new TotalHits(10, TotalHits.Relation.EQUAL_TO));
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);

        when(esRepository.searchWithGeoSorting(anyString(), any(), anyInt(), anyInt(), any())).thenReturn(searchResponse);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        TaskSearchResponse finalSearchResponse = taskDiscoveryService.search(ACTOR, request);
        Assertions.assertNotNull(finalSearchResponse);
    }

    @Test
    public void searchDiscoveryPrioritySuccess() {
        //No sorting
        //arrange
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getSectorDiscoveryTaskSearchRequest(1, 15);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());

        request.setSorter(Sorter.NONE);
        TaskSearchResponse expectedResponse = setupSearchTest(request, 10, false);

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, 1);
        //call
        taskDiscoveryService.search(ACTOR, request);
    }

    @Test
    public void searchDiscoveryViewSuccessWithSorter() {
        //Location sorting
        //arrange
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SECTOR")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        TaskSearchRequest request = getDiscoveryTaskSearchRequest(1, 1, TaskSearchRequestType.DISCOVERY_VIEW);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());

        request.setSorter(Sorter.LOCATION);
        request.setLocation(EsLocationRequest.builder().lat(12.9245312).lon(77.6700838).build());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        setupSearchTest(request, 10, false);
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, 1);
        //call
        TaskSearchResponse actualResponse = taskDiscoveryService.search(ACTOR, request);
        Assertions.assertNotNull(actualResponse);
    }

    @Test(expected = LegionException.class)
    public void searchPageNumberAndSizeValidation() {
        //arrange
        TaskSearchRequest request = getDiscoveryTaskSearchRequest(30, 10, TaskSearchRequestType.DISCOVERY_VIEW);
        request.setSorter(Sorter.NONE);
        setupSearchTest(request, 10, false);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());
        //call
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(ACTOR, request)).thenReturn(queryBuilder);
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, 1);
        taskDiscoveryService.search(ACTOR, request);

    }

    @Test
    public void getById() {
        //arrange
        DiscoveryTaskInstance expectedResponse = getDiscoveryTaskInstance(LegionTaskStateMachineState.COMPLETED);
        TaskByIdRequest request = TaskByIdRequest.builder()
                .taskInstanceId(expectedResponse.getTaskInstanceId())
                .build();
        when(esRepository.get(expectedResponse.getTaskInstanceId(), TASK_INDEX, DiscoveryTaskInstance.class))
                .thenReturn(expectedResponse);

        //call
        DiscoveryTaskInstance actualResponse = taskDiscoveryService.getById(request);

        //assert
        Assert.assertSame(expectedResponse, actualResponse);
    }

    @Test
    public void getTaskStats() {
        List<StatFilter> stats = new ArrayList<>();
        Map<LegionTaskStateMachineState, Long> map = new EnumMap<>(LegionTaskStateMachineState.class);
        stats.add(getTaskStateStatFilter("Approved", java.util.Arrays.asList(VERIFICATION_SUCCESS), map));
        stats.add(getTaskStateStatFilter("In Review", java.util.Arrays.asList(COMPLETED), map));
        stats.add(getTaskStateStatFilter("Rejected", java.util.Arrays.asList(VERIFICATION_FAILED), map));
        stats.add(getTaskStateStatFilter("Pending", java.util.Arrays.asList(BOUNDED_ASSIGNED, SELF_ASSIGNED, STARTED), map));
        stats.add(StatFilter.builder()
                .displayText("Points")
                .value(0.0)
                .clickable(false)
                .build());
        UserStatsAndFilters expectedResponse = UserStatsAndFilters.builder()
                .stats(stats)
                .build();
        Aggregations aggregations = getDummyStatsAggregation(STATUS);
        when(esRepository.getAggregations(eq(TASK_INDEX), any(), any(), anyInt())).thenReturn(aggregations);
        UserStatsAndFilters actualResponse = taskDiscoveryService.getUserStats(TaskStatsRequest.builder()
                .assignedTo(ACTOR)
                .build());
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void getEntityStats() {
        EntityStatsResponse expectedResponse = EntityStatsResponse.builder()
                .tasksPresent(false)
                .attributes(Arrays.asList(
                        TaskMetaAttribute.builder()
                                .value(0l)
                                .displayText(TASKS_UNIT)
                                .build(),
                        TaskMetaAttribute.builder()
                                .value(0.0)
                                .displayText(POINTS_UNIT)
                                .build()
                ))
                .build();
        Aggregations aggregations = TaskTestUtils.getDummyStatsAggregation(ENTITY_TYPE);
        when(esRepository.getAggregations(eq(TASK_INDEX), any(), any(), anyInt()))
                .thenReturn(aggregations);
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("SEC1")
                .active(true)
                .agentId("actor")
                .build());
        when(legionService.getAgentSectors("actor")).thenReturn(sectors);
        when(legionService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                        .agentType(AgentType.AGENT)
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("MASS_RETAIL")
                        .build())).build());
        EntityStatsResponse actualResponse = taskDiscoveryService.getEntityStats("actor", "entityId");
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void getTaskFilterOptions() {
        //arrange
        TaskFilters taskFilters = getTaskFilters();
        ViewWiseFilters expectedAssignedFilters = getTaskFilters().getAssignedFilterOptions();
        expectedAssignedFilters.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedAssignedFilters.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        expectedAssignedFilters.getFilters().add(TaskFilterOptions.builder()
                .key(OBJECTIVES_KEYWORD)
                .displayText(OBJECTIVE)
                .options(Collections.emptyList())
                .multipleSelect(true)
                .build());
        expectedAssignedFilters.getFilters().add(TaskFilterOptions.builder()
                .key(ACTION_ID)
                .displayText("Task Type")
                .options(Collections.singletonList(FilterOptions.builder()
                        .key("ACTION_ID")
                        .fieldName("action_id")
                        .displayText("Test Task")
                        .build()))
                .multipleSelect(true)
                .build());

        ViewWiseFilters expectedDiscoveryFilters = getTaskFilters().getDiscoveryFilterOptions();
        expectedDiscoveryFilters.getFilters().add(TaskFilterOptions.builder()
                .key(OBJECTIVES_KEYWORD)
                .displayText(OBJECTIVE)
                .options(Collections.emptyList())
                .multipleSelect(true)
                .build());
        expectedDiscoveryFilters.getFilters().add(TaskFilterOptions.builder()
                .key(ACTION_ID)
                .displayText("Task Type")
                .options(Collections.singletonList(FilterOptions.builder()
                        .key("ACTION_ID")
                        .fieldName("action_id")
                        .displayText("Test Task")
                        .build()))
                .multipleSelect(true)
                .build());

        StoredTaskAction storedTaskAction = StoredTaskAction.builder()
                .actionId("ACTION_ID")
                .createdBy("saransh")
                .entityType(STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .description("Test Task")
                .build();
        taskActionRepository.save(storedTaskAction);

        Aggregations aggregations = getDummyStatsAggregation(ACTION_ID);
        when(esRepository.getAggregations(eq(TASK_INDEX), any(), any(), anyInt())).thenReturn(aggregations);

        Aggregations aggregation = Mockito.mock(Aggregations.class);
        Mockito.when(searchResponse.getAggregations()).thenReturn(aggregation);
        Map<String, Aggregation> aggregationMap = new HashMap<>();
        ParsedLongTerms pointAggregation = Mockito.mock(ParsedLongTerms.class);
        ParsedStringTerms actionAggregation = Mockito.mock(ParsedStringTerms.class);
        ParsedStringTerms objectiveAggregation = Mockito.mock(ParsedStringTerms.class);
        aggregationMap.put(POINTS, pointAggregation);
        aggregationMap.put(ACTION_ID, actionAggregation);
        aggregationMap.put(ATTRIBUTES, objectiveAggregation);
        Mockito.when(aggregation.getAsMap()).thenReturn(aggregationMap);
        when(esRepository.getAggregationResult(eq(TASK_INDEX), any(QueryBuilder.class), anyList())).thenReturn(searchResponse);
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        filterOptions.put(OBJECTIVES, new ArrayList<>());
        filterOptions.put(ACTION_ID, new ArrayList<>());
        taskDiscoveryService.getTaskFilters();
        when(requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, TaskSearchRequestType.DISCOVERY_VIEW)).thenReturn(expectedDiscoveryFilters);
        ViewWiseFilters actualDiscoveryFilters = taskDiscoveryService.getTaskFilterOptions(TaskSearchRequestType.DISCOVERY_VIEW);
        assertEquals(expectedDiscoveryFilters.getMaxDate(), actualDiscoveryFilters.getMaxDate());
    }


    private SectorTaskSearchResponse setupSectorTaskList(SectorMapViewTaskSearchRequest request, int taskCount, boolean includeGeoLocationOutputInGeoSort) {
        List<DiscoveryTaskInstance> tasks = getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState.COMPLETED, taskCount);
        tasks.stream().forEach(task -> {
            task.setPolygonIds(List.of(request.getSectorId()));
            task.setLocation(EsLocationRequest.builder().lat(Math.random() * 90)
                    .lon(Math.random() * 180).build());
        });
        setUpDefaultEs(tasks, includeGeoLocationOutputInGeoSort);
        List<TaskMapViewDetails> taskMapViewDetailsList = tasks.stream()
                .map(TaskMapViewDetails::new)
                .toList();
        return SectorTaskSearchResponse.builder()
                .taskCount(tasks.size())
                .taskDetails(taskMapViewDetailsList)
                .build();
    }

    @Test
    public void getAssignedTaskList() {
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .agentId(ACTOR)
                .active(true)
                .managerId("manager")
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.TSM)
                .build());
        SectorMapViewTaskSearchRequest request = TaskTestUtils.getAssignedSectorTaskRequest(ACTOR, 1, 10);
        SearchHits searchHits = mock(SearchHits.class);
        TotalHits totalHits = mock(TotalHits.class);
        SearchHit[] searchHits1 = new SearchHit[]{mock(SearchHit.class)};
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchResponse.getHits().getTotalHits()).thenReturn(totalHits);
        when(searchResponse.getHits().getHits()).thenAnswer(invocation -> searchHits1);
        SectorTaskSearchResponse actualResponse = taskDiscoveryService.getTaskList(ACTOR, request);
        Assertions.assertTrue(true);
//        Assert.assertEquals(70,actualResponse.getTaskCount());
    }

    @Test
    public void getDiscoverableTaskList() {
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("sectorId")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .agentId(ACTOR)
                .active(true)
                .managerId("manager")
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.TSM)
                .build());
        SectorMapViewTaskSearchRequest request = TaskTestUtils.getDiscoverableSectorTaskRequest(ACTOR, 1, 10);
        SearchHits searchHits = mock(SearchHits.class);
        TotalHits totalHits = mock(TotalHits.class);
        SearchHit[] searchHits1 = new SearchHit[]{mock(SearchHit.class)};
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchResponse.getHits().getTotalHits()).thenReturn(totalHits);
        when(searchResponse.getHits().getHits()).thenAnswer(invocation -> searchHits1);
        SectorTaskSearchResponse actualResponse = taskDiscoveryService.getTaskList(ACTOR, request);
        Assertions.assertTrue(true);
//        Assert.assertEquals(70,actualResponse.getTaskCount());
//        Assert.assertEquals(0, actualResponse.getTaskDetails().size());
    }

    @Test
    public void getTaskListWithSortingForTSM() {
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("string")
                .build());
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .agentType(AgentType.AGENT)
                .active(true)
                .agentId(ACTOR)
                .managerId("M!")
                .sectors(Collections.singletonList("Z"))
                .attributes(Collections.singletonList(TagsAttribute.builder()
                        .active(true)
                        .tag("mohit")
                        .build())).build());
        SectorMapViewTaskSearchRequest request = TaskTestUtils.getDiscoverableSectorTaskRequest(ACTOR, 1, 10);

        request.setSorter(Sorter.DUE_DATE);
        SearchHits searchHits = mock(SearchHits.class);
        TotalHits totalHits = mock(TotalHits.class);
        SearchHit[] searchHits1 = new SearchHit[]{mock(SearchHit.class)};
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchResponse.getHits().getTotalHits()).thenReturn(totalHits);
        when(searchResponse.getHits().getHits()).thenAnswer(invocation -> searchHits1);
        SectorTaskSearchResponse actualResponse = taskDiscoveryService.getTaskList(ACTOR, request);
        Assertions.assertTrue(true);
//        Assert.assertEquals(70,actualResponse.getTaskCount());
//        Assert.assertEquals(0, actualResponse.getTaskDetails().size());

    }

    @Test(expected = LegionException.class)
    public void getTaskListByAgentTest() {
        List<AgentSectorResponse> sectors = new ArrayList<>();
        sectors.add(AgentSectorResponse.builder()
                .sectorId("string")
                .build());
        when(profileCRUDService.getAgentSectors(ACTOR)).thenReturn(sectors);
        when(profileCRUDService.getAgentProfile(ACTOR)).thenReturn(AgentProfile.builder()
                .agentId(ACTOR)
                .active(true)
                .managerId("manager")
                .sectors(Collections.singletonList("string1"))
                .agentType(AgentType.AGENT)
                .build());
        SectorMapViewTaskSearchRequest request = TaskTestUtils.getDiscoverableSectorTaskRequest(ACTOR, 1, 10);
        doThrow(LegionException.error(SECTOR_MISMATCH)).when(validations).validateTaskSectorWithUser(any(), anyList());
        request.setSorter(Sorter.DUE_DATE);
        setupSectorTaskList(request, request.getPageSize(), false);
        taskDiscoveryService.getTaskList(ACTOR, request);
    }

    @Test
    public void getServiceBaseTasks() {
        when(esRepository.search(eq(TASK_INDEX), any(BoolQueryBuilder.class), anyInt(), anyInt(), any()))
                .thenReturn(List.of(DiscoveryTaskInstance.builder().entityId(ENTITY_ID).build()));
        List<DiscoveryTaskInstance> tasks = taskDiscoveryService.getServiceBaseTasks(ENTITY_ID);
        Assert.assertFalse(tasks.isEmpty());
        assertEquals(ENTITY_ID, tasks.get(0).getEntityId());
    }

    @Test
    public void getTaskDetails() {
        TaskDetailRequest taskDetailRequest = TaskDetailRequest.builder().taskInstanceId("TD134141").build();
        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .entityType(EntityType.TASK)
                .actionId("ACTION_SHASHA")
                .subSteps(Arrays.asList(
                        Map.of(STEP_KEY_NAME, "1"),
                        Map.of(STEP_KEY_NAME, "3")
                ))
                .description("action description")
                .build();

        TaskMetaInformation taskMetaInformation = new TaskMetaInformation(TaskMetaType.DEVICE_ID, "RANDOM_DEVICE_ID", true);
        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(taskMetaInformation);

        TaskInstance taskInstance = TaskInstance.builder()
                .instanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(taskMetaInformationList)
                        .build())
                .namespace(Namespace.CONSUMER)
                .build();

        DiscoveryTaskInstance discoveryTaskInstance = getDiscoveryTaskInstance(LegionTaskStateMachineState.COMPLETED);
        discoveryTaskInstance.setEntityType(STORE);
        discoveryTaskInstance.setAttributes(ImmutableMap.of("objective", ImmutableSet.of("objective1")));
        when(esRepository.get(any(), any(), any())).thenReturn(discoveryTaskInstance);

        when(taskAttributeService.getFromCache(anyString()))
                .thenReturn(TaskAttributeInstance.builder()
                        .name("name")
                        .attributeType(AttributeType.OBJECTIVE)
                        .taskattributeValue("taskAttributevalue")
                        .build());

        when(taskActionService.getFromDB(any())).thenReturn(taskActionInstance);
        when(validationService.checkIfDefinitionIsWhitelisted(any())).thenReturn(false);
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(taskInstance);
        when(taskDefinitionService.getFromDb(any())).thenReturn(TaskDefinitionInstance.builder()
                .taskDefinitionId(discoveryTaskInstance.getTaskDefinitionId())
                .name("TASK_DEFINITION")
                .build());
        taskDefinitionRepository.save(StoredTaskDefinition.builder()
                .namespace(taskInstance.getNamespace())
                .name("name")
                .createdBy("someone")
                .points(10)
                .actionId("ACTION_SHASHA")
                .priority(Priority.P1)
                .taskDefinitionId(discoveryTaskInstance.getTaskDefinitionId())
                .definitionAttributes(SerDe.writeValueAsBytes(TaskDefinitionAttributes.builder()
                        .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder()
                                        .config(List.of(IntentWithRemarks.builder().intent("lead").build()))
                                        .build()))
                                .build())
                        .build()))
                .build());

        TaskDetailResponse response = taskDiscoveryService.getTaskDetailsById(taskDetailRequest);
        assertEquals(discoveryTaskInstance.getTaskInstanceId(), response.getTaskInstanceId());

    }


    @Test
    public void getTaskDetails_ForWhitelistedDefinitions() {
        TaskDetailRequest taskDetailRequest = TaskDetailRequest.builder().taskInstanceId("TD134141").build();
        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .entityType(EntityType.TASK)
                .actionId("ACTION_SHASHA")
                .subSteps(Arrays.asList(
                        Map.of(STEP_KEY_NAME, "1"),
                        Map.of(STEP_KEY_NAME, "3")
                ))
                .description("action description")
                .build();

        TaskMetaInformation taskMetaInformation = new TaskMetaInformation(TaskMetaType.DEVICE_ID, "RANDOM_DEVICE_ID", true);
        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(taskMetaInformation);

        TaskInstance taskInstance = TaskInstance.builder()
                .instanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(taskMetaInformationList)
                        .build())
                .namespace(Namespace.CONSUMER)
                .build();

        DiscoveryTaskInstance discoveryTaskInstance = getDiscoveryTaskInstance(LegionTaskStateMachineState.COMPLETED);
        when(validationService.checkIfDefinitionIsWhitelisted(any())).thenReturn(true);
        discoveryTaskInstance.setEntityType(STORE);
        discoveryTaskInstance.setAttributes(ImmutableMap.of("objective", ImmutableSet.of("objective1")));
        when(esRepository.get(any(), any(), any())).thenReturn(discoveryTaskInstance);

        when(taskAttributeService.getFromCache(anyString()))
                .thenReturn(TaskAttributeInstance.builder()
                        .name("name")
                        .attributeType(AttributeType.OBJECTIVE)
                        .taskattributeValue("taskAttributevalue")
                        .build());

        when(taskActionService.getFromDB(any())).thenReturn(taskActionInstance);
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(taskInstance);
        when(taskDefinitionService.getFromDb(any())).thenReturn(TaskDefinitionInstance.builder()
                .taskDefinitionId(discoveryTaskInstance.getTaskDefinitionId())
                .name("TASK_DEFINITION")
                .build());
        taskDefinitionRepository.save(StoredTaskDefinition.builder()
                .namespace(taskInstance.getNamespace())
                .name("name")
                .createdBy("someone")
                .points(10)
                .actionId("ACTION_SHASHA")
                .priority(Priority.P1)
                .taskDefinitionId(discoveryTaskInstance.getTaskDefinitionId())
                .definitionAttributes(SerDe.writeValueAsBytes(TaskDefinitionAttributes.builder()
                        .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder()
                                        .config(List.of(IntentWithRemarks.builder().intent("lead").build()))
                                        .build()))
                                .build())
                        .build()))
                .build());

        TaskDetailResponse response = taskDiscoveryService.getTaskDetailsById(taskDetailRequest);
        assertEquals(discoveryTaskInstance.getTaskInstanceId(), response.getTaskInstanceId());

    }

    @Test
    public void testGetOpenTasksCountByAction() {
        TasksStatsRequest request = new TasksStatsRequest(1722771242000L);
        Aggregations mockAggregations = getDummyStatsAggregation("action_aggs");
        List<TaskStateStatsByAction> expectedList = new ArrayList<>();
        when(esRepository.getAggregations(anyString(), any(BoolQueryBuilder.class), any(AggregationBuilder.class), anyInt()))
                .thenReturn(mockAggregations);

        List<TaskStateStatsByAction> result = taskDiscoveryService.getTasksStateStatsByAction(request);

        assertEquals(expectedList, result);
    }

    @Test
    public void testFetchAgentsEligibleForTask_Success() {
        // Arrange
        TaskMetaInformation taskMetaInfo = new TaskMetaInformation(TaskMetaType.DEVICE_ID, "DEVICE_ID", true);
        List<TaskMetaInformation> taskMetaInfoList = Arrays.asList(
                taskMetaInfo,
                new TaskMetaInformation(TaskMetaType.LEAD_INTENT, "HOT", true)
        );

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        setUpDefaultEs(tasks, false);

        SectorEntity sectorResponse = DiscoveryTestUtils.getSectorEntity();
        sectorResponse.setLocation(EsLocationRequest.builder().lat(2.0).lon(2.0).build());
        List<String> sectorIds = Collections.singletonList("sector-id");
        sectorResponse.setPolygonIds(sectorIds);

        when(entityStore.getById(EntityStoreRequest.builder().entityType(EntityType.SECTOR).referenceId("entityId").build())).thenReturn(Optional.of(sectorResponse));
        when(taskDefinitionService.getFromCache(any())).thenReturn(TaskDefinitionInstance.builder().build());

        Map<String, List<AgentProfile>> profilesPerSector = new HashMap<>();
        profilesPerSector.put("sector-id", Collections.singletonList(AgentProfile.builder().agentId("agent1").status(AgentStatus.ACTIVATED).build()));
        AgentProfilesInSectorResponse agentProfilesInSectorResponse = AgentProfilesInSectorResponse.builder().profilesPerSector(profilesPerSector).build();

        when(legionService.getAgentProfilesInSector(sectorIds)).thenReturn(agentProfilesInSectorResponse);
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(TaskInstance.builder()
                .actionId("EDC_ORDER_PLACEMENT")
                .instanceMeta(TaskInstanceMeta.builder().taskMetaList(taskMetaInfoList).build())
                .build()
        );

        AgentTaskEligibilityRequest agentTaskEligibilityRequest = AgentTaskEligibilityRequest.builder()
                .taskInstanceId("task-id1")
                .entityType(EntityType.SECTOR)
                .entityId("entityId")
                .build();

        when(enricher.buildAgentsEligibleForTaskQuery(List.of("agent1"), "task-id1")).thenReturn(new BoolQueryBuilder());
        when(esRepository.searchWithSorting(TASK_INDEX, new BoolQueryBuilder(), 1, 10000, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC)).thenReturn(searchResponse);

        SearchHits searchHits = Mockito.mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);

        // Call
        AgentTaskEligibilityResponse expectedResponse = AgentTaskEligibilityResponse.builder().eligibleAgentsIds(Collections.emptyList()).build();
        AgentTaskEligibilityResponse actualResponse = taskDiscoveryService.fetchAgentsEligibleForTask(ACTOR, agentTaskEligibilityRequest);

        // Assert
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testFetchEligibleAgent_NotNullSearchHits() {
        // Arrange
        TaskMetaInformation taskMetaInfo = new TaskMetaInformation(TaskMetaType.DEVICE_ID, "DEVICE_ID", true);
        List<TaskMetaInformation> taskMetaInfoList = Arrays.asList(
                taskMetaInfo,
                new TaskMetaInformation(TaskMetaType.LEAD_INTENT, "HOT", true)
        );

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        setUpDefaultEs(tasks, false);

        SectorEntity sectorResponse = DiscoveryTestUtils.getSectorEntity();
        sectorResponse.setLocation(EsLocationRequest.builder().lat(2.0).lon(2.0).build());
        List<String> sectorIds = Collections.singletonList("sector-id2");
        sectorResponse.setPolygonIds(sectorIds);

        when(entityStore.getById(EntityStoreRequest.builder().entityType(EntityType.SECTOR).referenceId("entityId2").build()))
                .thenReturn(Optional.of(sectorResponse));
        when(taskDefinitionService.getFromCache(any())).thenReturn(TaskDefinitionInstance.builder().build());

        Map<String, List<AgentProfile>> profilesPerSector = new HashMap<>();
        profilesPerSector.put("sector-id2", Collections.singletonList(AgentProfile.builder().agentId("agent1").status(AgentStatus.ACTIVATED).build()));
        AgentProfilesInSectorResponse agentProfilesInSectorResponse = AgentProfilesInSectorResponse.builder().profilesPerSector(profilesPerSector).build();

        when(legionService.getAgentProfilesInSector(sectorIds)).thenReturn(agentProfilesInSectorResponse);
        when(taskInstanceManagementService.getById(any(TaskByIdRequest.class))).thenReturn(TaskInstance.builder()
                .actionId("EDC_ORDER_PLACEMENT")
                .instanceMeta(TaskInstanceMeta.builder().taskMetaList(taskMetaInfoList).build())
                .build()
        );

        AgentTaskEligibilityRequest agentTaskEligibilityRequest = AgentTaskEligibilityRequest.builder()
                .taskInstanceId("task-id1")
                .entityType(EntityType.SECTOR)
                .entityId("entityId2")
                .build();

        when(enricher.buildAgentsEligibleForTaskQuery(List.of("agent1"), "task-id1")).thenReturn(new BoolQueryBuilder());
        when(esRepository.searchWithSorting(TASK_INDEX, new BoolQueryBuilder(), 1, 10000, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC)).thenReturn(searchResponse);

        // Mock search hits
        SearchHit searchHit = Mockito.mock(SearchHit.class);
        SearchHits searchHits = Mockito.mock(SearchHits.class);

        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[] { searchHit });

        when(searchHit.getId()).thenReturn("hit-id");
        when(searchHit.getMatchedQueries()).thenReturn(new String[] { "agent1" });

        AgentTaskEligibilityResponse expectedResponse = AgentTaskEligibilityResponse.builder().eligibleAgentsIds(Collections.singletonList("agent1")).build();

        // Call
        AgentTaskEligibilityResponse actualResponse = taskDiscoveryService.fetchAgentsEligibleForTask(ACTOR, agentTaskEligibilityRequest);

        // Assert
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testFetchAgentsEligibleForTask_FetchEntityId_Failure() {

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        setUpDefaultEs(tasks, false);

        doThrow(new RuntimeException()).when(entityStore).getById(EntityStoreRequest.builder().entityType(EntityType.SECTOR).referenceId("entityId").build());
        AgentTaskEligibilityRequest agentTaskEligibilityRequest = AgentTaskEligibilityRequest.builder()
                .taskInstanceId("task-id1")
                .entityType(EntityType.SECTOR)
                .entityId("entityId").build();
        Assertions.assertThrows(RuntimeException.class, () -> taskDiscoveryService.fetchAgentsEligibleForTask(ACTOR, agentTaskEligibilityRequest));
    }

    @Test
    public void testFetchAgentsEligibleForTask_EmptyEntityStore_Failure() {

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        setUpDefaultEs(tasks, false);
        when(entityStore.getById(EntityStoreRequest.builder().entityType(EntityType.SECTOR).referenceId("entityId").build())).thenReturn(Optional.ofNullable(null));

        AgentTaskEligibilityRequest agentTaskEligibilityRequest = AgentTaskEligibilityRequest.builder()
                .taskInstanceId("task-id1")
                .entityType(EntityType.SECTOR)
                .entityId("entityId").build();
        Assertions.assertThrows(RuntimeException.class, () -> taskDiscoveryService.fetchAgentsEligibleForTask(ACTOR, agentTaskEligibilityRequest));
    }


    @Test
    public void testFetchAgentsEligibleForTask_FetchActiveAgents_Failure() {

        SectorEntity sectorResponse = DiscoveryTestUtils.getSectorEntity();
        sectorResponse.setLocation(EsLocationRequest.builder()
                .lat(2.0)
                .lon(2.0)
                .build());

        List<String>sectorIds = Collections.singletonList("sector-id");
        sectorResponse.setPolygonIds(sectorIds);

        List<DiscoveryTaskInstance> tasks = getAgentListingTestTasks();
        when(entityStore.getById(EntityStoreRequest.builder().entityType(EntityType.SECTOR).referenceId("entityId").build())).thenReturn(Optional.of(sectorResponse));
        setUpDefaultEs(tasks, false);

        doThrow(new RuntimeException()).when(legionService).getAgentProfilesInSector(sectorIds);
        AgentTaskEligibilityRequest agentTaskEligibilityRequest = AgentTaskEligibilityRequest.builder()
                .taskInstanceId("task-id1")
                .entityType(EntityType.SECTOR)
                .entityId("entityId").build();
        Assertions.assertThrows(RuntimeException.class, () -> taskDiscoveryService.fetchAgentsEligibleForTask(ACTOR, agentTaskEligibilityRequest));
    }



}




