package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.survey.FeedbackPayload;
import com.phonepe.merchant.gladius.models.survey.HtmContentResponse;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EventBasedTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.BrickbatService;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskDeleteMessage;
import com.phonepe.merchant.legion.tasks.actor.message.EventBasedTaskCreationMessage;
import com.phonepe.merchant.legion.tasks.actor.message.QcTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.cache.CampaignQuestionsCache;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.impl.ClientTaskServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import com.phonepe.models.merchants.tasks.EligibleEventBasedTaskType;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationClient;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationConfig;
import com.phonepe.models.merchants.tasks.TaskCreationConfig;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.brickbat.models.entityconfig.EntityType;
import com.phonepe.platform.brickbat.models.feedback.StoredFeedback;
import com.phonepe.platform.brickbat.models.feedback.StoredSurvey;
import com.phonepe.platform.brickbat.models.question.DateQuestion;
import com.phonepe.platform.brickbat.models.question.EntityRatingQuestion;
import com.phonepe.platform.brickbat.models.question.FeedbackReasonQuestion;
import com.phonepe.platform.brickbat.models.question.MCQGridQuestion;
import com.phonepe.platform.brickbat.models.question.MCQQuestion;
import com.phonepe.platform.brickbat.models.question.Question;
import com.phonepe.platform.brickbat.models.question.RatingQuestion;
import com.phonepe.platform.brickbat.models.question.TextQuestion;
import com.phonepe.platform.brickbat.models.question.options.Option;
import com.phonepe.platform.brickbat.models.question.questionresponse.DateQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.EntityRatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.FeedbackReasonQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQGridQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.OptionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.RatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.TextQuestionResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
/**
 * <AUTHOR> puri
 */

@Slf4j
public class MiscResourcesTest {

    private static BrickbatService brickbatService;
    private static MiscResource miscResource;
    private static MockedStatic<TaskActionMessagePublisher> taskActionMessagePublisherMockedStatic;
    private static MetricRegistry metricRegistry;
    private static CacheUtils cacheUtils;
    private static ClientTaskService clientTaskService;
    private static FoxtrotEventExecutor eventExecutor;
    private static TaskManagementService taskManagementService;
    private static TaskDiscoveryService taskDiscoveryService;

    private static Validator validator;

    @BeforeClass
    public static void init() {
        clientTaskService = mock(ClientTaskServiceImpl.class);
        eventExecutor = mock(FoxtrotEventExecutor.class);
        taskActionMessagePublisherMockedStatic = mockStatic(TaskActionMessagePublisher.class);
        brickbatService = mock(BrickbatService.class);
        SerDe.init(new ObjectMapper());
        metricRegistry = new MetricRegistry();
        cacheUtils = new CacheUtils();
        taskManagementService = mock(TaskManagementService.class);
        Map<CacheName, CacheConfig> cacheConfigs = Map.of(CacheName.CAMPAIGN_QUESTIONS, new CacheConfig());
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        validator = Validation.buildDefaultValidatorFactory().getValidator();
        miscResource = new MiscResource(brickbatService, clientTaskService, new CampaignQuestionsCache(cacheConfigs, () -> brickbatService, metricRegistry, cacheUtils), eventExecutor, taskManagementService, taskDiscoveryService);
        LeadManagementConfiguration.create(List.of(), List.of(ActionToRemarkConfig.builder()
                        .taskType(UserGenTaskType.USER_CREATED_SS_DEPLOYMENT)
                        .config(List.of(IntentWithRemarks.builder()
                                        .intent("NOT_INTERESTED")
                                .build(), IntentWithRemarks.builder()
                                .intent("TASK_DONE")
                                .build()))
                .build()));
    }

    @AfterClass
    public static void tearDownClass() {
        taskActionMessagePublisherMockedStatic.close();
        taskActionMessagePublisherMockedStatic = null;
    }

    @Test
    public void getFeedbackHTMLTest() {
        MCQQuestionResponse mcqQuestionResponse = new MCQQuestionResponse();
        OptionResponse mcqQuestionOptionResponse = new OptionResponse();
        mcqQuestionOptionResponse.setKey("A");
        mcqQuestionOptionResponse.setText("text");
        mcqQuestionResponse.setAnswers(List.of(mcqQuestionOptionResponse));
        GenericResponse<StoredFeedback> storedFeedbackGenericResponse = GenericResponse.<StoredFeedback>builder()
                .data(StoredSurvey.builder()
                        .userResponses(Map.of("QuestionId",
                                new TextQuestionResponse("QuestionId", "sectionId", "answer"),
                                "QuestionId2", mcqQuestionResponse,
                                "questionId3", new EntityRatingQuestionResponse(),
                                "questionId4", new RatingQuestionResponse(),
                                "questionId5", new FeedbackReasonQuestionResponse(),
                                "questionId6", new MCQGridQuestionResponse(),
                                "questionId7", new DateQuestionResponse())
                        ).rating(null)
                        .build()).success(true)
                .build();

        GenericResponse<Map<String, Question>> campaignQuestions = GenericResponse.<Map<String, Question>>builder()
                .success(true)
                .data(Map.of("QuestionId",
                        new TextQuestion(),
                        "QuestionId2", MCQQuestion.builder().questionId("QuestionId2").title("home delivery").
                                options(Map.of("1", new Option("A", "Yes", true, 200, false))).build(),
                        "questionId3", new EntityRatingQuestion(),
                        "questionId4", new RatingQuestion(),
                        "questionId5", new FeedbackReasonQuestion(),
                        "questionId6", new MCQGridQuestion(),
                        "questionId7", new DateQuestion()))
                .build();
        when(brickbatService.getSurveyResult(anyString())).thenReturn(storedFeedbackGenericResponse);
        when(brickbatService.getCampaignQuestions(anyString())).thenReturn(campaignQuestions);
        FeedbackPayload feedbackPayload = FeedbackPayload.builder()
                .brickbatCampaignId("id")
                .brickbatEntityId("id")
                .brickbatEntityType(EntityType.SURVEY.name())
                .brickbatNamespace("LEGION")
                .build();
        GenericResponse<HtmContentResponse> finalHtml = miscResource.getFeedbackResult(Optional.empty(), feedbackPayload);
        Assert.assertNotNull(finalHtml);
        Assert.assertNotNull(finalHtml.getData());
        Assert.assertNotNull(finalHtml.getData().getHtmlContent());
        Assert.assertFalse(finalHtml.getData().getHtmlContent().isEmpty());
    }

    @Test
    public void createAndAssignTask() {
        QcTaskCreateAndAssignMessage request = QcTaskCreateAndAssignMessage.builder()
                .taskInstanceId("taskId")
                .build();
        when(TaskActionMessagePublisher.createAndAssignQcTask(request)).thenReturn(true);
        GenericResponse<Boolean> genericResponse = miscResource.createAndAssignTask(Optional.empty(), "taskId");
        Assert.assertTrue(genericResponse.getData());
        Assert.assertTrue(genericResponse.isSuccess());
    }

    @Test()
    public void createTaskFromEvent() {
        Map<EligibleEventBasedTaskType, TaskCreationConfig> map = new HashMap<>();
        map.put(EligibleEventBasedTaskType.OQC_VALIDATION, TaskCreationConfig.builder()
                .taskDefinitionId("T")
                .campaignId("C")
                .build());
        EventBasedTaskCreationRequest request = EventBasedTaskCreationRequest.builder()
                .taskType(EligibleEventBasedTaskType.OQC_VALIDATION)
                .markAvailable(true)
                .entityId("E")
                .config(EventBasedTaskCreationConfig.builder()
                        .taskCreationConfigMap(map)
                        .clientName(EventBasedTaskCreationClient.MERCHANT_ONBOARDING)
                        .build())
                .build();
        EventBasedTaskCreationMessage message = new EventBasedTaskCreationMessage(request);
        when(TaskActionMessagePublisher.createTaskFromEvent(message)).thenReturn(true);
        Response response = miscResource.createTaskFromEvent(Optional.empty(), request);
        Assert.assertEquals(202, response.getStatus());
    }

    @Test
    public void clientTaskCreateAndAssign() throws Exception {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                    .assigneeId("my_manager")
                    .campaignId("active_123")
                    .entityId("SARANSH_KIRANA")
                    .taskDefinitionId("T")
                    .build();
            ClientTaskCreateAndAssignMessage message = new ClientTaskCreateAndAssignMessage(request);
            when(TaskActionMessagePublisher.createAndAssignTaskForClient(message)).thenReturn(true);
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("USER");
            GenericResponse<Boolean> response = miscResource.createAndAssignClientTask(null, request);
            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void clientTaskCreateAndAssignWithException() throws Exception {
        ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                .assigneeId("my_manager")
                .campaignId("active_123")
                .entityId("SARANSH_KIRANA")
                .taskDefinitionId("T")
                .build();
        ClientTaskCreateAndAssignMessage message = new ClientTaskCreateAndAssignMessage(request);

        taskActionMessagePublisherMockedStatic.when(() -> TaskActionMessagePublisher.createAndAssignTaskForClient(any())).thenThrow(LegionException.class);
        Assertions.assertThrows(LegionException.class, () -> miscResource.createAndAssignClientTask( null, request));
    }


    @Test
    public void clientTaskCreateAndAssignWithoutException() throws Exception {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                    .assigneeId("my_manager")
                    .campaignId("active_123")
                    .entityId("SARANSH_KIRANA")
                    .taskDefinitionId("T")
                    .build();
            ClientTaskCreateAndAssignMessage message = new ClientTaskCreateAndAssignMessage(request);
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("USER");
            taskActionMessagePublisherMockedStatic.when(() -> TaskActionMessagePublisher.createAndAssignTaskForClient(any())).thenReturn(true);
            GenericResponse<Boolean> response = miscResource.createAndAssignClientTask(null, request);
//        GenericResponse<Boolean> response = miscResource.createAndAssignClientTask(null, null, request);
            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void clientTaskCreateAndAssignSync() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                    .assigneeId("my_manager")
                    .campaignId("active_123")
                    .entityId("SARANSH_KIRANA")
                    .taskDefinitionId("T")
                    .build();
            ClientTaskCreateAndAssignMessage message = new ClientTaskCreateAndAssignMessage(request);
            StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TI123").build();
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("USER");
            when(clientTaskService.createAndAssignClientTask(any())).thenReturn(storedTaskInstance);
            GenericResponse<TaskInstance> response = miscResource.createAndAssignClientTaskSync(null, request, null);
            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void testInValidEntityIdClientTaskCreateAndAssignRequest() {

        ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                .entityId("")
                .taskDefinitionId("task-def-456")
                .campaignId("campaign-789")
                .assigneeId("assignee-101")
                .createTaskForManager(true)
                .markAvailable(true)
                .createdBy("creator-111")
                .forceTaskCreationRequest(true)
                .reasonForDeletion("No longer needed")
                .build();

        Set<ConstraintViolation<ClientTaskCreateAndAssignRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
    }
    @Test
    public void testInValidDefinitionIdClientTaskCreateAndAssignRequest() {

        ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                .entityId("Id")
                .taskDefinitionId("")
                .campaignId("campaign-789")
                .assigneeId("assignee-101")
                .createTaskForManager(true)
                .markAvailable(true)
                .createdBy("creator-111")
                .forceTaskCreationRequest(true)
                .reasonForDeletion("No longer needed")
                .build();

        Set<ConstraintViolation<ClientTaskCreateAndAssignRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
    }

    @Test
    public void testInValidCampaignIdClientTaskCreateAndAssignRequest() {

        ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                .entityId("Id")
                .taskDefinitionId("id")
                .campaignId("")
                .assigneeId("assignee-101")
                .createTaskForManager(true)
                .markAvailable(true)
                .createdBy("creator-111")
                .forceTaskCreationRequest(true)
                .reasonForDeletion("No longer needed")
                .build();

        Set<ConstraintViolation<ClientTaskCreateAndAssignRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
    }


    @Test
    public void clientTaskDelete() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            ClientTaskDeleteRequest request = ClientTaskDeleteRequest.builder().taskInstanceId("TI123").deletedBy("prabh").reason("reason_abc").build();
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("User");
            ClientTaskDeleteMessage message = new ClientTaskDeleteMessage(request);
            when(TaskActionMessagePublisher.deleteTask(message)).thenReturn(true);
            GenericResponse<Boolean> response = miscResource.deleteClientTask(null, request);
            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void clientTaskDeleteSync() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("User");

            ClientTaskDeleteRequest request = ClientTaskDeleteRequest.builder().taskInstanceId("TI123").deletedBy("prabh").deletionReason("reason_abc").build();

            when(taskManagementService.deleteTask(request)).thenReturn(StoredTaskInstance.builder()
                    .taskInstanceId("TI123")
                    .build());
            GenericResponse<TaskInstance> response = miscResource.deleteTask(request, null);
            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void clientTaskverify() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("User");
            TaskManualVerificationRequest request = TaskManualVerificationRequest.builder().build();
            when(TaskActionMessagePublisher.verifyTask(any())).thenReturn(true);
            GenericResponse<Void> response = miscResource.verifyClientTask(null, request, null);
            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void getLeadConfigTest() {
        GenericResponse<LeadConfig> response = miscResource.getConfig(Optional.empty());
        Assert.assertNotNull(response);
    }


    @Test
    public void getTaskListing() {
        //arrange
        TaskListRequest taskListRequest = TaskListRequest.builder().build();
        TaskListResponse expectedResponse = TaskListResponse.builder().build();
        when(taskDiscoveryService.getTaskListing(taskListRequest)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskListResponse> actualResponse = miscResource.getTaskListing( taskListRequest, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse, actualResponse.getData());
    }

}
