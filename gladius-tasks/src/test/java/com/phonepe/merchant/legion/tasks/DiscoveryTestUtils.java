package com.phonepe.merchant.legion.tasks;

import com.phonepe.merchant.gladius.models.entitystore.AgentEntity;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity;
import com.phonepe.merchant.gladius.models.external.request.ExternalEntityAdditionalFields;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.models.merchants.BusinessUnit;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.models.merchants.tasks.TaskMerchantMeta;
import com.phonepe.models.merchants.tasks.TaskStoreMeta;
import com.phonepe.models.merchants.tasks.TaskVpaMeta;
import io.appform.ranger.discovery.bundle.id.IdGenerator;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_INSTANCE_ID_PREFIX;

public class DiscoveryTestUtils {

    public static com.phonepe.merchant.gladius.models.entitystore.StoreEntity getStoreEntity() {
        List<String> polygonIds = Collections.emptyList();
        return com.phonepe.merchant.gladius.models.entitystore.StoreEntity.builder()
                .storeId(IdGenerator.generate("MS").getId())
                .merchantId(IdGenerator.generate("M").getId())
                .phoneNumber(IdGenerator.generateWithConstraints("","1234567890").get().getId())
                .subCategory("SUBCATEGORY")
                .category("CATEGORY")
                .superCategory("SUPERCATEGORY")
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .location(EsLocationRequest.builder()
                        .lon(0.0)
                        .lat(0.0)
                        .build())
                .storeName("RANDOM STORE NAME")
                .polygonIds(polygonIds)
                .updatedAt(new Date(System.currentTimeMillis()))
                .createdAt(new Date(System.currentTimeMillis()))
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.MerchantEntity getMerchantEntity() {
        List<String> polygonIds = new ArrayList<>();
        String polygonId = IdGenerator.generate("SECTOR_").getId();
        polygonIds.add(polygonId);
        return com.phonepe.merchant.gladius.models.entitystore.MerchantEntity.builder()
                .merchantId(IdGenerator.generate("M").getId())
                .phoneNumber(IdGenerator.generateWithConstraints("","1234567899").get().getId())
                .subCategory("SUBCATEGORYY")
                .category("CATEGORYY")
                .superCategory("SUPERCATEGORYY")
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .location(EsLocationRequest.builder()
                        .lon(0.0)
                        .lat(0.0)
                        .build())
                .displayName("RANDOM MERCHANT")
                .fullName("RANDOM GUY STORE MERCHANT")
                .polygonIds(polygonIds)
                .updatedAt(new Date(System.currentTimeMillis()))
                .createdAt(new Date(System.currentTimeMillis()))
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.SectorEntity getSectorEntity() {
        return com.phonepe.merchant.gladius.models.entitystore.SectorEntity.builder()
                .sectorId(IdGenerator.generate("SECTOR_").getId())
                .location(EsLocationRequest.builder()
                        .lon(0.0)
                        .lat(0.0)
                        .build())
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.TaskEntity getTaskEntity() {
        return com.phonepe.merchant.gladius.models.entitystore.TaskEntity.builder()
                .taskInstanceId(IdGenerator.generate(TASK_INSTANCE_ID_PREFIX).getId())
                .completedBy("RANDOM GUY")
                .completedOn(new Date(System.currentTimeMillis()))
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.VpaEntity getVpaEntity() {
        List<String> polygonIds = new ArrayList<>();
        String polygonId = IdGenerator.generate("SECTOR_").getId();
        polygonIds.add(polygonId);
        return com.phonepe.merchant.gladius.models.entitystore.VpaEntity.builder()
                .vpaId(IdGenerator.generate("VPA_").getId())
                .providerName("PROVIDER NAME")
                .shopName("BUSINESS NAME")
                .location(EsLocationRequest.builder()
                        .lon(0.0)
                        .lat(0.0)
                        .build())
                .polygonIds(polygonIds)
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.ExternalEntity getExternalEntity() {
        List<String> polygonIds = new ArrayList<>();
        String polygonId = IdGenerator.generate("SECTOR_").getId();
        polygonIds.add(polygonId);
        return com.phonepe.merchant.gladius.models.entitystore.ExternalEntity.builder()
                .externalEntityId(IdGenerator.generate("EXT_").getId())
                .clientId("CLIENT_ID")
                .location(EsLocationRequest.builder()
                        .lon(0.0)
                        .lat(0.0)
                        .build())
                .sectorIds(polygonIds)
                .createdBy("Someone")
                .updatedBy("Someone")
                .clientProvidedId("My_id")
                .name("Name")
                .externalEntityMeta(new ExternalEntityAdditionalFields())
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.AgentEntity getAgentEntity() {
        List<String> polygonIds = new ArrayList<>();
        String polygonId = IdGenerator.generate("SECTOR_").getId();
        polygonIds.add(polygonId);
        return com.phonepe.merchant.gladius.models.entitystore.AgentEntity.builder()
                .active(true)
                .managerId("Manager ID")
                .agentName("NAME")
                .polygonIds(polygonIds)
                .emailId("a@bcd")
                .phoneNumber("9999999999")
                .build();
    }

    public static com.phonepe.merchant.gladius.models.entitystore.Entity getEntity(EntityType entityType) {
        return entityType.accept(new EntityType.EntityTypeVisitor<com.phonepe.merchant.gladius.models.entitystore.Entity,Void>() {
            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitUser(Void aVoid) {
                return null;
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitSector(Void aVoid) {
                return getSectorEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitMerchant(Void aVoid) {
                return getMerchantEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitNone(Void aVoid) {
                return null;
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitStore(Void aVoid) {
                return getStoreEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitTask(Void aVoid) {
                return getTaskEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitVpa(Void aVoid) {
                return getVpaEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitExternal(Void aVoid) {
                return getExternalEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitAgent(Void aVoid) {
                return getAgentEntity();
            }

            @Override
            public com.phonepe.merchant.gladius.models.entitystore.Entity visitPhoneNumber(Void aVoid) {
                return com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity.builder()
                        .phoneNumber("9999999999")
                        .build();
            }
        },null);
    }

    public static MerchantProfile toMerchantProfile(com.phonepe.merchant.gladius.models.entitystore.MerchantEntity entity) {
        return MerchantProfile.builder()
                .merchantId(entity.getMerchantId())
                .phoneNumber(entity.getPhoneNumber())
                .subCategory(entity.getSubCategory())
                .category(entity.getCategory())
                .superCategory(entity.getSuperCategory())
                .businessUnit(entity.getBusinessUnit())
                .displayName(entity.getDisplayName())
                .fullName(entity.getFullName())
                .updatedAt(entity.getUpdatedAt())
                .createdAt(entity.getCreatedAt())
                .build();
    }

    public static MerchantProfile toMerchantProfile(com.phonepe.merchant.gladius.models.entitystore.StoreEntity entity) {
        return MerchantProfile.builder()
                .merchantId(entity.getMerchantId())
                .phoneNumber(entity.getPhoneNumber())
                .subCategory(entity.getSubCategory())
                .category(entity.getCategory())
                .superCategory(entity.getSuperCategory())
                .businessUnit(entity.getBusinessUnit())
                .updatedAt(entity.getUpdatedAt())
                .createdAt(entity.getCreatedAt())
                .build();
    }

    public static PhysicalStore toPhysicalStore(com.phonepe.merchant.gladius.models.entitystore.StoreEntity storeEntity) {
        return PhysicalStore.builder()
                .merchantId(storeEntity.getMerchantId())
                .storeId(storeEntity.getStoreId())
                .phoneNumber(storeEntity.getPhoneNumber())
                .updatedAt(storeEntity.getUpdatedAt())
                .createdAt(storeEntity.getCreatedAt())
                .latitude(storeEntity.getLocation().getLat())
                .longitude(storeEntity.getLocation().getLon())
                .sectors(new HashSet<>(storeEntity.getPolygonIds()))
                .displayName(storeEntity.getDisplayName())
                .name(storeEntity.getStoreName())
                .build();
    }

    public static PhysicalStore toPhysicalStore(com.phonepe.merchant.gladius.models.entitystore.MerchantEntity merchantEntity) {
        return PhysicalStore.builder()
                .merchantId(merchantEntity.getMerchantId())
                .storeId(IdGenerator.generate("MS").getId())
                .phoneNumber(merchantEntity.getPhoneNumber())
                .updatedAt(merchantEntity.getUpdatedAt())
                .createdAt(merchantEntity.getCreatedAt())
                .latitude(merchantEntity.getLocation().getLat())
                .longitude(merchantEntity.getLocation().getLon())
                .sectors(new HashSet<>(merchantEntity.getPolygonIds()))
                .displayName(merchantEntity.getDisplayName())
                .name("STORE NAME")
                .build();
    }

    public static CompetitionQrResponse toCompetitionQrResponse(com.phonepe.merchant.gladius.models.entitystore.VpaEntity vpaEntity) {
        return CompetitionQrResponse.builder()
                .businessName(vpaEntity.getName())
                .vpa(vpaEntity.getEntityId())
                .latitude(Float.valueOf(String.valueOf(vpaEntity.getLocation().getLat())))
                .longitude(Float.valueOf(String.valueOf(vpaEntity.getLocation().getLon())))
                .providerName(vpaEntity.getProviderName())
                .sectorIds(vpaEntity.getPolygonIds())
                .build();
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.MerchantEntity> getMerchantEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.MerchantEntity> merchantEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            merchantEntities.add(getMerchantEntity());
        }
        return merchantEntities;
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.StoreEntity> getStoreEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.StoreEntity> storeEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            storeEntities.add(getStoreEntity());
        }
        return storeEntities;
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.SectorEntity> getSectorEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.SectorEntity> sectorEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            sectorEntities.add(getSectorEntity());
        }
        return sectorEntities;
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.TaskEntity> getTaskEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.TaskEntity> taskEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            taskEntities.add(getTaskEntity());
        }
        return taskEntities;
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.VpaEntity> getVpaEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.VpaEntity> vpaEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            vpaEntities.add(getVpaEntity());
        }
        return vpaEntities;
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.AgentEntity> getAgentEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.AgentEntity> agentEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            agentEntities.add(getAgentEntity());
        }
        return agentEntities;
    }

    public static List<com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity> getPhoneNumberEntityList(int size) {
        List<com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity> phoneNumberEntities = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            phoneNumberEntities.add(PhoneNumberEntity.builder().phoneNumber("99999999").build());
        }
        return phoneNumberEntities;
    }

    public static List<TaskMerchantMeta> getTaskMerchantMetaList(List<com.phonepe.merchant.gladius.models.entitystore.MerchantEntity> merchantEntities) {
        return merchantEntities.stream()
                .map(merchantEntity -> TaskMerchantMeta.builder()
                        .phoneNumber(merchantEntity.getPhoneNumber())
                        .merchantId(merchantEntity.getMerchantId())
                        .displayName(merchantEntity.getDisplayName())
                        .displayCategory(merchantEntity.getCategory())
                        .build())
                .toList();
    }

   public static List<TaskStoreMeta> getTaskStoreMetaList(List<com.phonepe.merchant.gladius.models.entitystore.StoreEntity> storeEntities) {
       return storeEntities.stream()
               .map(storeEntity -> TaskStoreMeta.builder()
                       .phoneNumber(storeEntity.getPhoneNumber())
                       .merchantId(storeEntity.getMerchantId())
                       .storeId(storeEntity.getStoreId())
                       .displayName(storeEntity.getDisplayName())
                       .displayCategory(storeEntity.getCategory())
                       .build())
               .toList();
   }

   public static List<DiscoveryTaskInstance> getDiscoveryTaskInstanceList(List<com.phonepe.merchant.gladius.models.entitystore.TaskEntity> taskEntities) {
        return taskEntities.stream()
                .map(taskEntity -> DiscoveryTaskInstance.builder()
                        .taskInstanceId(taskEntity.getEntityId())
                        .completedOn(taskEntity.getCompletedOn().getTime())
                        .completedBy(taskEntity.getCompletedBy())
                        .taskState(LegionTaskStateMachineState.COMPLETED)
                        .taskInstanceMeta(TaskInstanceMeta.builder().taskMetaAsMap(new HashMap<>()).build())
                        .taskDefinitionId("RANDOM TASK DEFNITION")
                        .actionId("RANDOM ACTION ID")
                        .dueDate(0l)
                        .startDate(0l)
                        .updatedAt(0l)
                        .createdAt(0l)
                        .build())
                .toList();
   }

    public static Set<TaskVpaMeta> getTaskVpaMetaList(List<com.phonepe.merchant.gladius.models.entitystore.VpaEntity> vpaEntities) {
        return vpaEntities.stream()
                .map(vpaEntity -> TaskVpaMeta.builder()
                        .shopName(vpaEntity.getName())
                        .providerName(vpaEntity.getProviderName())
                        .vpaId(vpaEntity.getEntityId())
                        .build())
                .collect(Collectors.toSet());
    }

    public static List<AgentProfile> getAgentProfilesList(List<AgentEntity> agentEntities) {
        return agentEntities.stream()
                .map(agentEntity -> AgentProfile.builder()
                        .active(agentEntity.isActive())
                        .name(agentEntity.getName())
                        .emailId(agentEntity.getEmailId())
                        .phoneNumber(agentEntity.getPhoneNumber())
                        .agentId(agentEntity.getEntityId())
                        .addresses(agentEntity.getAddresses())
                        .sectors(agentEntity.getPolygonIds())
                        .lastActiveDate(agentEntity.getLastActiveDate())
                        .managerId(agentEntity.getManagerId())
                        .build())
                .toList();
    }

}
