package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.response.SectorStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskStats;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.SectorStatsService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SectorStatsResourceTest {

    private static SectorStatsService sectorStatsService;
    private static SectorStatsResource sectorStatsResource;

    @BeforeClass
    public static void init() {
        sectorStatsService = mock(SectorStatsService.class);
        sectorStatsResource = new SectorStatsResource(sectorStatsService);
    }

    @Test
    public void testGetSectorStats() {
        UserDetails userDetails = getUserDetails();
        ServiceUserPrincipal userPrincipal = mock(ServiceUserPrincipal.class);
        AgentProfile agentProfile = mock(AgentProfile.class);

        SectorStatsResource resource = new SectorStatsResource(sectorStatsService);

        try (MockedStatic<AuthUserDetails> staticMock = mockStatic(AuthUserDetails.class)) {
            staticMock.when(() -> AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal))
                    .thenReturn("user-123");

            // Prepare expected response
            SectorStatsResponse expectedStatsResponse = new SectorStatsResponse();
            expectedStatsResponse.setTotalCounts(Map.of("Created", 10L, "Completed", 5L));
            expectedStatsResponse.setSectorWiseStats(List.of(
                    SectorTaskStats.builder().sectorId("sector-1").taskStats(Map.of("Created", 6L, "Completed", 2L)).build(),
                    SectorTaskStats.builder().sectorId("sector-2").taskStats(Map.of("Created", 4L, "Completed", 3L)).build()
            ));

            when(sectorStatsService.getSectorTaskStats("user-123")).thenReturn(expectedStatsResponse);

            // Act
            GenericResponse<SectorStatsResponse> response = resource.getSectorStats(
                    userDetails, userPrincipal, agentProfile
            );

            // Assert
            assertTrue(response.isSuccess());
            assertNotNull(response.getData());
            assertEquals(expectedStatsResponse, response.getData());
            assertEquals("sector-1", response.getData().getSectorWiseStats().get(0).getSectorId());
            assertEquals("sector-2", response.getData().getSectorWiseStats().get(1).getSectorId());
        }
    }
}