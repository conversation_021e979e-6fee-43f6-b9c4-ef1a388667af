package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CampaignFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskGeoJsonSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSectorIdSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.geopolygon.GeoFenceRemappingSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.legion.core.entitystore.ESConnection;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.utils.EsUtil;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.TaskTestUtils;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.search.query.QueryEnricher;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.geopolygon.RemappingGeoPolygonQueryBuilder;
import com.phonepe.merchant.legion.tasks.services.impl.TaskHousekeepingServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.BulkSectorUpdater;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Date;
import java.util.EnumSet;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POLYGON_IDS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_STATE;
import static com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod.RELATIVE_DAYS;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskInstance;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_DEFINITION_ID_PREFIX;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getDueDateFilter;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getEsSearchResponse;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getPolygonCoordinates;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getShapeForSectorId;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskHousekeepingServiceTest extends LegionTaskBaseTest {

    private static TaskHousekeepingService taskHousekeepingService;
    private static ESRepository esRepository = mock(ESRepository.class);
    private static ESConnection esConnection = mock(ESConnection.class);
    private static RestHighLevelClient client ;
    private static AtlasService atlasService;
    private static TaskDefinitionService taskDefinitionService;
    private static final TaskInstanceRepository taskInstanceRepository = mock(TaskInstanceRepository.class);
    private static final CampaignService campaignService = mock(CampaignService.class);
    private static RemappingGeoPolygonQueryBuilder geoPolygonQueryBuilder;
    private static TaskDiscoveryService taskDiscoveryService;
    private static final QueryEnricher enricher  = mock(QueryEnricher.class);
    private  static final EntityStore entityStore = mock(EntityStore.class);
    private static final String TASK_INSTANCE_ID = "task-id";
    private static final String CAMPAIGN_ID = "campaign-id";
    private static TaskESRepository taskESRepository;
    private final EsLocationRequest location = EsLocationRequest.builder().lat(37.7749).lon(-122.4194).build();
    private final DiscoveryTaskInstance taskInstance = DiscoveryTaskInstance.builder().taskInstanceId("taskId").location(location).polygonIds(List.of("sector1", "sector2")).build();


    public TaskHousekeepingServiceTest() {
        EsUtil.init("agent_tasks_v4");
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        geoPolygonQueryBuilder = new RemappingGeoPolygonQueryBuilder();
        client = mock(RestHighLevelClient.class);
        atlasService = mock(AtlasService.class);
        taskESRepository = mock(TaskESRepository.class);
        taskDefinitionService = mock(TaskDefinitionService.class);
        when(esConnection.client()).thenReturn(client);
        taskHousekeepingService = new TaskHousekeepingServiceImpl(esRepository,esConnection,atlasService,eventExecutor, taskESRepository, taskInstanceRepository, campaignService, taskDefinitionService, taskDiscoveryService, geoPolygonQueryBuilder);
        when(sectorGeofenceQueryBuilder.visit(any(EqualsFilter.class))).thenReturn(QueryBuilders.boolQuery());
        when(sectorGeofenceQueryBuilder.visit(any(InFilter.class))).thenReturn(QueryBuilders.boolQuery());
    }



    @Test(expected = Test.None.class)
    //client.bulk() is a final method
    public void bulkSectorUpdaterTestSuccess() throws Exception {

        List<DiscoveryTaskInstance> tasks = new ArrayList<>();
        tasks.add(getDiscoveryTaskInstance(LegionTaskStateMachineState.COMPLETED));
        BulkResponse bulkResponse = mock(BulkResponse.class);
        when(bulkResponse.status()).thenReturn(RestStatus.OK);
        //when(client.bulk(any(BulkRequest.class), eq(RequestOptions.DEFAULT))).thenReturn(bulkResponse);
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.submit(
                new BulkSectorUpdater(tasks,atlasService,client,eventExecutor)
        );
        executorService.shutdown();
    }

    @Test(expected = Test.None.class)
    //client.bulk() is a final method
    public void bulkSectorUpdaterTestSuccess2() throws Exception {
        BulkRequest bulkRequest = new BulkRequest(TASK_INDEX);
        List<String> sectors = Arrays.asList(new String[]{"A","B","C"});
        UpdateRequest updateRequest = new UpdateRequest(TASK_INDEX, "taskInstanceId").doc(POLYGON_IDS, sectors);
        bulkRequest.add(updateRequest);
        List<DiscoveryTaskInstance> tasks = new ArrayList<>();
        tasks.add(getDiscoveryTaskInstance(LegionTaskStateMachineState.COMPLETED));

        when(atlasService.getSectorIdByLatLong(any(Double.class), any(Double.class))).thenReturn(sectors);
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.submit(
                new BulkSectorUpdater(tasks,atlasService,client,eventExecutor)
        );
        executorService.shutdown();
    }


    @Test(expected = Test.None.class)
    //client.bulk() is a final method
    public void bulkSectorUpdaterTestFailure() throws IOException {
        List<DiscoveryTaskInstance> tasks = new ArrayList<>();
        tasks.add(getDiscoveryTaskInstance(LegionTaskStateMachineState.COMPLETED));
        BulkResponse bulkResponse = mock(BulkResponse.class);
        when(bulkResponse.status()).thenReturn(RestStatus.BAD_REQUEST);
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.submit(
                new BulkSectorUpdater(tasks,atlasService,client,eventExecutor)
        );
        executorService.shutdown();
    }

    @Test()
    public void getTaskInGeoFenceOfSectorId() {

        when(esRepository.search(eq(TASK_INDEX),any(),anyInt(),anyInt()))
                .thenReturn(getEsSearchResponse(List.of(TaskTestUtils.getSectorDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE)),false));
        when(atlasService.getSectorCoordinates(any()))
                .thenReturn(getShapeForSectorId());
        TaskListResponse response = taskHousekeepingService.getTaskInGeoFenceOfSector(new TaskSectorIdSearchRequest(1, 15, "Sector1"));
        Assert.assertEquals(1, response.getCount());
    }

    @Test()
    public void getTaskInGeoFence() {

        when(esRepository.search(eq(TASK_INDEX),any(),anyInt(),anyInt()))
                .thenReturn(getEsSearchResponse(List.of(TaskTestUtils.getSectorDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE)),false));
        TaskListResponse response = taskHousekeepingService.getTaskInGeoFenceOfSector(new TaskGeoJsonSearchRequest(1, 15, getPolygonCoordinates()));
        Assert.assertEquals(1, response.getCount());
    }

    public static StoredTaskInstance getStoredTaskInstance() {
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId(IdGenerator.generate("T").getId())
                .taskDefinitionId(IdGenerator.generate(TASK_DEFINITION_ID_PREFIX).getId())
                .actionId("DUMMY_ACTION")
                .curState(LegionTaskStateMachineState.VERIFICATION_SUCCESS)
                .entityId(IdGenerator.generate("M").getId())
                .entityType(EntityType.MERCHANT)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .dueDate(new Date())
                .build();
        List<StoredTaskTransition> storedTaskTransitions = new ArrayList<>();
        Date date = new Date();
        for (LegionTaskStateMachineState state: EnumSet.allOf(LegionTaskStateMachineState.class)) {
            StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
                    .actor("ACTOR")
                    .toState(state)
                    .createdAt(date)
                    .build();
            storedTaskTransitions.add(storedTaskTransition);
            if (state == LegionTaskStateMachineState.CREATED) {
                Date date1 = new Date();
                date1.setTime(0);
                storedTaskTransition.setCreatedAt(date1);
            }
        }
        storedTaskInstance.setTaskTransitions(storedTaskTransitions);
        return storedTaskInstance;
    }
    @Test(expected = Test.None.class)
    public void refreshTask() {
        StoredTaskInstance storedTaskInstance = getStoredTaskInstance();
        when(taskInstanceRepository.get(anyString())).thenReturn(Optional.of(storedTaskInstance));
        Map<String, Set<String>> attributeMap = new HashMap<>();
        HashSet<String> set = new HashSet<>();
        set.add("abc");
        attributeMap.put(AttributeType.OBJECTIVE.name(),set);

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder().actionId("action").taskDefinitionId("definition")
                .createdBy("shasha").name("shasha").attributes(attributeMap).build();

        when(taskDefinitionService.getFromDb(any())).thenReturn(taskDefinitionInstance);

        doNothing().when(esRepository).update(eq(TASK_INDEX),eq(storedTaskInstance.getTaskInstanceId()),eq(""),anyBoolean());
        taskHousekeepingService.refreshTaskInstance(storedTaskInstance.getTaskInstanceId());
    }

    @Test
    public void testSyncDueDateInDb_TaskExists() {
        Campaign campaign = Campaign.builder().expiryPeriod(RELATIVE_DAYS).expiryValue(1L).campaignId(CAMPAIGN_ID).build();
        when(campaignService.get(CampaignFetchByIdRequest.builder().campaignId(CAMPAIGN_ID).build()))
                .thenReturn(campaign);

        StoredTaskInstance taskInstance = StoredTaskInstance.builder().taskInstanceId(TASK_INSTANCE_ID).campaignId(CAMPAIGN_ID).build();
        when(taskInstanceRepository.get(TASK_INSTANCE_ID))
                .thenReturn(Optional.of(taskInstance));
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().build();
        when(taskInstanceRepository.save(taskInstance)).thenReturn(storedTaskInstance);
        taskHousekeepingService.syncDueDateInDb(TASK_INSTANCE_ID);

        verify(taskInstanceRepository).save(taskInstance);
        verify(taskInstanceRepository).get(TASK_INSTANCE_ID);
    }

    @Test
    public void testSyncDueDateInDb_TaskDoesNotExist() {
        when(taskInstanceRepository.get(TASK_INSTANCE_ID)).thenReturn(Optional.empty());
        assertThrows(RuntimeException.class, () -> taskHousekeepingService.syncDueDateInDb(TASK_INSTANCE_ID));
    }

    @Test
    public void testSuccessfulUpdate() {
        EsLocationRequest location = EsLocationRequest.builder().lat(37.7749).lon(-122.4194).build();
        DiscoveryTaskInstance taskInstance = DiscoveryTaskInstance.builder().taskInstanceId("taskId1").location(location).polygonIds(List.of("sector1", "sector2")).build();
        List<String> sectorIds = List.of("newSectorId1", "newSectorId2");

        when(taskESRepository.get("taskId1")).thenReturn(taskInstance);
        when(atlasService.getSectorIdByLatLong(location.getLat(), location.getLon())).thenReturn(sectorIds);

        boolean result = taskHousekeepingService.updateSectorForTask("taskId1");

        verify(esRepository).updateField(TASK_INDEX, "taskId1", POLYGON_IDS, sectorIds);
        assertTrue(result);
    }

    @Test
    public void testEmptySectorIds() {
        EsLocationRequest location = EsLocationRequest.builder().lat(37.7749).lon(-122.4194).build();
        DiscoveryTaskInstance taskInstance = DiscoveryTaskInstance.builder().taskInstanceId("taskId").location(location).polygonIds(List.of("sector1", "sector2")).build();
        when(taskESRepository.get("taskId")).thenReturn(taskInstance);
        when(atlasService.getSectorIdByLatLong(location.getLat(), location.getLon())).thenReturn(List.of());

        LegionException exception = assertThrows(LegionException.class, () -> {
            taskHousekeepingService.updateSectorForTask("taskId");
        });

        assertEquals(LegionTaskErrorCode.SECTOR_UPDATE_FAILURE, exception.getErrorCode());
    }

    @Test
    public void testSectorsAlreadyUpdated() {
        EsLocationRequest location = EsLocationRequest.builder().lat(37.7749).lon(-122.4194).build();
        DiscoveryTaskInstance taskInstance = DiscoveryTaskInstance.builder().taskInstanceId("taskId").location(location).polygonIds(List.of("sector1", "sector2")).build();
        List<String> sectorIds = List.of("sector1", "sector2");

        when(taskESRepository.get("taskId")).thenReturn(taskInstance);
        when(atlasService.getSectorIdByLatLong(location.getLat(), location.getLon())).thenReturn(sectorIds);

        LegionException exception = assertThrows(LegionException.class, () -> {
            taskHousekeepingService.updateSectorForTask("taskId");
        });

        assertEquals(LegionTaskErrorCode.SECTOR_UPDATE_FAILURE, exception.getErrorCode());
    }

    @Test
    public void testSectorsAlreadyException() {
        EsLocationRequest location = EsLocationRequest.builder().lat(37.7749).lon(-122.4194).build();
        DiscoveryTaskInstance taskInstance = DiscoveryTaskInstance.builder().taskInstanceId("taskId").location(location).polygonIds(List.of("sector1", "sector2")).build();
        when(taskESRepository.get("taskId")).thenReturn(taskInstance);
        when(atlasService.getSectorIdByLatLong(location.getLat(), location.getLon())).thenThrow(new RuntimeException("Atlas Service Error"));

        LegionException exception = assertThrows(LegionException.class, () -> {
            taskHousekeepingService.updateSectorForTask("taskId");
        });

        assertEquals(LegionTaskErrorCode.SECTOR_UPDATE_FAILURE, exception.getErrorCode());
        verify(eventExecutor,times(3)).ingest(any());
        verify(esRepository, times(1)).updateField(anyString(), anyString(), anyString(), any());
    }

    @Test

    public void testUpdateSectorAgainstSectorId() {
        List<DiscoveryTaskInstance> tasks = List.of(
                taskInstance
        );
        when(esRepository.search(any(), any())).thenReturn(getEsSearchResponse(List.of(TaskTestUtils.getSectorDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE)),false));
        when(esRepository.scroll(any(), any())).thenReturn(getEsSearchResponse(Collections.EMPTY_LIST,false));

        when(esRepository.search(anyString(), any(), anyInt(), anyInt(), eq(DiscoveryTaskInstance.class))).thenReturn(tasks);
        boolean result = taskHousekeepingService.updateSectorAgainstSectorId("sector1", 1622487600L);
        assertTrue(result);
    }

    @Test
    public void testUpdateSectorAgainstSectorIdException() {
        List<DiscoveryTaskInstance> tasks = List.of(
                taskInstance
        );
        when(esRepository.search(anyString(), any(), anyInt(), anyInt(), eq(DiscoveryTaskInstance.class))).thenReturn(tasks);
        doThrow(new RuntimeException("Update error")).when(esRepository).search(anyString(), any(), anyInt(), anyInt(), eq(DiscoveryTaskInstance.class));
        Assertions.assertThrows(RuntimeException.class, () -> taskHousekeepingService.updateSectorAgainstSectorId("sector1", 1622487600L));
    }




//    @Test
//    public void testBulkSectorUpdateExecution() {
//        List<String> invalidSectorIds = List.of();
//        List<String> taskStates = new ArrayList<>();
//        List<Filter> filters = new ArrayList<>();
//        taskStates.add(LegionTaskStateMachineState.AVAILABLE.getText());
//        taskStates.add(LegionTaskStateMachineState.BOUNDED_ASSIGNED.getText());
//        taskStates.add(LegionTaskStateMachineState.SELF_ASSIGNED.getText());
//        taskStates.add(LegionTaskStateMachineState.IN_PROGRESS.getText());
//        InFilter validTaskStates = new InFilter(TASK_STATE, taskStates);
//        filters.add(validTaskStates);
//        filters.add(getDueDateFilter());
//        filters.add(new EqualsFilter(ACTIVE, true));
//        doNothing().when(esRepository).performBulkOperation(anyString(), any(BoolQueryBuilder.class), anyInt(), anyInt(), any(), any(), eq(DiscoveryTaskInstance.class));
//
//        taskHousekeepingService.bulkSectorUpdate(invalidSectorIds, 0l);
//
//        verify(esRepository, times(4)).performBulkOperation(anyString(), any(BoolQueryBuilder.class), anyInt(), anyInt(), any(Function.class), any(), eq(DiscoveryTaskInstance.class));
//    }





    @Test
    public void testGetTasksInGeoFence() {
        when(esRepository.search(any(), any())).thenReturn(getEsSearchResponse(List.of(TaskTestUtils.getSectorDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE)),false));

        GeoFenceRemappingSearchRequest request = GeoFenceRemappingSearchRequest.builder().geoFenceCoordinates(List.of(List.of(0.0,0.0), List.of(1.0,1.0), List.of(2.0,2.0))).build();
        Assertions.assertNotNull(taskHousekeepingService.getEsDocsWithinGeoFence(request));
    }
}
