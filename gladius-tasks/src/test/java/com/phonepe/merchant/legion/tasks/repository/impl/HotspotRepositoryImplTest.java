package com.phonepe.merchant.legion.tasks.repository.impl;

import com.phonepe.merchant.gladius.models.hotspots.enums.HotspotStatus;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import io.appform.dropwizard.sharding.dao.LookupDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HotspotRepositoryImplTest {

    @Mock
    private LookupDao<StoredHotspot> lookupDao;

    @InjectMocks
    private HotspotRepositoryImpl hotspotRepository;

    private StoredHotspot hotspot;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        hotspot = new StoredHotspot();
        hotspot.setId("hotspot1");
        hotspot.setSectorId("sector1");
        hotspot.setStatus(HotspotStatus.ACTIVE);
        hotspot.setHotspotType("SS");
    }

    @Test
    public void testSaveHotspotSuccess() throws Exception {
        when(lookupDao.save(any())).thenReturn(Optional.of(hotspot));
        StoredHotspot savedHotspot = hotspotRepository.save(hotspot);
        assertNotNull(savedHotspot);
        assertEquals("hotspot1", savedHotspot.getId());
        verify(lookupDao, times(1)).save(any(StoredHotspot.class));
    }

    @Test
    public void testSaveHotspotFailure() throws Exception {
        when(lookupDao.save(any(StoredHotspot.class))).thenReturn(Optional.empty());
        LegionException exception = assertThrows(LegionException.class, () -> {
            hotspotRepository.save(hotspot);
        });
        assertEquals(CoreErrorCode.DAO_ERROR, exception.getErrorCode());
        verify(lookupDao, times(1)).save(any(StoredHotspot.class));
    }

    @Test
    public void testGetHotspotSuccess() throws Exception {
        when(lookupDao.get(anyString())).thenReturn(Optional.of(hotspot));
        Optional<StoredHotspot> retrievedHotspot = hotspotRepository.get("hotspot1");
        assertTrue(retrievedHotspot.isPresent());
        assertEquals("hotspot1", retrievedHotspot.get().getId());
        verify(lookupDao, times(1)).get("hotspot1");
    }

    @Test
    public void testGetHotspotNotFound() throws Exception {
        when(lookupDao.get(anyString())).thenReturn(Optional.empty());
        Optional<StoredHotspot> retrievedHotspot = hotspotRepository.get("hotspot1");
        assertFalse(retrievedHotspot.isPresent());
        verify(lookupDao, times(1)).get("hotspot1");
    }

    @Test
    public void testGetSectorHotspotSuccess() {
        when(lookupDao.scatterGather(any())).thenReturn(List.of(hotspot));
        List<StoredHotspot> retrievedHotspot = hotspotRepository.getSectorHotspots("sector1", "type1");
        assertEquals(1, retrievedHotspot.size());
    }

    @Test
    public void testGetHotspotTypeSuccess() throws Exception {
        when(lookupDao.scatterGather(any())).thenReturn(List.of(hotspot));
        Set<String> hotspotType = hotspotRepository.getHotspotType();
        assertEquals(1, hotspotType.size());
    }

    @Test
    public void testGetSectorListHotspotSuccess() throws Exception {
        when(lookupDao.scatterGather(any())).thenReturn(List.of(hotspot));
        List<StoredHotspot> retrievedHotspot = hotspotRepository.fetchSectorHotspots(List.of("sector1"));
        assertEquals(1, retrievedHotspot.size());
    }

}
