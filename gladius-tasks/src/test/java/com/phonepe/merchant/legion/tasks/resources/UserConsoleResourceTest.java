package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.merchant.gladius.models.tasks.request.ActionDetails;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.CategoryDetails;
import com.phonepe.merchant.legion.tasks.services.CategoryInsightService;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.models.response.GenericResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class UserConsoleResourceTest {

    private static TaskActionService taskActionService;
    private static TaskDiscoveryService taskDiscoveryService;
    private static TaskDefinitionService taskDefinitionService;
    private static TaskAttributeService taskAttributeService;
    private static CategoryInsightService categoryInsightService;
    private static UserConsoleResource userConsoleResource;


    @BeforeAll
    public static void init() {
        taskActionService = mock(TaskActionService.class);
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        taskDefinitionService = mock(TaskDefinitionService.class);
        taskAttributeService = mock(TaskAttributeService.class);
        categoryInsightService = mock(CategoryInsightService.class);
        userConsoleResource = new UserConsoleResource(taskActionService, taskDiscoveryService, taskDefinitionService, taskAttributeService, categoryInsightService);
    }

    @Test
    public void testFetchCategoryDetails() {
        CategoryDetails categoryDetails = new CategoryDetails();
        when(categoryInsightService.fetchCategoryDetails()).thenReturn(categoryDetails);
        GenericResponse<CategoryDetails> actual = userConsoleResource.fetchCategoryDetails(null, null);
        Assertions.assertEquals(categoryDetails, actual.getData());
        Assertions.assertTrue(actual.isSuccess());
        Assertions.assertNotNull(actual.getData());
        verify(categoryInsightService).fetchCategoryDetails();
    }

    @Test
    public void testFetchActionDetails() {
        Category category = Category.SMARTSPEAKER;
        List<ActionDetails> actionDetails = Collections.singletonList(new ActionDetails());
        when(categoryInsightService.fetchAllActionsOfCategory(category)).thenReturn(actionDetails);
        GenericResponse<List<ActionDetails>> actual = userConsoleResource.fetchActionDetails(null,
                null, Category.SMARTSPEAKER);
        Assertions.assertTrue(actual.isSuccess());
        Assertions.assertNotNull(actual.getData());
        Assertions.assertFalse(actual.getData().isEmpty());
        Assertions.assertEquals(actionDetails, actual.getData());

        verify(categoryInsightService).fetchAllActionsOfCategory(category);
    }


}
