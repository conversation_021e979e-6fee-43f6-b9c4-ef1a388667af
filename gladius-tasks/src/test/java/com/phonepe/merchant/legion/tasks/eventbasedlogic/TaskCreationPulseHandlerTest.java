package com.phonepe.merchant.legion.tasks.eventbasedlogic;

import com.phonepe.growth.neuron.pulse.Pulse;
import com.phonepe.growth.neuron.pulse.Signal;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.junit.BeforeClass;
import org.junit.Test;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.AGENT_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.CAMPAIGN_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.CLIENT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.ENTITY_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MARK_AVAILABLE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.ROLE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_DEFINITION_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_TYPE;
import static org.mockito.Mockito.mock;

public class TaskCreationPulseHandlerTest extends LegionTaskBaseTest {

    private static TaskCreationPulseHandler pulseHandler;
    private static ClientTaskService clientTaskService = mock(ClientTaskService.class);

    @BeforeClass
    public static void init() {
        pulseHandler = new TaskCreationPulseHandler(clientTaskService);
    }

    @Test(expected = Test.None.class)
    public void testPulseHandler() {


        Pulse callback = Pulse.builder()
                .signals(Arrays.asList(new Signal[]{
                        Signal.builder()
                                .name(ENTITY_ID)
                                .value("MID_sid")
                                .build(),
                        Signal.builder()
                                .name(ROLE)
                                .value("AGENT")
                                .build(),
                        Signal.builder()
                                .name(CLIENT)
                                .value("MERCHANT_ONBOARDING")
                                .build(),
                        Signal.builder()
                                .name(TASK_DEFINITION_ID)
                                .value("TID")
                                .build(),
                        Signal.builder()
                                .name(AGENT_ID)
                                .value("AID")
                                .build(),
                        Signal.builder()
                                .name(CAMPAIGN_ID)
                                .value("CID")
                                .build(),
                        Signal.builder()
                                .name(TASK_TYPE)
                                .value("COMP_VPA_MAPPING")
                                .build(),
                        Signal.builder()
                                .name(MARK_AVAILABLE)
                                .value("true")
                                .build()
                }))
                .build();

        pulseHandler.preHandle(callback);
        pulseHandler.handle(callback);
        pulseHandler.handle(callback);
    }

}