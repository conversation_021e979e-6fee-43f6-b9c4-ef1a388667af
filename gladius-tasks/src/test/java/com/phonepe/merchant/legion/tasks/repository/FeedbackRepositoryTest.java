package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.survey.storage.StoredFeedback;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class FeedbackRepositoryTest extends LegionTaskBaseTest {

    private static StoredFeedback buildStoredFeedback(String formType, String feedbackAssetId) {
        return StoredFeedback.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId("feedbackId")
                .campaignId("campaignId")
                .createdAt(new Date())
                .updatedAt(new Date())
                .createdBy("createdBy")
                .updatedBy("updatedBy").build();
    }

    @Test
    public void testSaveAndGet() {
        String formType = "formType";
        String feedbackAssetId = "feedbackAssetId";
        StoredFeedback storedFeedback = buildStoredFeedback(formType, feedbackAssetId);
        assertDoesNotThrow(() -> {
            feedbackRepository.save(storedFeedback);
        });
        Optional<StoredFeedback> result = feedbackRepository.get(formType, feedbackAssetId);
        assertNotNull(result.get());
    }

    @Test
    public void testUpdate() {
        String formType = "formType2";
        String feedbackAssetId = "feedbackAssetId2";
        StoredFeedback storedFeedback = buildStoredFeedback(formType, feedbackAssetId);
        StoredFeedback savedStoredFeedback = feedbackRepository.save(storedFeedback);
        assertEquals("campaignId", savedStoredFeedback.getCampaignId());
        assertDoesNotThrow(() -> {
            feedbackRepository.update(formType, feedbackAssetId, storedFeedback1 -> {
                storedFeedback1.setCampaignId("campaignId2");
                return storedFeedback1;
            });
        });
        Optional<StoredFeedback> result = feedbackRepository.get(formType, feedbackAssetId);
        assertNotNull(result.get());
        assertEquals("campaignId2", result.get().getCampaignId());
    }

}
