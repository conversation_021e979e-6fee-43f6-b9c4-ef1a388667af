package com.phonepe.merchant.legion.tasks.search.postfiltersorting;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.TaskTestUtils;
import com.phonepe.merchant.legion.tasks.search.postfiltersorting.models.PrioritiseStrategyConfig;
import com.phonepe.merchant.legion.tasks.search.postfiltersorting.models.PrioritiseStrategyPriority;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PostFilterSortingStrategyExecutorTest extends LegionTaskBaseTest {
    public static final String AGENT_ID = "agent-id";
    public static final String OBJECTIVE_1 = "objective1";
    public static final String OBJECTIVE_2 = "objective2";
    public static final String TASK_DEFINITION_ID = "task-definition-id";
    private static final String CAMPAIGN_ID = "campaign-id";
    public static final Set<String> OBJECTIVES = Set.of(OBJECTIVE_1, OBJECTIVE_2);
    public static final Map<String, Set<String>> ATTRIBUTES = Map.of(AttributeType.OBJECTIVE.getName(), OBJECTIVES);
    public static final DiscoveryTaskInstance DISCOVERY_TASK_INSTANCE_1 =
            TaskTestUtils.getDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE);
    public static final DiscoveryTaskInstance DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE =
            TaskTestUtils.getDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE);
    public static final DiscoveryTaskInstance DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION =
            TaskTestUtils.getDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE);
    public static final DiscoveryTaskInstance DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE =
            TaskTestUtils.getDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE);
    public static final DiscoveryTaskInstance DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID =
            TaskTestUtils.getDiscoveryTaskInstance(LegionTaskStateMachineState.AVAILABLE);
    public static final List<DiscoveryTaskInstance> DISCOVERY_TASK_INSTANCES = List.of(
            DISCOVERY_TASK_INSTANCE_1,
            DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
            DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
            DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE,
            DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID);
    private static PostFilterSortingStrategyExecutor postFilterSortingStrategyExecutor;

    static {
        DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE.setAttributes(ATTRIBUTES);
        DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE.setDistance(0.5);
    }

    static {
        DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION.setDistance(0.5);
        DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION.setTaskDefinitionId(TASK_DEFINITION_ID);
    }

    static {
        DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE.setAttributes(ATTRIBUTES);
    }

    static {
        DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID.setCampaign(CAMPAIGN_ID);
    }

    @BeforeClass
    public static void setUpTest() {
        chimeraLiteRepository = mock(ChimeraLiteRepository.class);
        foxtrotEventIngestionService = mock(FoxtrotEventIngestionService.class);
        postFilterSortingStrategyExecutor = new PostFilterSortingStrategyExecutor(
                chimeraLiteRepository, foxtrotEventIngestionService);
    }

    @Test
    public void testSortNullConfig() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(null);
        assertEquals(
                DISCOVERY_TASK_INSTANCES,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortNullPriorities() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().build());
        assertEquals(
                DISCOVERY_TASK_INSTANCES,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortEmptyPriorities() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().priorities(List.of()).build());
        assertEquals(
                DISCOVERY_TASK_INSTANCES,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortAll() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().treatmentGroup("sort_all").priorities(
                        List.of(PrioritiseStrategyPriority.builder().priority(1).objectives(OBJECTIVES).build())
                ).build());
        List<DiscoveryTaskInstance> expectedTaskOrder = List.of(
                DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_1,
                DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
                DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID);
        assertEquals(
                expectedTaskOrder,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortTop2() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().treatmentGroup("sort_top_2").priorities(
                        List.of(PrioritiseStrategyPriority.builder().priority(1).objectives(OBJECTIVES).topNLimit(2).build())
                ).build());
        List<DiscoveryTaskInstance> expectedTaskOrder = List.of(
                DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_1,
                DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
                DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID);
        assertEquals(
                expectedTaskOrder,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortWithinDistance() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().treatmentGroup("sort_within_distance").priorities(
                        List.of(PrioritiseStrategyPriority.builder().priority(1).distanceThresholdKms(1.0).build())
                ).build());
        List<DiscoveryTaskInstance> expectedTaskOrder = List.of(
                DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
                DISCOVERY_TASK_INSTANCE_1,
                DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID);
        assertEquals(
                expectedTaskOrder,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortWithinTaskDefinitions() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().treatmentGroup("sort_within_task_definitions").priorities(
                        List.of(PrioritiseStrategyPriority.builder()
                                .priority(1)
                                .taskDefinitionIds(Set.of(TASK_DEFINITION_ID))
                                .build())
                ).build());
        List<DiscoveryTaskInstance> expectedTaskOrder = List.of(
                DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
                DISCOVERY_TASK_INSTANCE_1,
                DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID);
        assertEquals(
                expectedTaskOrder,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortWithinCampaignIds() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().treatmentGroup("sort_within_campaign_ids").priorities(
                        List.of(PrioritiseStrategyPriority.builder()
                                .priority(1)
                                .campaigns(Set.of(CAMPAIGN_ID))
                                .build())
                ).build());
        List<DiscoveryTaskInstance> expectedTaskOrder = List.of(
                DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID,
                DISCOVERY_TASK_INSTANCE_1,
                DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
                DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE);
        assertEquals(
                expectedTaskOrder,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

    @Test
    public void testSortWithMultiple() {
        when(chimeraLiteRepository.getChimeraConfig(
                PostFilterSortingStrategyExecutor.CHIMERA_KEY, AGENT_ID, PrioritiseStrategyConfig.class))
                .thenReturn(PrioritiseStrategyConfig.builder().treatmentGroup("sort_within_multiple").priorities(
                        List.of(PrioritiseStrategyPriority.builder()
                                        .priority(1)
                                        .campaigns(Set.of(CAMPAIGN_ID))
                                        .build(),
                                PrioritiseStrategyPriority.builder()
                                        .priority(2)
                                        .taskDefinitionIds(Set.of(TASK_DEFINITION_ID))
                                        .build(),
                                PrioritiseStrategyPriority.builder()
                                        .priority(3)
                                        .objectives(Set.of(OBJECTIVE_1))
                                        .build())
                ).build());
        List<DiscoveryTaskInstance> expectedTaskOrder = List.of(
                DISCOVERY_TASK_INSTANCE_5_WITH_CAMPAIGN_ID,
                DISCOVERY_TASK_INSTANCE_3_WITH_TASK_DEFINITION,
                DISCOVERY_TASK_INSTANCE_2_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_4_WITH_OBJECTIVE,
                DISCOVERY_TASK_INSTANCE_1);
        assertEquals(
                expectedTaskOrder,
                postFilterSortingStrategyExecutor.execute(AGENT_ID, DISCOVERY_TASK_INSTANCES));
    }

}