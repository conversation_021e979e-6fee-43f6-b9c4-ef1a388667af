package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.utils.IdUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.UnaryOperator;

@RunWith(MockitoJUnitRunner.class)
public class TaskInstanceRepositoryImplTest extends LegionTaskBaseTest {

  @Test
  public void testSave() {
    String taskInstanceId = UUID.randomUUID().toString().substring(0, 8);
    StoredTaskInstance storedAgentTaskInstance = StoredTaskInstance.builder()
            .taskInstanceId(taskInstanceId)
            .secondaryIndexSyncRequired(false)
            .entityId("FKRT")
            .entityType(EntityType.SECTOR)
            .actionId("actionId")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .curState(LegionTaskStateMachineState.INITIATED)
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .partitionId(1)
            .taskDefinitionId("TASK_DEFINITION_ID")
            .build();

    StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
            .actor("ACTOR")
            .taskInstance(storedAgentTaskInstance)
            .toState(LegionTaskStateMachineState.CREATED)
            .fromState(LegionTaskStateMachineState.INITIATED)
            .event(LegionTaskStateMachineEvent.CREATED)
            .transitionId(1)
            .build();

    // saving the one managing the relation
    taskInstanceRepository.save(storedAgentTaskInstance);
    taskTransitionRepository.save(storedTaskTransition);

    Optional<StoredTaskInstance> savedTaskInstanceWithStatus = taskInstanceRepository.get(taskInstanceId);
    Assert.assertTrue( savedTaskInstanceWithStatus.get().getTaskTransitions().size() == 1 );

    StoredTaskTransition storedTaskTransition1 = StoredTaskTransition.builder()
            .actor("ACTOR")
            .taskInstance(storedAgentTaskInstance)
            .toState(LegionTaskStateMachineState.BOUNDED_ASSIGNED)
            .fromState(LegionTaskStateMachineState.CREATED)
            .event(LegionTaskStateMachineEvent.ASSIGNMENT)
            .transitionId(2)
            .build();

    taskTransitionRepository.save(storedTaskTransition1);
    Optional<StoredTaskInstance> savedTaskInstanceWithStatus2 = taskInstanceRepository.get(taskInstanceId);
    List<StoredTaskTransition> storedTaskTransitions = taskTransitionRepository.get(taskInstanceId);
    Optional<StoredTaskTransition> storedTaskTransitionVersion = taskTransitionRepository.get(taskInstanceId, 2);
    Assert.assertTrue(storedTaskTransitions.size() == 2);
    Assert.assertTrue( savedTaskInstanceWithStatus2.get().getTaskTransitions().size() == 2 );
    Assert.assertTrue(storedTaskTransitionVersion.get().getToState() == LegionTaskStateMachineState.BOUNDED_ASSIGNED);
  }

  @Test
  public void testGet() {
    String taskInstanceId = UUID.randomUUID().toString().substring(0, 8);
    StoredTaskInstance storedSectorTaskInstance = StoredTaskInstance.builder()
            .taskInstanceId(taskInstanceId)
            .secondaryIndexSyncRequired(false)
            .entityId("FKRT")
            .entityType(EntityType.SECTOR)
            .actionId("actionId")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .curState(LegionTaskStateMachineState.INITIATED)
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .partitionId(1)
            .taskDefinitionId("TASK_DEFINITION_ID")
            .build();

    taskInstanceRepository.save(storedSectorTaskInstance);
    Optional<StoredTaskInstance> savedTaskInstanceWithStatus2 = taskInstanceRepository.get(taskInstanceId);
    Assert.assertTrue(savedTaskInstanceWithStatus2.get().getTaskDefinitionId().equals("TASK_DEFINITION_ID"));
  }

  @Test
  public void testUpdate() {
    String taskInstanceId = UUID.randomUUID().toString().substring(0, 8);
    StoredTaskInstance storedAgentTaskInstance = StoredTaskInstance.builder()
            .taskInstanceId(taskInstanceId)
            .secondaryIndexSyncRequired(false)
            .entityId("FKRT")
            .entityType(EntityType.SECTOR)
            .partitionId(1)
            .actionId("actionId")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .curState(LegionTaskStateMachineState.INITIATED)
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .taskDefinitionId("TASK_DEFINITION_ID")
            .build();

    taskInstanceRepository.save(storedAgentTaskInstance);
    taskInstanceRepository.update(taskInstanceId, new UnaryOperator<StoredTaskInstance>() {
      @Override
      public StoredTaskInstance apply(StoredTaskInstance storedTaskInstance) {
        storedTaskInstance.setTaskTransitions(Collections.emptyList());
        return storedTaskInstance;
      }
    });

    Optional<StoredTaskInstance> savedTaskInstanceWithStatus2 = taskInstanceRepository.get(taskInstanceId);
//    Assert.assertTrue(savedTaskInstanceWithStatus2.get().getSubEntityId().equals("RANDOM_STORE"));
    Assert.assertTrue(savedTaskInstanceWithStatus2.get().getTaskTransitions().size() == 0);
  }

  @Test
  public void generateTaskInstanceId(){
    List<String> entityIds = new ArrayList<>();
    entityIds.add("M4VKOCLE84V_MS2212261145142406111270");
    entityIds.add("MGD3INHM8_MS2202011334597438031597");
    entityIds.add("MY0U5JCBAED_MS2301041556492756111094");
    entityIds.add("MIAKXFZGO8T_MS2301091523163584051198");
    entityIds.add("M2206131157566464417610_MS2206131157572964417513");

    IdUtils idUtils = new IdUtils();
    for (String entityId: entityIds){
      int entityIdShard = idUtils.getShard(entityId, storedTaskInstance);
      String taskInstanceId = taskInstanceRepository.generateTaskInstanceId(entityId);
      int taskInstanceShard = idUtils.getShard(taskInstanceId, storedTaskInstance);
      Assert.assertEquals(entityIdShard, taskInstanceShard);
    }

  }

}