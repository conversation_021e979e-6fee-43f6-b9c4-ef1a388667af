package com.phonepe.merchant.legion.tasks.search.query;

import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClient;
import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClientBuilder;
import com.phonepe.merchant.gladius.models.hotspots.Coordinates;
import com.phonepe.merchant.gladius.models.hotspots.HotspotRegion;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.repository.HotspotRepository;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.GeoPolygonQueryBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.HOTSPOT_CONFIG_NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.HOTSPOT_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class HotspotSyncRequestQueryBuilderTest {

    @Mock
    private HotspotRepository hotspotRepository;

    @Mock
    private FilterCraftBuilderCache filterCraftBuilderCache;

    private StoredHotspot storedHotspot;

    @Mock
    private FilterCraftBuilderCacheKey cacheKey;

    @Mock
    FilterCraftClient filterCraftClient;

    @Mock
    private BoolQueryBuilder boolQueryBuilder;

    @Mock
    private LegionService legionService;

    @Mock
    private RestrictionQueryBuilder restrictionQueryBuilder;

    @InjectMocks
    private HotspotRequestQueryBuilder hotspotRequestQueryBuilder;

    private String actorId1 = "actorId1";

    private String actorId2 = "actorId2";

    private String expectedBoolQuery = """
            {
              "bool" : {
                "must" : [
                  {
                    "geo_polygon" : {
                      "location" : {
                        "points" : [
                          [
                            1.0,
                            2.0
                          ],
                          [
                            3.0,
                            4.0
                          ],
                          [
                            5.0,
                            6.0
                          ],
                          [
                            1.0,
                            2.0
                          ]
                        ]
                      },
                      "validation_method" : "STRICT",
                      "ignore_unmapped" : false,
                      "boost" : 1.0
                    }
                  }
                ],
                "adjust_pure_negative" : true,
                "boost" : 1.0
              }
            }""";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        storedHotspot = StoredHotspot.builder()
                .filterCraftConfig("filterCraftConfig")
                .region(HotspotRegion.builder()
                        .geofence(Arrays.asList(
                                Coordinates.builder()
                                        .longitude(1.0)
                                        .latitude(2.0)
                                        .build(),
                                Coordinates.builder()
                                        .longitude(3.0)
                                        .latitude(4.0)
                                        .build(),
                                Coordinates.builder()
                                        .longitude(5.0)
                                        .latitude(6.0)
                                        .build()
                        ))
                        .build())
                .build();
        when(filterCraftBuilderCache.get(any())).thenReturn(filterCraftClient);
        when(filterCraftClient.convertToESFilter()).thenReturn(new BoolQueryBuilder());
        when(legionService.getAgentProfile(actorId1)).thenReturn(AgentProfile.builder().agentType(AgentType.FREELANCER).build());
        when(legionService.getAgentProfile(actorId2)).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(restrictionQueryBuilder.getRestrictionQueries(any(), anyString())).thenReturn(List.of());
    }

    @Test
    public void testBuildQueryWithHotspotIdSuccess() {
        when(hotspotRepository.get(anyString())).thenReturn(Optional.of(storedHotspot));
        BoolQueryBuilder result = hotspotRequestQueryBuilder.buildQuery(actorId1, "hotspotId");
        assertNotNull(result);
        verify(hotspotRepository, times(1)).get("hotspotId");
        verify(filterCraftBuilderCache, times(1)).get(any(FilterCraftBuilderCacheKey.class));
        assertEquals(expectedBoolQuery ,result.toString());
    }

    @Test
    public void testBuildQueryWithHotspotSuccess() {
        BoolQueryBuilder result = hotspotRequestQueryBuilder.buildQuery(actorId1, storedHotspot);
        assertNotNull(result);
        verify(filterCraftBuilderCache, times(1)).get(any(FilterCraftBuilderCacheKey.class));
        assertEquals(expectedBoolQuery ,result.toString());
    }

    @Test
    public void testBuildQueryWithHotspotIdNotFound() {
        when(hotspotRepository.get(anyString())).thenReturn(Optional.empty());
        LegionException exception = assertThrows(LegionException.class, () -> {
            hotspotRequestQueryBuilder.buildQuery(actorId1, "hotspotId");
        });
        assertEquals(HOTSPOT_NOT_FOUND, exception.getErrorCode());
        verify(hotspotRepository, times(1)).get("hotspotId");
    }

    @Test
    public void testBuildQueryWithNullHotspot() {
        LegionException exception = assertThrows(LegionException.class, () -> {
            hotspotRequestQueryBuilder.buildQuery(actorId1, (StoredHotspot) null);
        });
        assertEquals(HOTSPOT_NOT_FOUND, exception.getErrorCode());
    }

    @Test
    public void testBuildQueryWithMissingFilterConfig() {
        StoredHotspot hotspot = new StoredHotspot();
        hotspot.setFilterCraftConfig(null);
        when(hotspotRepository.get(anyString())).thenReturn(Optional.of(hotspot));

        LegionException exception = assertThrows(LegionException.class, () -> {
            hotspotRequestQueryBuilder.buildQuery(actorId1,"hotspotId");
        });
        assertEquals(HOTSPOT_CONFIG_NOT_FOUND, exception.getErrorCode());
        verify(hotspotRepository, times(1)).get("hotspotId");
    }

}
