package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tags.TagsTenant;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;


@RunWith(MockitoJUnitRunner.class)
public class StoredTagRepositoryTest extends LegionTaskBaseTest {


    @Test
    public void save(){
        StoreTag storeTag = StoreTag.builder()
                .tagId("mohit")
                .tenantId("default")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        Assert.assertEquals("mohit", storedTagRepository.get("mohit").getTagId());
    }

    @Test
    public void get(){
        StoreTag storeTag = StoreTag.builder()
                .tagId("mohitPuri")
                .tenantId("defaultCampaign")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        Assert.assertEquals("mohitPuri", storedTagRepository.get("mohitPuri").getTagId());
    }

    @Test
    public void update(){
        StoreTag storeTag = StoreTag.builder()
                .tagId("update")
                .tenantId("defaultCampaign")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        StoreTag tag = storedTagRepository.get("update");
        boolean active = storedTagRepository.update(tag, tag.getTenantId(), tagg -> {
            tag.setActive(!tag.isActive());
            return tag;
        });
        Assert.assertTrue(active);
        Assert.assertFalse(storedTagRepository.get("update").isActive());
    }

    @Test
    public void searchTagWithTenantId(){
        StoreTag storeTag = StoreTag.builder()
                .tagId("mohit_Puri")
                .tenantId("campaign")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        Assert.assertEquals(1,  storedTagRepository.searchTagWithTenantId("campaign", true).size());
    }


    @Test
    public void searchTag(){
        StoreTag storeTag = StoreTag.builder()
                .tagId("mohit-Puri")
                .tenantId("defaultCampaign")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        Optional<StoreTag> storeTagOptional = storedTagRepository.searchTag("mohit-Puri", "defaultCampaign");
        Assert.assertTrue( storeTagOptional.isPresent());
        Assert.assertEquals("mohit-Puri", storeTagOptional.get().getTagId());
    }


    @Test
    public void searchActiveTag(){
        StoreTag storeTag = StoreTag.builder()
                .tagId("puri")
                .tenantId("defaultCampaign")
                .tenantType(TagsTenant.CAMPAIGN)
                .active(true)
                .createdBy("mohit")
                .updatedBy("mohit")
                .build();
        storedTagRepository.save(storeTag);
        Optional<StoreTag> storeTagOptional = storedTagRepository.searchActiveTag("puri", "defaultCampaign");
        Assert.assertTrue( storeTagOptional.isPresent());
        Assert.assertEquals("puri", storeTagOptional.get().getTagId());
        Assert.assertTrue( storeTagOptional.get().isActive());
    }

}
