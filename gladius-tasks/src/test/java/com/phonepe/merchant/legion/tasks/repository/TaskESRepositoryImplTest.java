package com.phonepe.merchant.legion.tasks.repository;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.rabbitmq.ActionMessagePublisher;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskESRepositoryImpl;
import com.phonepe.models.merchants.BusinessUnit;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.dropwizard.actors.actor.Actor;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static com.phonepe.merchant.gladius.models.entitystore.ActorMessageType.TASK_ES_CHANGE_EVENT;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getStoredTaskInstance;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.BAD_REQUEST;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskESRepositoryImplTest extends LegionTaskBaseTest {

    private static TaskESRepositoryImpl taskESRepository;
    private static ESRepository esRepository;
    private static final String TASK_INSTANCE_ID = "TASK_INSTANCE_ID";

    @BeforeClass
    public static void init() {

    }

    private void mockEs() {
        esRepository = mock(ESRepository.class);
        taskInstanceRepository = mock(TaskInstanceRepository.class);
        taskESRepository = new TaskESRepositoryImpl(esRepository,taskInstanceRepository, eventExecutor);
    }

    @Test(expected = LegionException.class)
    public void save() {
        //arrange
        mockEs();
        String value = "";
        doNothing().when(esRepository).save(TASK_INDEX,TASK_INSTANCE_ID,value);

        Actor actor = mock(Actor.class);
        when(actor.getType()).thenReturn(TASK_ES_CHANGE_EVENT);

        ActionMessagePublisher.addActor(actor);
        //call
        taskESRepository.save(TASK_INSTANCE_ID,value);

        //arrange
        mockEs();
        doThrow(LegionException.error(BAD_REQUEST))
                .when(esRepository).save(TASK_INDEX,TASK_INSTANCE_ID,value);
        //call
        taskESRepository.save(TASK_INSTANCE_ID,value);
    }

    @Test(expected = Test.None.class)
    public void update() {
        //arrange
        mockEs();
        String value = "";
        doNothing().when(esRepository).update(eq(TASK_INDEX),eq(TASK_INSTANCE_ID),eq(value),anyBoolean());

        //call
        taskESRepository.update(TASK_INSTANCE_ID,value,true);

        //arrange
        mockEs();
        doThrow(LegionException.error(BAD_REQUEST))
                .when(esRepository).update(eq(TASK_INDEX),eq(TASK_INSTANCE_ID),eq(value),anyBoolean());

        //call
        taskESRepository.update(TASK_INSTANCE_ID,value,false);
    }

    @Test(expected = Test.None.class)
    @Ignore("Will fix this test case later")
    public void syncWithDb() {
        //arrange
        mockEs();
        StoredTaskInstance storedTaskInstance = getStoredTaskInstance();
        String taskInstanceId = storedTaskInstance.getTaskInstanceId();
        when(taskInstanceRepository.get(anyString())).thenReturn(Optional.of(storedTaskInstance));
        doNothing().when(esRepository).update(any(String.class),any(String.class),any(String.class),any(Boolean.class));

        //call
        taskESRepository.syncWithDB(taskInstanceId,true);

        //arrange
        mockEs();
        doThrow(LegionException.error(BAD_REQUEST))
                .when(esRepository).update(any(String.class),any(String.class),any(String.class),any(Boolean.class));

        //call
        taskESRepository.syncWithDB(taskInstanceId,false);
    }

    @Test(expected = Test.None.class)
    public void create() throws JsonProcessingException {
        //arrange
        mockEs();
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder().build();
        ObjectMapper objectMapper = new ObjectMapper();
        String value = objectMapper.writeValueAsString(discoveryTaskInstance);
        doNothing().when(esRepository).save(TASK_INDEX,TASK_INSTANCE_ID,value);

        //call
        taskESRepository.create(TASK_INSTANCE_ID,discoveryTaskInstance);

        //arrange
        mockEs();
        doThrow(LegionException.error(BAD_REQUEST))
                .when(esRepository).save(TASK_INDEX,TASK_INSTANCE_ID,value);

        //call
        taskESRepository.create(TASK_INSTANCE_ID,discoveryTaskInstance);
    }

    @Test(expected = Test.None.class)
    public void testIngestEvent() {
        mockEs();
        doNothing().when(eventExecutor).ingest(any());
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.INTERNAL)
                .location(EsLocationRequest.builder()
                        .lat(1.0)
                        .lon(1.0)
                        .build())
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .taskInstanceId("taskInstanceId")
                .taskDefinitionId("taskDefinitoinId")
                .entityType(EntityType.STORE)
                .entityId("entityId")
                .assignedTo("assignedTO")
                .actionId("ACTION_ID")
                .active(true)
                .build();
        taskESRepository.publishEvent(discoveryTaskInstance) ;
    }
}
