package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tags.TagRequest;
import com.phonepe.merchant.gladius.models.tags.TagResponse;
import com.phonepe.merchant.gladius.models.tags.TagsTenant;
import com.phonepe.merchant.legion.tasks.services.TagService;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


public class TagResourceTest {

    private  static TagResource tagResource;
    private static TagService tagService;


    @BeforeClass
    public static void init() {

        tagService = mock(TagService.class);
       tagResource = new TagResource(tagService);
    }



    @Test
    public void getTag(){
        when(tagService.getTag(any())).thenReturn(TagResponse.builder()
                .tenantType(TagsTenant.CAMPAIGN)
                .tenantId("resource")
                .active(true)
                .tagId("tagId").build());
        TagResponse tagResponse = tagResource.getTag("tagId");
        Assert.assertEquals("tagId", tagResponse.getTagId());
    }

    @Test
    public void saveTag(){
        when(tagService.saveTag(any())).thenReturn(TagResponse.builder()
                .tenantType(TagsTenant.CAMPAIGN)
                .tenantId("resource")
                .active(true)
                .tagId("tagId").build());
        TagResponse tagResponse = tagResource.saveTag(TagRequest.builder()
                .tenantType(TagsTenant.CAMPAIGN)
                .tenantId("resource")
                .active(true)
                .tagId("tagId").build());
        Assert.assertEquals("tagId", tagResponse.getTagId());
    }

    @Test
    public void searchTagWithTagAndTenantId(){
        when(tagService.getTenantTag(any(), anyString())).thenReturn(StoreTag.builder()
                .tenantType(TagsTenant.CAMPAIGN)
                .tenantId("resource")
                .active(true)
                .tagId("tagId").build());
        StoreTag tagResponse = tagResource.searchTagByTenantAndTag("resource", "tagId");
        Assert.assertEquals("tagId", tagResponse.getTagId());
    }

    @Test
    public void searchTagByTenant(){
        when(tagService.getTagsForTenant(any(), anyBoolean())).thenReturn(Collections.singletonList(StoreTag.builder()
                .tenantType(TagsTenant.CAMPAIGN)
                .tenantId("resource")
                .active(true)
                .tagId("tagId").build()));
        List<StoreTag> tagResponse = tagResource.searchTagByTenant("resource", true);
        Assert.assertEquals(1, tagResponse.size());
        Assert.assertEquals("tagId", tagResponse.get(0).getTagId());

    }



}
