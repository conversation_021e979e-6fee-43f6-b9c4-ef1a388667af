package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.gladius.models.entitystore.StoreEntity;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskHistoryRequest;
import com.phonepe.merchant.gladius.models.tasks.response.EntityTaskHistoryResponse;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.services.EntityHistoryService;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class EntityResourceTest {

    private static EntityStore entityStore;
    private static EntityResource entityResource;
    private static EntityHistoryService entityHistoryService;

    @BeforeClass
    public static void init() {
        entityStore = mock(EntityStore.class);
        entityHistoryService = mock(EntityHistoryService.class);
        entityResource = new EntityResource(entityStore, entityHistoryService);
    }

    @Test
    public void get() {
        //arrange
        String entityId = "entityId";
        EntityType entityType = EntityType.STORE;
        EntityStoreRequest request = EntityStoreRequest.builder()
                .referenceId(entityId)
                .entityType(entityType)
                .build();
        Optional<Entity> expectedResponse = Optional.of(StoreEntity.builder().build());
        when(entityStore.getById(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<Optional<Entity>> actualResponse = entityResource.get(entityId,entityType);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(expectedResponse,actualResponse.getData());
    }

    @Test
    public void testGetTasksHistoryOfAnEntity() {
        EntityTaskHistoryResponse mockResponses = mock(EntityTaskHistoryResponse.class);
        when(entityHistoryService.getEntityTaskHistory(any(), any())).thenReturn(mockResponses);
        EntityTaskHistoryRequest request = EntityTaskHistoryRequest.builder().entityId("entity_id").actionId("action").pageNo(1)
                .pageSize(100).build();
        GenericResponse<EntityTaskHistoryResponse> response = entityResource.getTasksHistoryOfAnEntity(ServiceUserPrincipal.builder()
                .userAuthDetails(UserAuthDetails.builder().userDetails(HumanUserDetails.builder().userId("user-id").build()).build()).build(), null, request, null);
        Assert.assertNotNull(response);
    }

}
