package com.phonepe.merchant.legion.tasks;

import com.phonepe.frontend.chimera.lite.ChimeraLiteBundle;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.core.config.DiscoveryViewRestrictionConfig;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.sharding.DBShardingBundle;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;


import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class TaskModuleTest {

    private AppConfig appConfig;
    private DBShardingBundle<AppConfig> shardingBundle;
    private RabbitmqActorBundle<AppConfig> rmqBundle;
    private ChimeraLiteBundle<AppConfig> chimeraLiteBundle;

    private TaskModule taskModule;

    @BeforeEach
    void setUp() {
        appConfig = mock(AppConfig.class);
        shardingBundle = mock(DBShardingBundle.class);
        rmqBundle = mock(RabbitmqActorBundle.class);
        chimeraLiteBundle = mock(ChimeraLiteBundle.class);
        taskModule = new TestTaskModule(shardingBundle, rmqBundle, chimeraLiteBundle);
    }

    @Test
    void getRestrictionContextSectorProfileTags() {
        DiscoveryViewRestrictionConfig config = DiscoveryViewRestrictionConfig.builder().build();
        when(appConfig.getDiscoveryViewRestrictionConfig()).thenReturn(config);
        Assert.assertEquals(config, taskModule.getRestrictionContextSectorProfileTags(appConfig));
    }

    private static class TestTaskModule extends TaskModule{
        public TestTaskModule(DBShardingBundle<AppConfig> shardingBundle, RabbitmqActorBundle<AppConfig> rmqBundle, ChimeraLiteBundle<AppConfig> chimeraLiteBundle) {
            super(shardingBundle, rmqBundle, chimeraLiteBundle);
        }

        @Override
        protected void configure() {
            super.configure();
        }
    }

    @Test
    void getLeadTaskConfigSuccess() {
        LeadCreationConfig config = LeadCreationConfig.builder().build();
        when(appConfig.getLeadCreationConfig()).thenReturn(config);
        Assertions.assertEquals(config, taskModule.getLeadTaskConfig(appConfig));
    }
}