package com.phonepe.merchant.legion.tasks.search.query.restrictions;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.filtercraft.es.internal.converter.filters.impl.ConditionToEsQueryConverterImpl;
import com.phonepe.merchant.filtercraft.es.internal.converter.filters.impl.FilterToEsQueryConverterImpl;
import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClientBuilder;
import com.phonepe.merchant.filtercraft.filters.client.impl.FilterCraftClientBuilderImpl;
import com.phonepe.merchant.filtercraft.internal.evaluators.impl.ConditionEvaluatorImpl;
import com.phonepe.merchant.filtercraft.internal.evaluators.impl.FilterEvaluatorImpl;
import com.phonepe.merchant.filtercraft.internal.parsers.impl.FilterParserImpl;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.config.DiscoveryViewRestrictionConfig;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.GeneralPurposeCache;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.models.DiscoveryRestrictionContext;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.CHIMERA_RESTRICTIONS_CONFIG;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.CHIMERA_RESTRICTIONS_CONFIG_DISABLED;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.CHIMERA_RESTRICTIONS_CONFIG_EMPTY;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.CHIMERA_RESTRICTIONS_CONFIG_NULL;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.EXPECTED_DFOS_RESTRICTION_QUERY;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.EXPECTED_FOS_RESTRICTION_QUERY;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.EXPECTED_LENDING_RESTRICTION_QUERY_ON_LENDING_AGENT;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.EXPECTED_LENDING_RESTRICTION_QUERY_ON_OTHER_AGENT;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_CONDITION_DFOS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_CONDITION_FOS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_LENDING_CONDITION_ON_LENDING_AGENTS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_LENDING_CONDITION_ON_OTHER_AGENTS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_LENDING_RESTRICTION_ON_LENDING_AGENTS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_LENDING_RESTRICTION_ON_OTHER_AGENTS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_RESTRICTION_DFOS;
import static com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionsQueryBuilderConstants.GLADIUS_RESTRICTION_FOS;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.DEFAULT_TREATMENT_GROUP;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.EXCLUSIVE_LENDING_TAG;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RestrictionQueryBuilderTest {
    private Map<CacheName, CacheConfig> cacheConfigs;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private ChimeraLiteRepository chimeraLiteRepository;
    private GeneralPurposeCache generalPurposeCache;
    private FilterCraftBuilderCache filterCraftBuilderCache;
    private FilterCraftClientBuilder filterCraftClientBuilder = new FilterCraftClientBuilderImpl(
            new FilterParserImpl(objectMapper),
            new FilterEvaluatorImpl(new ConditionEvaluatorImpl(), objectMapper),
            new FilterToEsQueryConverterImpl(new ConditionToEsQueryConverterImpl()));
    private static final Map<String, String> chimeraConfigMap= new HashMap<>();
    private RestrictionQueryBuilder restrictionQueryBuilder;

    private final DiscoveryRestrictionContext FosAgentContext = DiscoveryRestrictionContext.builder()
            .requestingAgentRole(AgentType.BDE)
            .ddpSectorIds(List.of("S1", "S2"))
            .build();
    private final DiscoveryRestrictionContext DfosAgentContext = DiscoveryRestrictionContext.builder()
            .requestingAgentRole(AgentType.DDP_FOS)
            .ddpSectorIds(List.of("S1", "S2"))
            .build();
    private final DiscoveryRestrictionContext LendingAgentContext = DiscoveryRestrictionContext.builder()
            .requestingAgentRole(AgentType.FREELANCER)
            .agentTags(List.of(EXCLUSIVE_LENDING_TAG))
            .exclusiveLendingSectorIds(List.of("S1", "S2"))
            .ddpSectorIds(List.of())
            .build();
    private final DiscoveryRestrictionContext LendingOtherAgentContext = DiscoveryRestrictionContext.builder()
            .requestingAgentRole(AgentType.BDE)
            .agentTags(List.of("T1"))
            .exclusiveLendingSectorIds(List.of("S1", "S2"))
            .ddpSectorIds(List.of())
            .build();
    private DiscoveryViewRestrictionConfig discoveryViewRestrictionConfig= DiscoveryViewRestrictionConfig.builder()
            .chimeraKey("gladius_restrictions_config_v3")
            .sectorProfileTags(List.of(EXCLUSIVE_LENDING_TAG))
            .build();

    static {
        chimeraConfigMap.put("gladius_restrictions_config_v3", CHIMERA_RESTRICTIONS_CONFIG);
        chimeraConfigMap.put("gladius_condition_fos", GLADIUS_CONDITION_FOS);
        chimeraConfigMap.put("gladius_restriction_fos", GLADIUS_RESTRICTION_FOS);
        chimeraConfigMap.put("gladius_condition_dfos", GLADIUS_CONDITION_DFOS);
        chimeraConfigMap.put("gladius_restriction_dfos", GLADIUS_RESTRICTION_DFOS);
        chimeraConfigMap.put("gladius_lending_condition_on_lending_agents", GLADIUS_LENDING_CONDITION_ON_LENDING_AGENTS);
        chimeraConfigMap.put("gladius_lending_restriction_on_lending_agents", GLADIUS_LENDING_RESTRICTION_ON_LENDING_AGENTS);
        chimeraConfigMap.put("gladius_lending_condition_on_other_agents", GLADIUS_LENDING_CONDITION_ON_OTHER_AGENTS);
        chimeraConfigMap.put("gladius_lending_restriction_on_other_agents", GLADIUS_LENDING_RESTRICTION_ON_OTHER_AGENTS);
    }

    @Before
    public void setUp() {
        when(chimeraLiteRepository.getStringValuedChimeraConfig(anyString())).thenAnswer(a -> chimeraConfigMap.get(a.getArgument(0)));
        SerDe.init(objectMapper);
        cacheConfigs = new HashMap<>();
        cacheConfigs.put(CacheName.FILTERCRAFT_BUILDER, new CacheConfig());
        cacheConfigs.put(CacheName.GENERAL_PURPOSE, new CacheConfig());
        filterCraftBuilderCache = new FilterCraftBuilderCache(cacheConfigs, mock(MetricRegistry.class), mock(CacheUtils.class), filterCraftClientBuilder);
        generalPurposeCache = new GeneralPurposeCache(cacheConfigs, mock(CacheUtils.class), mock(MetricRegistry.class));
        restrictionQueryBuilder = new RestrictionQueryBuilder(chimeraLiteRepository, filterCraftBuilderCache, generalPurposeCache,discoveryViewRestrictionConfig);
    }

    @Test
    public void testGetSectorProfileTags() {
        assertEquals(discoveryViewRestrictionConfig.getSectorProfileTags(), restrictionQueryBuilder.getSectorProfileTags());
    }

    @Test
    public void testFosAgentRestriction() {
        // Test method when restrictions are enabled and present
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(FosAgentContext, DEFAULT_TREATMENT_GROUP);
        // Verify the expected number of queries returned
        assertEquals(1, queries.size());
        BoolQueryBuilder actualQuery = queries.get(0);
        assertEquals(EXPECTED_FOS_RESTRICTION_QUERY, actualQuery.toString());
    }

    @Test
    public void testDfosAgentRestriction() {
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries( DfosAgentContext, DEFAULT_TREATMENT_GROUP);
        assertEquals(1, queries.size());
        BoolQueryBuilder actualQuery = queries.get(0);
        assertEquals(EXPECTED_DFOS_RESTRICTION_QUERY, actualQuery.toString());
    }

    @Test
    public void testNoRestriction() {
        when(chimeraLiteRepository.getStringValuedChimeraConfig(anyString())).thenReturn(null);
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(DfosAgentContext, DEFAULT_TREATMENT_GROUP);
        assertEquals(0, queries.size());
    }

    @Test
    public void testRestrictionDisabled() {
        when(chimeraLiteRepository.getStringValuedChimeraConfig(anyString())).thenReturn(CHIMERA_RESTRICTIONS_CONFIG_DISABLED);
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(DfosAgentContext, DEFAULT_TREATMENT_GROUP);
        assertEquals(0, queries.size());
    }

    @Test
    public void testRestrictionEmpty() {
        when(chimeraLiteRepository.getStringValuedChimeraConfig(anyString())).thenReturn(CHIMERA_RESTRICTIONS_CONFIG_EMPTY);
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(DfosAgentContext, DEFAULT_TREATMENT_GROUP);
        assertEquals(0, queries.size());
    }

    @Test
    public void testRestrictionNull() {
        when(chimeraLiteRepository.getStringValuedChimeraConfig(anyString())).thenReturn(CHIMERA_RESTRICTIONS_CONFIG_NULL);
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(DfosAgentContext, DEFAULT_TREATMENT_GROUP);
        assertEquals(0, queries.size());
    }

    @Test
    public void testLendingAgentRestriction() {
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(LendingAgentContext, DEFAULT_TREATMENT_GROUP);
        BoolQueryBuilder actualQuery = queries.get(1);
        assertEquals(EXPECTED_LENDING_RESTRICTION_QUERY_ON_LENDING_AGENT, actualQuery.toString());
    }

    @Test
    public void testLendingAgentRestrictionOnOtherAgents() {
        List<BoolQueryBuilder> queries = restrictionQueryBuilder.getRestrictionQueries(LendingOtherAgentContext, DEFAULT_TREATMENT_GROUP);
        BoolQueryBuilder actualQuery = queries.get(1);
        assertEquals(EXPECTED_LENDING_RESTRICTION_QUERY_ON_OTHER_AGENT, actualQuery.toString());
    }
}
