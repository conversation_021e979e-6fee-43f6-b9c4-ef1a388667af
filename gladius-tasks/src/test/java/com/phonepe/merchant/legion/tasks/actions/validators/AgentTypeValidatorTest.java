package com.phonepe.merchant.legion.tasks.actions.validators;

import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.AgentTypeValidatorConfig;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AgentTypeValidatorTest {

    @Mock
    private LegionService legionService;

    @InjectMocks
    private AgentTypeValidator agentTypeValidator;

    @Mock
    private TaskAssignRequest taskAssignRequest;

    private AgentTypeValidatorConfig agentTypeValidatorConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        agentTypeValidatorConfig = new AgentTypeValidatorConfig();
        agentTypeValidatorConfig.setBlacklistedAgentTypes(Collections.singleton(AgentType.FREELANCER));
    }

    @Test
    void shouldReturnValidatedTrue_WhenAgentIsNotBlacklisted() {
        AgentProfile agentProfile = new AgentProfile();
        agentProfile.setAgentType(AgentType.AGENT);

        when(taskAssignRequest.getAssignedTo()).thenReturn("agent123");
        when(legionService.getAgentProfile("agent123")).thenReturn(agentProfile);

        ValidatorResponse response = agentTypeValidator.validate(taskAssignRequest, agentTypeValidatorConfig);

        assertTrue(response.isValidated());
        assertNull(response.getErrorCode());
        assertNull(response.getErrorMessage());
    }

    @Test
    void shouldReturnValidatedFalse_WhenAgentIsBlacklisted() {
        AgentProfile agentProfile = new AgentProfile();
        agentProfile.setAgentType(AgentType.FREELANCER);
        agentTypeValidator.validate(EntityType.TASK, agentTypeValidatorConfig);
        when(taskAssignRequest.getAssignedTo()).thenReturn("agent123");
        when(legionService.getAgentProfile("agent123")).thenReturn(agentProfile);

        ValidatorResponse response = agentTypeValidator.validate(taskAssignRequest, agentTypeValidatorConfig);

        assertFalse(response.isValidated());
        assertEquals(LegionTaskErrorCode.SELF_ASSIGN_NOT_ALLOWED, response.getErrorCode());
        assertEquals("Agent is not allowed to perform this action", response.getErrorMessage());
    }
}
