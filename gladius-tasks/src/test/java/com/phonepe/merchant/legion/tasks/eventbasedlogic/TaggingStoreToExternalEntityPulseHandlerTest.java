package com.phonepe.merchant.legion.tasks.eventbasedlogic;

import com.phonepe.growth.neuron.pulse.Pulse;
import com.phonepe.growth.neuron.pulse.Signal;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.junit.BeforeClass;
import org.junit.Test;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MERCHANT_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_INSTANCE_ID;
import static org.mockito.Mockito.mock;

public class TaggingStoreToExternalEntityPulseHandlerTest extends LegionTaskBaseTest {

    private static TaggingStoreToExternalEntityPulseHandler pulseHandler;
    private static ClientTaskService clientTaskService = mock(ClientTaskService.class);

    @BeforeClass
    public static void init() {
        pulseHandler = new TaggingStoreToExternalEntityPulseHandler(clientTaskService);
    }

    @Test(expected = Test.None.class)
    public void testPulseHandler() {
        Pulse callback = Pulse.builder()
                .signals(Arrays.asList(new Signal[]{
                        Signal.builder()
                                .name(MERCHANT_ID)
                                .value("MID")
                                .build(),
                        Signal.builder()
                                .name(STORE_ID)
                                .value("SID")
                                .build(),
                        Signal.builder()
                                .name(TASK_INSTANCE_ID)
                                .value("TID")
                                .build()
                }))
                .build();

        pulseHandler.preHandle(callback);
        pulseHandler.handle(callback);
        pulseHandler.handle(callback);
    }

}
