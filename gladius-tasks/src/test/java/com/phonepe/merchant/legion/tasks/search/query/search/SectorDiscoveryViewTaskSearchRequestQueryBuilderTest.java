package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.request.userattribute.TagsAttribute;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.UserRestrictionResponse;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.scoring.FunctionalQueryBuilder;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.SECTOR_PROCESSING_ERROR;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.EXCLUSIVE_LENDING_TAG;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SectorDiscoveryViewTaskSearchRequestQueryBuilderTest extends LegionTaskBaseTest {

    private SectorDiscoveryViewTaskSearchRequestQueryBuilder sectorDiscoveryViewTaskSearchRequestQueryBuilder;
    private final LegionService legionService;
    private final RestrictionQueryBuilder restrictionQueryBuilder;
    private SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    private final FunctionalQueryBuilder functionalQueryBuilder;
    public SectorDiscoveryViewTaskSearchRequestQueryBuilderTest() {
        legionService = mock(LegionService.class);
        sectorGeofenceQueryBuilder = mock(SectorGeofenceQueryBuilder.class);
        TaskEsUtils.init(sectorGeofenceQueryBuilder);
        when(sectorGeofenceQueryBuilder.visit(any(InFilter.class))).thenReturn(QueryBuilders.boolQuery());
        functionalQueryBuilder = mock(FunctionalQueryBuilder.class);
        restrictionQueryBuilder = mock(RestrictionQueryBuilder.class);
        sectorDiscoveryViewTaskSearchRequestQueryBuilder = new SectorDiscoveryViewTaskSearchRequestQueryBuilder(legionService, functionalQueryBuilder, mock(ViewKillSwitchExecutor.class), restrictionQueryBuilder);
    }

    @BeforeEach
    void afterEach() {
        Mockito.reset(legionService);
    }

    @Test
    public void test() {
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.FREELANCER)
                .managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).attributes(new ArrayList<>()).build();
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .sectorId("ABC1").filters(new ArrayList<>()).build();
        when(legionService.getAgentProfile("agent")).thenReturn(agentProfile);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.emptyMap()).build());
        doNothing().when(legionService).isSectorAccessible("ABC1", "agent");
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());

        Assertions.assertDoesNotThrow(() -> {
            sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });

        verify(legionService).getAgentProfile("agent");
    }

    @Test
    public void test2() {
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.FREELANCER)
                .managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).attributes(new ArrayList<>()).build();
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder().filters(new ArrayList<>()).build();

        when(legionService.getAgentProfile("agent")).thenReturn(agentProfile);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.emptyMap()).build());
        doNothing().when(legionService).isSectorAccessible("ABC1", "agent");

        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());
        Assertions.assertDoesNotThrow(() -> {
            sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });

        verify(legionService).getAgentProfile("agent");
    }

    @Test
    public void testWithAgentWithOtherEnabledFeatureThanKYC() {
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.FREELANCER)
                .managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).attributes(new ArrayList<>()).build();
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .sectorId("ABC1").filters(new ArrayList<>()).build();

        when(legionService.getAgentProfile("agent")).thenReturn(agentProfile);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.emptyMap()).build());
        doNothing().when(legionService).isSectorAccessible("ABC1", "agent");
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());

        Assertions.assertDoesNotThrow(() -> {
            sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });

        verify(legionService).getAgentProfile("agent");
        verify(legionService).isSectorAccessible("ABC1", "agent");
    }

    @Test
    public void testWithAgentWithSectorIds() {
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.FREELANCER)
                .managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).attributes(List.of(TagsAttribute.builder().tag(EXCLUSIVE_LENDING_TAG).active(true).build())).build();
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .sectorIds(List.of("ABC1","ABC2")).filters(new ArrayList<>()).build();

        when(legionService.getAgentProfile("agent")).thenReturn(agentProfile);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.singletonMap("ABC1", List.of(agentProfile))).build());
        when(restrictionQueryBuilder.getSectorProfileTags()).thenReturn(List.of(EXCLUSIVE_LENDING_TAG));
        doNothing().when(legionService).isSectorAccessible("ABC1", "agent");
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());

        Assertions.assertDoesNotThrow(() -> {
            sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });

        verify(legionService).getAgentProfile("agent");
        verify(legionService).isSectorAccessible("ABC1", "agent");
    }



    @Test
    public void testWithAgentWithSectorIdsException() {
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.FREELANCER)
                .managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).attributes(new ArrayList<>()).build();
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .sectorIds(List.of("ABC1","ABC2")).filters(new ArrayList<>()).build();

        when(legionService.getAgentProfile("agent")).thenReturn(agentProfile);
        doNothing().when(legionService).isSectorAccessible("ABC1", "agent");
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());
        doThrow(new NullPointerException()).when(legionService).isSectorAccessible("ABC1", "agent");
        LegionException exception = Assertions.assertThrows(LegionException.class, () -> {
            sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });
        Assertions.assertEquals(SECTOR_PROCESSING_ERROR, exception.getErrorCode());


        verify(legionService).getAgentProfile("agent");
        verify(legionService).isSectorAccessible("ABC1", "agent");
    }
    @Test
    public void testException() {
        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.FREELANCER)
                .managerId("manager").sectors(Arrays.asList("ABC1", "ABC3", "ABC2")).attributes(new ArrayList<>()).build();
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .sectorId("ABC1").filters(new ArrayList<>()).build();

        when(legionService.getAgentProfile("agent")).thenReturn(agentProfile);
        doThrow(new NullPointerException()).when(legionService).isSectorAccessible("ABC1", "agent");
        LegionException exception = Assertions.assertThrows(LegionException.class, () -> {
            sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });

        Assertions.assertEquals(SECTOR_PROCESSING_ERROR, exception.getErrorCode());

        verify(legionService, times(1)).getAgentProfile("agent");
        verify(legionService).isSectorAccessible("ABC1", "agent");
    }

}
