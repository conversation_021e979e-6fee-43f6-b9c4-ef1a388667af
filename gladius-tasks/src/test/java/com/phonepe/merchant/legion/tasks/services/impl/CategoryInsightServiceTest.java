package com.phonepe.merchant.legion.tasks.services.impl;

import com.phonepe.merchant.gladius.models.tasks.request.ActionDetails;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.CategoryDetails;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.tasks.cache.ActionDetailsCache;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class CategoryInsightServiceTest {

    @Mock
    private ChimeraLiteRepository chimeraRepository;

    @Mock
    private ActionDetailsCache actionDetailsCache;
    private static CategoryInsightServiceImpl categoryInsightService;

    private static final String CATEGORY_CHIMERA_KEY = "category_details_config";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        categoryInsightService = new CategoryInsightServiceImpl(chimeraRepository, actionDetailsCache);
    }


    @Test
    public void test_FetchCategorySummary_Success() {
        List<CategoryDetails.CategoryDetailConfig> details = List.of(CategoryDetails.CategoryDetailConfig.builder().categoryType(Category.LENDING).categoryName("Lending").build());
        CategoryDetails categoryDetails = CategoryDetails.builder().details(details).build();
        when(chimeraRepository.getChimeraConfig(CATEGORY_CHIMERA_KEY, CategoryDetails.class)).thenReturn(categoryDetails);
        Assertions.assertEquals(categoryDetails, categoryInsightService.fetchCategoryDetails());
    }

    @Test
    public void test_FetchAllActionsOfACategory_Success() {
        List<ActionDetails> actionDetails = new ArrayList<>();
        actionDetails.add(ActionDetails.builder().actionId("SMARTSPEAKER_DEPLOYMENT").description("SmartSpeaker Deployment").build());
        when(actionDetailsCache.get(Category.LENDING)).thenReturn(actionDetails);
        Assertions.assertEquals(actionDetails, categoryInsightService.fetchAllActionsOfCategory(Category.LENDING));
    }

    @Test
    public void test_FetchAllActionsOfACategory_Failure() {
        doThrow(new RuntimeException()).when(actionDetailsCache).get(Category.LENDING);
        Assertions.assertThrows(RuntimeException.class, () -> categoryInsightService.fetchAllActionsOfCategory(Category.LENDING));
    }

}
