package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Provider;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType;
import com.phonepe.merchant.gladius.models.tasks.request.ActionDetails;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ActionDetailsCacheTest {

    private Provider<TaskDefinitionRepository> taskDefinitionRepositoryProvider;

    private TaskDefinitionRepository taskDefinitionRepository;

    private CacheUtils cacheUtils;

    private MetricRegistry metricRegistry;
    private Map<CacheName, CacheConfig> cacheConfigs;
    private ActionDetailsCache actionDetailsCache;
    private ValidationService validationService;
    private ObjectMapper objectMapper = new ObjectMapper();


    private static final CacheName CACHE_NAME = CacheName.ACTION_DETAILS;

    @BeforeEach
    void setUp() {
        SerDe.init(objectMapper);
        metricRegistry = new MetricRegistry();
        cacheUtils = mock(CacheUtils.class);
        validationService = mock(ValidationService.class);
        cacheConfigs = Map.of(CACHE_NAME, new CacheConfig());
        taskDefinitionRepositoryProvider = mock(Provider.class);
        taskDefinitionRepository = mock(TaskDefinitionRepository.class);
        Mockito.reset(taskDefinitionRepository);
        when(taskDefinitionRepositoryProvider.get()).thenReturn(taskDefinitionRepository);
        actionDetailsCache = new ActionDetailsCache(cacheConfigs, taskDefinitionRepositoryProvider, metricRegistry, cacheUtils, validationService);
    }

    @Test
    void testConstructor_CacheRegister() {
        verify(cacheUtils).registerCache(CACHE_NAME, actionDetailsCache);
    }

    @Test
    void test_FetchAllActionsOfCategory() {
        List<ActionDetails> expected = List.of(ActionDetails.builder().actionId("action-id").build());
        TaskDefinitionAttributes attributes = TaskDefinitionAttributes.builder().taskType("test").category(Category.LENDING)
                .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder().actionId("action-id").build()))
                        .leadCreation(List.of()).build()).build();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder().actionId("action-id")
                .taskDefinitionId("task-definition-id").definitionAttributes(SerDe.writeValueAsBytes(attributes)).build();
        when(taskDefinitionRepositoryProvider.get()).thenReturn(taskDefinitionRepository);
        when(validationService.checkIfDefinitionIsWhitelisted("task-definition-id")).thenReturn(true);
        when(taskDefinitionRepository.getAll()).thenReturn(Collections.singletonList(storedTaskDefinition));
        List<ActionDetails> result = ActionDetailsCache.fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, Category.ALL, validationService);
        assertEquals(expected, result);
    }

    @Test
    void test_FetchAllActionsOfCategory_NullAttributes() {
        List<ActionDetails> expected = List.of();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder().actionId("action-id")
                .taskDefinitionId("task-definition-id").build();
        when(taskDefinitionRepositoryProvider.get()).thenReturn(taskDefinitionRepository);
        when(validationService.checkIfDefinitionIsWhitelisted("task-definition-id")).thenReturn(true);
        when(taskDefinitionRepository.getAll()).thenReturn(Collections.singletonList(storedTaskDefinition));
        List<ActionDetails> result = ActionDetailsCache.fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, Category.ALL, validationService);
        assertEquals(expected, result);
    }

    @Test
    void test_FetchAllActionsOfCategory_Null_Category() {
        List<ActionDetails> expected = List.of();
        TaskDefinitionAttributes attributes = TaskDefinitionAttributes.builder().taskType("test").category(Category.LENDING)
                .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder().actionId("action-id").build()))
                        .leadCreation(List.of()).build()).build();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder().actionId("action-id")
                .taskDefinitionId("task-definition-id").definitionAttributes(SerDe.writeValueAsBytes(attributes)).build();
        when(taskDefinitionRepositoryProvider.get()).thenReturn(taskDefinitionRepository);
        when(validationService.checkIfDefinitionIsWhitelisted("task-definition-id")).thenReturn(true);
        when(taskDefinitionRepository.getAll()).thenReturn(Collections.singletonList(storedTaskDefinition));
        List<ActionDetails> result = ActionDetailsCache.fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, Category.SMARTSPEAKER, validationService);
        assertEquals(expected, result);
    }


    @Test
    void test_FetchAllActionsOfSpecificCategory_FromConfig() {
        try (MockedStatic<LeadManagementConfiguration> leadConfigMock = mockStatic(LeadManagementConfiguration.class)) {
            leadConfigMock.when(LeadManagementConfiguration::getLeadUpdation).thenReturn(getMockActionToRemarkConfig());
            TaskDefinitionAttributes definitionAttributes = TaskDefinitionAttributes.builder().category(Category.LENDING).build();
            StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder().actionId("action-id")
                    .taskDefinitionId("task-definition-id").build();
            storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(definitionAttributes));
            when(taskDefinitionRepositoryProvider.get()).thenReturn(taskDefinitionRepository);
            when(taskDefinitionRepository.getAll()).thenReturn(Collections.singletonList(storedTaskDefinition));
            when(validationService.checkIfDefinitionIsWhitelisted("task-definition-id")).thenReturn(false);
            List<ActionDetails> result = ActionDetailsCache.fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, Category.LENDING, validationService);
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("action-id", result.get(0).getActionId());
        }
    }

    @Test
    void test_FetchAllActionsOfSpecificCategory() {
        TaskDefinitionAttributes attributes = TaskDefinitionAttributes.builder().taskType("test").category(Category.LENDING)
                .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder().actionId("action-id").build()))
                        .leadCreation(List.of()).build()).build();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder().actionId("action-id")
                .taskDefinitionId("task-definition-id").definitionAttributes(SerDe.writeValueAsBytes(attributes)).build();
        storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(attributes));
        List<StoredTaskDefinition> storedTaskDefinitions = List.of(storedTaskDefinition);
        when(taskDefinitionRepository.getAll()).thenReturn(storedTaskDefinitions);
        when(validationService.checkIfDefinitionIsWhitelisted("task-definition-id")).thenReturn(true);
        List<ActionDetails> result = ActionDetailsCache.fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, Category.LENDING, validationService);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("action-id", result.get(0).getActionId());

    }

    @Test
    void test_FetchAllActionsOfSpecificCategory_EmptyList() {
        TaskDefinitionAttributes attributes = TaskDefinitionAttributes.builder().taskType("test").category(Category.LENDING)
                .leadConfig(LeadConfig.builder().leadUpdation(List.of())
                        .leadCreation(List.of()).build()).build();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder().actionId("action-id")
                .taskDefinitionId("task-definition-id").definitionAttributes(SerDe.writeValueAsBytes(attributes)).build();
        storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(attributes));
        List<StoredTaskDefinition> storedTaskDefinitions = List.of(storedTaskDefinition);
        when(taskDefinitionRepository.getAll()).thenReturn(storedTaskDefinitions);
        when(validationService.checkIfDefinitionIsWhitelisted("task-definition-id")).thenReturn(true);
        List<ActionDetails> result = ActionDetailsCache.fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, Category.LENDING, validationService);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    private List<ActionToRemarkConfig> getMockActionToRemarkConfig() {
        return List.of(ActionToRemarkConfig.builder().taskType(UserGenTaskType.USER_CREATED_LENDING_DEPLOYMENT).actionId("action-id").config(Collections.emptyList()).build());
    }
}
