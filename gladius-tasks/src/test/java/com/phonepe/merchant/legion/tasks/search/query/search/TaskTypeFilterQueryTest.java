package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClient;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.search.query.filter.request.TaskTypeFiltersConfig;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TaskTypeFilterQueryTest {
    TaskTypeFilterQuery queryBuilder;

    @Mock
    ChimeraRepositoryImpl chimeraRepository;

    @Mock
    FilterCraftBuilderCache filterCraftBuilderCache;

    private static final String TASK_TYPE_MAPPING = "task_type_config_mapping";

    private static final String CONFIG = "{\n" +
            "  \"orConditions\": [\n" +
            "    [\n" +
            "      {\n" +
            "        \"type\": \"IN\",\n" +
            "        \"field\": \"task_definition_id\",\n" +
            "        \"values\": [\n" +
            "          \"TEST_DEPLOY_PHONEPE_SMARTSPEAKER1\",\n" +
            "          \"TEST_DEPLOY_PHONEPE_SMARTSPEAKER\",\n" +
            "          \"TD2205181445304182272286\",\n" +
            "          \"TD2204132046440228074452\"\n" +
            "        ]\n" +
            "      },\n" +
            "      {\n" +
            "        \"type\": \"WITHIN_LAST_N_MILLISECONDS\",\n" +
            "        \"field\": \"created_at\",\n" +
            "        \"intervalInMilliSeconds\": 300000\n" +
            "      }\n" +
            "    ]\n" +
            "  ]\n" +
            "}";
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        queryBuilder = new TaskTypeFilterQuery(chimeraRepository, filterCraftBuilderCache);
    }

    @Test
    public void testFetch() {
        Map<String, String> taskType = new HashMap<>();
        taskType.put("SS_DEPLOYMENTS", "CHIMERA_KEY1");
        taskType.put("EDC_DEPLOYMENTS", "CHIMERA_KEY2");

        TaskTypeFiltersConfig rules = new TaskTypeFiltersConfig(taskType);
        doReturn(rules).when(chimeraRepository).getChimeraConfig(TASK_TYPE_MAPPING, TaskTypeFiltersConfig.class);
        doReturn(CONFIG).when(chimeraRepository).getChimeraConfigString(anyString());
        FilterCraftClient filterCraftClient = mock(FilterCraftClient.class);

        when(filterCraftBuilderCache.get(any(FilterCraftBuilderCacheKey.class))).thenReturn(filterCraftClient);
        when(filterCraftClient.convertToESFilter()).thenReturn(new BoolQueryBuilder());
        Assert.assertNotNull(queryBuilder.taskTypeToFilterQuery("SS_DEPLOYMENTS"));
    }

    @Test
    public void testNullCase() {
        Map<String, String> taskType = new HashMap<>();
        taskType.put("SS_DEPLOYMENT", "CHIMERA_KEY1");
        taskType.put("EDC_DEPLOYMENTS", "CHIMERA_KEY2");

        TaskTypeFiltersConfig rules = new TaskTypeFiltersConfig(taskType);
        doReturn(rules).when(chimeraRepository).getChimeraConfig(TASK_TYPE_MAPPING, TaskTypeFiltersConfig.class);
        doReturn(CONFIG).when(chimeraRepository).getChimeraConfigString(anyString());
        FilterCraftClient filterCraftClient = mock(FilterCraftClient.class);

        when(filterCraftBuilderCache.get(any(FilterCraftBuilderCacheKey.class))).thenReturn(filterCraftClient);
        when(filterCraftClient.convertToESFilter()).thenReturn(new BoolQueryBuilder());
        Assert.assertNull(queryBuilder.taskTypeToFilterQuery("SS_DEPLOYMENTS"));
    }
}