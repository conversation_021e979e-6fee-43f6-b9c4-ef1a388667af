package com.phonepe.merchant.legion.tasks.actions.validators;

import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.LeadIntentValidatorConfig;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class LeadIntentValidatorTest {
    private TaskESRepository taskESRepository = mock(TaskESRepository.class);
    private TaskDefinitionService taskDefinitionService = mock(TaskDefinitionService.class);
    private ValidationService validationService = mock(ValidationService.class);
    private LeadIntentValidator<TaskAssignRequest> leadIntentValidator = new LeadIntentValidator(taskESRepository, taskDefinitionService, validationService);
    List<ActionToRemarkConfig> leadCreationConfig = Collections.emptyList(); // Mock lead creation config
    List<ActionToRemarkConfig> leadUpdationConfig = List.of(
           ActionToRemarkConfig.builder().actionId("ACTION_ID").config(List.of(IntentWithRemarks.builder().intent("HOT").build())).build()
    );

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        leadIntentValidator = new LeadIntentValidator<>(taskESRepository, taskDefinitionService, validationService);
    }

    @Test
    public void test_validate_failure(){
        LeadIntentValidatorConfig leadValidatorConfig = new LeadIntentValidatorConfig();
        TaskAssignRequest assignRequest = new TaskAssignRequest();
        assignRequest.setTaskInstanceId("TI");

        when(taskESRepository.get("TI")).thenReturn(DiscoveryTaskInstance.builder().taskInstanceMeta(TaskInstanceMeta.builder().build()).build());
        ValidatorResponse validatorResponse = leadIntentValidator.validate(assignRequest, leadValidatorConfig);
        Assertions.assertNotNull(validatorResponse);

    }

    @Test
    public void test_validate_failure1(){
        LeadIntentValidatorConfig leadValidatorConfig = new LeadIntentValidatorConfig();
        TaskAssignRequest assignRequest = new TaskAssignRequest();
        assignRequest.setTaskInstanceId("TI");
        when(taskESRepository.get("TI")).thenReturn(DiscoveryTaskInstance.builder()
                .taskInstanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(List.of(TaskMetaInformation.builder()
                                .type(TaskMetaType.AMOUNT_DUE)
                                .value(12).build())).build()).build());

        ValidatorResponse validatorResponse = leadIntentValidator.validate(assignRequest, leadValidatorConfig);
        Assertions.assertNotNull(validatorResponse);
    }

    @Test
    public void test_validate_invalid_lead() {
        LeadIntentValidatorConfig leadValidatorConfig = new LeadIntentValidatorConfig();
        TaskAssignRequest assignRequest = new TaskAssignRequest();
        assignRequest.setTaskInstanceId("TI");
        LeadManagementConfiguration.create(leadCreationConfig, leadUpdationConfig);
        when(taskESRepository.get("TI")).thenReturn(DiscoveryTaskInstance.builder()
                .taskDefinitionId("TD1")
                .taskInstanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(List.of(TaskMetaInformation.builder()
                                .type(TaskMetaType.LEAD_INTENT)
                                .value("TEST_LEAD").build())).build()).build());
        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId("TD1")
                .build())).thenReturn(TaskDefinitionInstance.builder()
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder()
                                .config(List.of(IntentWithRemarks.builder().intent("intent").build())).actionId("testAction").build())).build())
                        .build()).build());
        ValidatorResponse validatorResponse = leadIntentValidator.validate(assignRequest, leadValidatorConfig);
        Assertions.assertNotNull(validatorResponse);
    }

    @Test
    public void test_validate_lead_from_definition() {
        LeadIntentValidatorConfig leadValidatorConfig = new LeadIntentValidatorConfig();
        TaskAssignRequest assignRequest = new TaskAssignRequest();
        assignRequest.setTaskInstanceId("TI");
        LeadManagementConfiguration.create(leadCreationConfig, leadUpdationConfig);
        when(taskESRepository.get("TI")).thenReturn(DiscoveryTaskInstance.builder()
                .taskDefinitionId("TD1")
                .taskInstanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(List.of(TaskMetaInformation.builder()
                                .type(TaskMetaType.LEAD_INTENT)
                                .value("TEST_LEAD").build())).build()).build());
        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId("TD1")
                .build())).thenReturn(TaskDefinitionInstance.builder()
                        .taskDefinitionId("TD1")
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .leadConfig(LeadConfig.builder().leadUpdation(List.of(ActionToRemarkConfig.builder()
                                .config(List.of(IntentWithRemarks.builder().intent("intent").build()))
                                .config(List.of(IntentWithRemarks.builder().intent("HOT").build())).actionId("testAction").build())).build())
                        .build()).build());
        when(validationService.checkIfDefinitionIsWhitelisted("TD1")).thenReturn(true);
        ValidatorResponse validatorResponse = leadIntentValidator.validate(assignRequest, leadValidatorConfig);
        Assertions.assertNotNull(validatorResponse);
    }

    @Test
    public void test_validate_lead_from_config() {
        try (MockedStatic<LeadManagementConfiguration> leadConfigMock = mockStatic(LeadManagementConfiguration.class)) {
            LeadIntentValidatorConfig leadValidatorConfig = new LeadIntentValidatorConfig();
            TaskAssignRequest assignRequest = new TaskAssignRequest();
            assignRequest.setTaskInstanceId("TI");
            LeadManagementConfiguration.create(leadCreationConfig, leadUpdationConfig);
            when(taskESRepository.get("TI")).thenReturn(DiscoveryTaskInstance.builder()
                    .actionId("ACTION_ID")
                    .taskDefinitionId("TD1")
                    .taskInstanceMeta(TaskInstanceMeta.builder()
                            .taskMetaList(List.of(TaskMetaInformation.builder()
                                    .type(TaskMetaType.LEAD_INTENT)
                                    .value("TEST_LEAD").build())).build()).build());
            when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                    .taskDefinitionId("TD1")
                    .build())).thenReturn(TaskDefinitionInstance.builder()
                    .definitionAttributes(TaskDefinitionAttributes.builder()
                            .build()).build());
            when(validationService.checkIfDefinitionIsWhitelisted("TD1")).thenReturn(false);
            ValidatorResponse validatorResponse = leadIntentValidator.validate(assignRequest, leadValidatorConfig);
            Assertions.assertNotNull(validatorResponse);
        }
    }



}