package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.merchant.gladius.models.tasks.request.search.LeadViewTaskSearchRequest;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.ArrayList;
import java.util.List;

public class LeadViewTaskSearchRequestQueryBuilderTest extends LegionTaskBaseTest {
    private static LeadViewTaskSearchRequestQueryBuilder leadViewTaskSearchRequestQueryBuilder;

    @BeforeClass
    public static void init() {
        leadViewTaskSearchRequestQueryBuilder = new LeadViewTaskSearchRequestQueryBuilder();
    }

    @Test
    public void testAssignedViewTaskSearchRequestFilterEnricher() {
        LeadViewTaskSearchRequest request = LeadViewTaskSearchRequest.builder().build();
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setAssignedTo("agent");
        request.setNeedScheduledTasks(true);
        Assertions.assertDoesNotThrow(() -> {
            leadViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });
    }

    @Test
    public void testAssignedViewTaskSearchRequestFilterEnricherWithEndDate() {
        LeadViewTaskSearchRequest request = LeadViewTaskSearchRequest.builder().build();
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setAssignedTo("agent");
        request.setNeedScheduledTasks(true);
        request.setEndDate(1633111200000L);
        Assertions.assertDoesNotThrow(() -> {
            leadViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });
    }
}
