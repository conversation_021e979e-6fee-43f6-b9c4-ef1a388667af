package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.survey.request.FeedbackRequest;
import com.phonepe.merchant.gladius.models.survey.response.FeedbackInstance;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFeedback;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.repository.FeedbackRepository;
import com.phonepe.merchant.legion.tasks.services.impl.FeedbackServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.FeedbackTransformationUtils;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FeedbackServiceTest extends LegionTaskBaseTest {

    @BeforeClass
    public static void init() {
        feedbackRepository = mock(FeedbackRepository.class);
        feedbackService = new FeedbackServiceImpl(feedbackRepository, eventExecutor);
    }

    @Before
    public void testSetup() {
        reset(feedbackRepository);
    }

    @Test
    public void testGetFeedback() {
        String formType = "formType";
        String feedbackAssetId = "feedbackAssetId";
        StoredFeedback storedFeedback = StoredFeedback.builder().formType(formType).feedbackAssetId(feedbackAssetId).build();
        FeedbackInstance feedbackInstance = FeedbackTransformationUtils.toFeedbackInstance(storedFeedback);
        when(feedbackRepository.get(formType, feedbackAssetId)).thenReturn(Optional.of(storedFeedback));
        assertEquals(feedbackInstance, feedbackService.getFeedback(formType, feedbackAssetId));
    }

    @Test
    public void testGetFeedback_EmptyOptional() {
        String formType = "formType";
        String feedbackAssetId = "feedbackAssetId";
        when(feedbackRepository.get(formType, feedbackAssetId)).thenReturn(Optional.empty());
        assertNull(feedbackService.getFeedback(formType, feedbackAssetId));
    }

    @Test
    public void testSaveOrUpdateFeedback_SaveCase() {
        String formType = "formType";
        String feedbackAssetId = "feedbackAssetId";
        String feedbackId = "feedbackId";
        String campaignId = "campaignId";
        String actor = "actor";
        FeedbackRequest feedbackRequest = FeedbackRequest.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId(feedbackId)
                .campaignId(campaignId).build();
        when(feedbackRepository.get(formType, feedbackAssetId)).thenReturn(Optional.empty());
        StoredFeedback storedFeedback = StoredFeedback.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId(feedbackId)
                .campaignId(campaignId)
                .createdBy(actor)
                .updatedBy(actor).build();
        FeedbackInstance feedbackInstance = FeedbackTransformationUtils.toFeedbackInstance(storedFeedback);
        when(feedbackRepository.save(storedFeedback)).thenReturn(storedFeedback);
        assertEquals(feedbackInstance, feedbackService.saveOrUpdateFeedback(feedbackRequest, actor));
    }

    @Test
    public void testSaveOrUpdateFeedback_UpdateCase() {
        String formType = "formType";
        String feedbackAssetId = "feedbackAssetId";
        String oldFeedbackId = "feedbackId";
        String oldCampaignId = "campaignId";
        String oldActor = "actor";
        String newFeedbackId = "feedbackId2";
        String newCampaignId = "campaignId2";
        String newActor = "actor2";
        FeedbackRequest feedbackRequest = FeedbackRequest.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId(newFeedbackId)
                .campaignId(newCampaignId).build();
        StoredFeedback oldStoredFeedback = StoredFeedback.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId(oldFeedbackId)
                .campaignId(oldCampaignId)
                .createdBy(oldActor)
                .updatedBy(oldActor).build();
        StoredFeedback newStoredFeedback = StoredFeedback.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId(newFeedbackId)
                .campaignId(newCampaignId)
                .createdBy(newActor)
                .updatedBy(newActor).build();
        when(feedbackRepository.get(formType, feedbackAssetId)).thenReturn(Optional.of(oldStoredFeedback));
        when(feedbackRepository.update(any(), any(), any())).thenReturn(newStoredFeedback);
        assertNotNull(feedbackService.saveOrUpdateFeedback(feedbackRequest, newActor));
    }

    @Test
    public void testSaveOrUpdateFeedback_ExceptionCase() {
        String formType = "formType";
        String feedbackAssetId = "feedbackAssetId";
        String feedbackId = "feedbackId";
        String campaignId = "campaignId";
        String actor = "actor";
        FeedbackRequest feedbackRequest = FeedbackRequest.builder()
                .formType(formType)
                .feedbackAssetId(feedbackAssetId)
                .feedbackId(feedbackId)
                .campaignId(campaignId).build();
        when(feedbackRepository.get(formType, feedbackAssetId)).thenThrow(LegionException.class);
        assertThrows(LegionException.class, () -> feedbackService.saveOrUpdateFeedback(feedbackRequest, actor));
    }

}
