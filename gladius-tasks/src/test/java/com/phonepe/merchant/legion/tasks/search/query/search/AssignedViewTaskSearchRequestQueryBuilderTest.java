package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import javax.xml.crypto.Data;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AssignedViewTaskSearchRequestQueryBuilderTest extends LegionTaskBaseTest {

    private static AssignedViewTaskSearchRequestQueryBuilder assignedViewTaskSearchRequestQueryBuilder;

    @BeforeClass
    public static void init() {
        assignedViewTaskSearchRequestQueryBuilder = new AssignedViewTaskSearchRequestQueryBuilder();
    }

    @Test
    public void testAssignedViewTaskSearchRequestFilterEnricher() {
        AssignedViewTaskFetchRequest request = AssignedViewTaskFetchRequest.builder().build();
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setAssignedTo("agent");
        request.setNeedScheduledTasks(true);
        Assertions.assertDoesNotThrow(() -> {
            assignedViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });
    }

    @Test
    public void testAssignedViewTaskSearchRequestFilterEnricherWithEndDate() {
        AssignedViewTaskFetchRequest request = AssignedViewTaskFetchRequest.builder().build();
        List<Filter> filters = new ArrayList<>();
        request.setFilters(filters);
        request.setAssignedTo("agent");
        request.setNeedScheduledTasks(true);
        request.setEndDate(1633111200000L);
        Assertions.assertDoesNotThrow(() -> {
            assignedViewTaskSearchRequestQueryBuilder.buildQuery("agent", request);
        });
    }

}
