package com.phonepe.merchant.legion.tasks.resources;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.services.TaskFilterService;
import com.phonepe.models.response.GenericResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.Mockito.mock;


@RunWith(MockitoJUnitRunner.class)
public class TaskDiscoveryResourceV2Test extends LegionTaskBaseTest  {
    private static LegionService legionService;
    private static Miscellaneous miscellaneous;
    private static ESRepository esRepository;
    private static TaskFilterService taskFilterService;
    private static SearchResponse searchResponse;
    public static TaskDiscoveryResourceV2 taskDiscoveryResourceV2 = mock(TaskDiscoveryResourceV2.class);
    public static ObjectMapper mapper;
    private static final String AGENT_ID = "AGENT_1";

    @BeforeClass
    public static void init() {

        legionService = mock(LegionService.class);
        searchResponse = mock(SearchResponse.class);
        miscellaneous = mock(Miscellaneous.class);
        esRepository = mock(ESRepository.class);
        taskFilterService = mock(TaskFilterService.class);
        taskDiscoveryResourceV2 = new TaskDiscoveryResourceV2(taskFilterService);
    }

    @Test
    public void getFilterOnTasksDiscoveryView() {
        UserDetails userDetails = getUserDetails();
        DiscoveryLocationTaskFilterRequest discoveryRequest = DiscoveryLocationTaskFilterRequest.builder().build();
        discoveryRequest.setLocation(EsLocationRequest.builder().lat(0.0).lon(0.0).build());
        discoveryRequest.setTaskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW);
        GenericResponse<ViewWiseFilters> actualDiscoveryFilters = taskDiscoveryResourceV2.getFiltersOnTasks(Optional.empty(), userDetails,null,discoveryRequest, null);
        Assert.assertNotNull(actualDiscoveryFilters);

    }

    @Test
    public void getFilterOnTasksAssignedView() {
        UserDetails userDetails = getUserDetails();
        AssignedLocationTaskFilterRequest assignedRequest = AssignedLocationTaskFilterRequest.builder().build();
        assignedRequest.setLocation(EsLocationRequest.builder().lat(0.0).lon(0.0).build());
        assignedRequest.setTaskSearchRequestType(TaskSearchRequestType.ASSIGNED_VIEW);
        GenericResponse<ViewWiseFilters> actualDiscoveryFilters = taskDiscoveryResourceV2.getFiltersOnTasks(Optional.empty(), userDetails,null, assignedRequest, null);
        Assert.assertNotNull(actualDiscoveryFilters);
    }

}
