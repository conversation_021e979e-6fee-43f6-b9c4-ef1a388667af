package com.phonepe.merchant.legion.tasks;

import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.AgentLocationValidatorConfig;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.utils.EsUtil;
import com.phonepe.merchant.legion.tasks.actions.validators.AgentLocationValidator;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class AgentLocationValidatorTest {

    private static FoxtrotEventIngestionService foxtrotEventIngestionService;
    private static ESRepository esRepository;
    private static AgentLocationValidator agentLocationValidator;
    private static final String ERROR_MESSAGE = "You are too far from the location";

    @BeforeClass
    public static void init() {
        foxtrotEventIngestionService = mock(FoxtrotEventIngestionService.class);
        esRepository = mock(ESRepository.class);
        agentLocationValidator = new AgentLocationValidator(foxtrotEventIngestionService, esRepository);
    }

    @Test
    public void test_AgentLocationValidatorToSelfAssign() {
        AgentLocationValidatorConfig agentLocationValidatorConfig = new AgentLocationValidatorConfig();
        EsLocationRequest esLocation = EsLocationRequest.builder().lat(2.01).lon(7.92).build();
        EsLocationRequest actorLocation = EsLocationRequest.builder().lat(12.01).lon(77.92).build();
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder().location(esLocation).build();
        TaskAssignRequest assignRequest = TaskAssignRequest.builder().taskInstanceId("task-instance-id").assignedTo("agent1").build();
        assignRequest.setActorCurrentLocation(actorLocation);
        when(esRepository.get(assignRequest.getTaskInstanceId(), EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(discoveryTaskInstance);
        ValidatorResponse expectedResponse = ValidatorResponse.builder().validated(false)
                .errorCode(LegionTaskErrorCode.DISTANCE_TOO_FAR_TO_ASSIGN)
                .errorMessage(ERROR_MESSAGE)
                .build();
        ValidatorResponse actualResponse = agentLocationValidator.validate(assignRequest, agentLocationValidatorConfig);
        Assertions.assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void test_AgentLocationValidatorToStart() {
        AgentLocationValidatorConfig agentLocationValidatorConfig = new AgentLocationValidatorConfig();
        EsLocationRequest esLocation = EsLocationRequest.builder().lat(2.01).lon(7.92).build();
        EsLocationRequest actorLocation = EsLocationRequest.builder().lat(12.01).lon(77.92).build();
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder().location(esLocation).build();
        TaskStartRequest startRequest = TaskStartRequest.builder().taskInstanceId("task-instance-id").build();
        startRequest.setActorCurrentLocation(actorLocation);
        when(esRepository.get(startRequest.getTaskInstanceId(), EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(discoveryTaskInstance);
        ValidatorResponse expectedResponse = ValidatorResponse.builder().validated(false)
                .errorCode(LegionTaskErrorCode.DISTANCE_TOO_FAR_TO_START)
                .errorMessage(ERROR_MESSAGE)
                .build();
        ValidatorResponse actualResponse = agentLocationValidator.validate(startRequest, agentLocationValidatorConfig);
        Assertions.assertEquals(expectedResponse, actualResponse);
    }




}
