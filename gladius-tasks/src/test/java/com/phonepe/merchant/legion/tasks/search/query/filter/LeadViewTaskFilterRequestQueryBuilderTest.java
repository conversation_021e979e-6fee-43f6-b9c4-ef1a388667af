package com.phonepe.merchant.legion.tasks.search.query.filter;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.GreaterEqualsNumberFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.LesserEqualsNumberFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.filter.LeadViewTaskFilterRequest;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.models.merchants.tasks.EntityType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;

import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.ENTITY_TYPE;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

public class LeadViewTaskFilterRequestQueryBuilderTest extends LegionTaskBaseTest {

    private final long epoch;
    private final LeadViewTaskFilterRequestQueryBuilder filterGenerator;

    public LeadViewTaskFilterRequestQueryBuilderTest() {
        this.epoch = 1234567890L;
        this.filterGenerator
                = new LeadViewTaskFilterRequestQueryBuilder(Clock.fixed(Instant.ofEpochMilli(epoch), ZoneId.systemDefault()));
    }

    @Test
    public void testLeadViewFilters() {
        LeadViewTaskFilterRequest request = LeadViewTaskFilterRequest.builder().build();

        List<Filter> filters = List.of(
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true),
                new LesserEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.START_DATE, epoch),
                new GreaterEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, epoch),
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, "agent"),
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, EntityType.PHONE_NUMBER.name())
        );

        BoolQueryBuilder expected = getBoolQuery(filters);
        QueryBuilder actual = filterGenerator.enrichFilters("agent", request);

        Assertions.assertEquals(expected, actual);
    }
}
