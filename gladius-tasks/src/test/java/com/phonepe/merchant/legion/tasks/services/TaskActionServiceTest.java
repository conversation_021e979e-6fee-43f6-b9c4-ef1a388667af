package com.phonepe.merchant.legion.tasks.services;

import com.flipkart.foxtrot.common.query.Filter;
import com.flipkart.foxtrot.common.query.general.EqualsFilter;
import com.phonepe.merchant.gladius.models.core.FoxtrotRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.request.ActionMetaData;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAttributeCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.validation.*;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.*;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.SysSyncVerificationStrategy;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.*;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionVerificationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.ActionVerificationProcessorImpl;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.CompletionActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.StartActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.*;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ROLES_NOT_ALLOWED_STRING;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getValidatorMap;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getVerifierMap;
import static org.mockito.Mockito.mock;


public class TaskActionServiceTest extends LegionTaskBaseTest {

    private static TaskActionService taskActionService;
    private static ESRepository esRepository = mock(ESRepository.class);
    private static TaskDefinitionService taskDefinitionService = mock(TaskDefinitionService.class);
    private static ExternalEntityService externalEntityService = mock(ExternalEntityService.class);

    @BeforeClass
    public static void init() {
        Map<String, ActionVerifier> verifierMap = getVerifierMap(merchantOnboardingService, foxtrotService, paradoxService, seerService, merchantService, fortunaService, externalEntityService, foxtrotEventIngestionService, hermodService, tmsService, legionService, atlasService, intelService);
        Map<String, ActionValidator> validatorMap = getValidatorMap(foxtrotService, eventIngestionService, brickbatService, esRepository, merchantOnboardingService, externalEntityService, legionService, taskESRepository, taskDefinitionService, leadConfig, geminiService, validationService);
        ActionVerificationProcessor actionVerificationProcessor = new ActionVerificationProcessorImpl(verifierMap, taskTransitionRepository, taskActionService, mock(FoxtrotEventIngestionService.class));
        ActionValidationProcessor startActionValidationProcessor = new StartActionValidationProcessor(validatorMap, taskActionService, foxtrotEventIngestionService);
        ActionValidationProcessor completionActionValidationProcessor = new CompletionActionValidationProcessor(validatorMap, taskActionService, foxtrotEventIngestionService);
        Map cacheConfigs = Map.of(CacheName.AGENT_ACTION, new CacheConfig());

        TaskAttributeCreateRequest taskAttributeCreateRequest = TaskAttributeCreateRequest.builder()
                .attributeType(AttributeType.ROLES_NOT_ALLOWED)
                .attributeValue("TSM")
                .name("TSM")
                .build();
        taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, "saransh");
        taskActionService = new TaskActionServiceImpl(taskActionRepository,
                () -> actionVerificationProcessor,
                startActionValidationProcessor,
                completionActionValidationProcessor,
                cacheUtils, taskAttributeCache, taskActionIdsCache);
    }

    private String saveActionWithVerifiers(EntityType entityType, List<VerificationConfig> verifiers) {

        HashMap map = new HashMap<String, Set<String>>();
        map.put(ROLES_NOT_ALLOWED_STRING, Set.of(AgentType.TSM.toString()));

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(IdGenerator.generate("A").getId())
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .createdBy("jakshat")
                .description("")
                .entityType(entityType)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .attributes(map)
                .build();

        taskActionService.save(taskActionInstance);
        return taskActionInstance.getActionId();
    }


    private String saveActionWithSelfAssignValidators(EntityType entityType, List<VerificationConfig> verifiers, ValidationStrategy validationStrategy) {

        HashMap map = new HashMap<String, Set<String>>();
        map.put(ROLES_NOT_ALLOWED_STRING, Set.of(AgentType.TSM.toString()));;

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(IdGenerator.generate("A").getId())
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .assignTaskValidationStrategy(validationStrategy)
                .createdBy("jakshat")
                .description("")
                .entityType(entityType)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .attributes(map)
                .build();

        taskActionService.save(taskActionInstance);
        return taskActionInstance.getActionId();
    }

    @Test(expected = Test.None.class)
    public void test_actionWithActionMetaData() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());
        List<ValidatorConfig>validatorConfigs = new ArrayList<>();
        ValidatorConfig validatorConfig = new AgentLocationValidatorConfig();
        ValidationContainer validationContainer = new LeafContainer(validatorConfig);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(validationContainer);
        for (EntityType type : EntityType.values()) {
            if (type == EntityType.SECTOR)
                continue;
            String actionId = saveActionWithActionMetaData(type, verificationConfigs, validationStrategy);
            updateValidators(actionId, validatorConfigs);
        }
    }


    private String saveActionWithActionMetaData(EntityType entityType, List<VerificationConfig> verifiers, ValidationStrategy validationStrategy) {

        HashMap map = new HashMap<String, Set<String>>();
        map.put(ROLES_NOT_ALLOWED_STRING, Set.of(AgentType.TSM.toString()));;

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(IdGenerator.generate("A").getId())
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .assignTaskValidationStrategy(validationStrategy)
                .createdBy("jakshat")
                .description("")
                .entityType(entityType)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .attributes(map)
                .metaData(ActionMetaData.builder().actionLevelSelfAssignmentLimit(10).build())
                .build();

        taskActionService.save(taskActionInstance);
        return taskActionInstance.getActionId();
    }

    private void updateValidators(String actionId, List<ValidatorConfig> validators) {
        TaskActionInstance taskActionInstance = taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                .taskActionId(actionId)
                .build());
        if (validators != null && !validators.isEmpty()) {
            List<ValidationContainer> validationContainers = new ArrayList<>();
            validators.forEach(validatorConfig -> validationContainers.add(new LeafContainer(validatorConfig)));
            ValidationContainer validationContainer = new AndOperationContainer(validationContainers);
            ValidationStrategy validationStrategy = new ValidationStrategy();
            validationStrategy.setValidationConfig(validationContainer);
            taskActionInstance.setCompletionValidationStrategy(validationStrategy);
            taskActionInstance.setAssignTaskValidationStrategy(validationStrategy);
            taskActionService.update(taskActionInstance);
        }
    }

    @Test(expected = Test.None.class)
    public void validateVerifiersSuccess() {
        //no action verifier
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());
        for (EntityType type : EntityType.values()) {
            saveActionWithVerifiers(type, verificationConfigs);
        }

        //store check in verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new StoreCheckInVerifierConfig(12_000));
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //oqc validation verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new OqcValidationVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //task qc validation verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new TaskQcVerifierConfig());
        saveActionWithVerifiers(EntityType.TASK, verificationConfigs);

        //merchant kyc verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantKycVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //event existence verifier
        verificationConfigs = new ArrayList<>();
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<SingleEventVerificationConfig> singleEventVerificationConfigs = new ArrayList<>();
        singleEventVerificationConfigs.add(SingleEventVerificationConfig.builder()
                .foxtrotRequest(FoxtrotRequest.builder()
                        .table("legion")
                        .filters(filters)
                        .build())
                .secondsBeforeTaskCompletion(1000l)
                .entityIdFoxtrotColumns(Arrays.asList("merchantIdFieldName", "storeIdFieldName"))
                .build());
        verificationConfigs.add(new EventExistenceVerifierConfig(singleEventVerificationConfigs));
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //store QC verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new StoreQcVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //Brickbat form fill verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BrickbatFormFillVerifierConfig(true));
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //FL Drive verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new FlDriveVerifierConfig(1, 1));
        saveActionWithVerifiers(EntityType.AGENT, verificationConfigs);

        //P2ML migration verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new P2MLmigrationVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //POA verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PoaVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //POB verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PobVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        //Smartspeaker troubleshooting verifier
        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SmartspeakerTroubleshootVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);

        verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantLendingCompletionVerifierConfig());
        saveActionWithVerifiers(EntityType.STORE, verificationConfigs);
    }

    @Test(expected = Test.None.class)
    public void validateValidatorsSuccess() {
        //location validator
        List<ValidatorConfig> validatorConfigs = new ArrayList<>();
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new AgentLocationValidatorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        for (EntityType type : EntityType.values()) {
            if (type == EntityType.SECTOR)
                continue;
            String actionId = saveActionWithVerifiers(type, verificationConfigs);
            updateValidators(actionId, validatorConfigs);
        }

        //brickbat form fill validator
        validatorConfigs = new ArrayList<>();
        verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new BrickbatFormFillValidatorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        for (EntityType type : EntityType.values()) {
            String actionId = saveActionWithVerifiers(type, verificationConfigs);
            updateValidators(actionId, validatorConfigs);
        }

        //Onboard external entity as phonepe mxn validator
        validatorConfigs = new ArrayList<>();
        verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new OnboardExternalEntityAsPhonepeMxnValidatorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        String actionId = saveActionWithVerifiers(EntityType.EXTERNAL, verificationConfigs);
        updateValidators(actionId, validatorConfigs);

        //image qc action validator
        validatorConfigs = new ArrayList<>();
        verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new ImageQcActionValidtorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        String actionId1 = saveActionWithVerifiers(EntityType.AGENT, verificationConfigs);
        updateValidators(actionId1, validatorConfigs);

        //lead intent validator
        validatorConfigs = new ArrayList<>();
        verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new LeadIntentValidatorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        String actionId2 = saveActionWithVerifiers(EntityType.STORE, verificationConfigs);
        updateValidators(actionId2, validatorConfigs);

        //Smart speaker Deployment Validator

        verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new SmartSpeakerDeploymentValidatorConfig());
       verificationConfigs.add(new NoActionVerifierConfig());
        String actionId3 = saveActionWithVerifiers(EntityType.STORE, verificationConfigs);
        updateValidators(actionId3, validatorConfigs);
    }

    @Test(expected = Test.None.class)
    public void test_actionWithSelfAssignValidators() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());
        List<ValidatorConfig>validatorConfigs = new ArrayList<>();
        ValidatorConfig validatorConfig = new AgentLocationValidatorConfig();
        ValidationContainer validationContainer = new LeafContainer(validatorConfig);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(validationContainer);
        for (EntityType type : EntityType.values()) {
            if (type == EntityType.SECTOR)
                continue;
            String actionId = saveActionWithSelfAssignValidators(type, verificationConfigs, validationStrategy);
            updateValidators(actionId, validatorConfigs);
        }
    }

    // Verifier Validations

    @Test(expected = LegionException.class)
    public void storeCheckInVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new StoreCheckInVerifierConfig(12_000));
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void oqcVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new OqcValidationVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void taskQcVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new TaskQcVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void agentDeboardingVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new AgentDeboardingVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void merchantKycVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantKycVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void sbDeploymentVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SoundboxDeploymentVerifierConfig(180000));
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void sbTroubleshootVerificationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SmartspeakerTroubleshootVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void businessCategoryUpdateVerifierFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BusinessCategoryUpdateVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void pobVerifierSuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PobVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void eventExistenceVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<SingleEventVerificationConfig> singleEventVerificationConfigs = new ArrayList<>();
        singleEventVerificationConfigs.add(SingleEventVerificationConfig.builder()
                .foxtrotRequest(FoxtrotRequest.builder()
                        .table("legion")
                        .filters(filters)
                        .build())
                .entityIdFoxtrotColumns(Arrays.asList("merchantIdFieldName", "storeIdFieldName"))
                .secondsBeforeTaskCompletion(1000l)
                .build());
        verificationConfigs.add(new EventExistenceVerifierConfig(singleEventVerificationConfigs));
        saveActionWithVerifiers(EntityType.TASK, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void p2mlVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new P2MLmigrationVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void storeQcVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new StoreQcVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void flDriveVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new FlDriveVerifierConfig(1, 1));
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void poaVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PoaVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test(expected = LegionException.class)
    public void pobVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PobVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    // Validator validations

    @Test(expected = LegionException.class)
    public void agentLocationValidatorValidationFailed() {
        List<ValidatorConfig> validatorConfigs = new ArrayList<>();
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new AgentLocationValidatorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        String actionId = saveActionWithVerifiers(EntityType.SECTOR, verificationConfigs);
        updateValidators(actionId, validatorConfigs);
    }

    @Test(expected = LegionException.class)
    public void onboardExternalEntityAsPhonepeMxnValidatorValidationFailed() {
        List<ValidatorConfig> validatorConfigs = new ArrayList<>();
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new OnboardExternalEntityAsPhonepeMxnValidatorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        String actionId = saveActionWithVerifiers(EntityType.SECTOR, verificationConfigs);
        updateValidators(actionId, validatorConfigs);
    }

    @Test(expected = LegionException.class)
    public void imageQcActionValidatorValidationFailed() {
        List<ValidatorConfig> validatorConfigs = new ArrayList<>();
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        validatorConfigs.add(new ImageQcActionValidtorConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        String actionId = saveActionWithVerifiers(EntityType.SECTOR, verificationConfigs);
        updateValidators(actionId, validatorConfigs);
    }

    @Test(expected = LegionException.class)
    public void dueCollectionVerifierValidationFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SoundboxDeploymentVerifierConfig());
        saveActionWithVerifiers(EntityType.MERCHANT, verificationConfigs);
    }

    @Test
    public void getAllActionsTest() {
        taskActionRepository.save(StoredTaskAction.builder()
                        .actionId("SARANSH_ACTION")
                        .entityType(EntityType.SECTOR)
                        .description("Test")
                        .namespace(Namespace.MERCHANT_ONBOARDING)
                        .createdBy("saransh")
                        .updatedBy("saransh")
                .build());
        List<TaskActionInstance> expectedOutput = taskActionService.getAllActions();
        Assert.assertNotNull(expectedOutput);
    }

    @Test
    public void getAllActionIdsTest() {
        Assert.assertNotNull(taskActionService.getAllActionIds());
    }

}
