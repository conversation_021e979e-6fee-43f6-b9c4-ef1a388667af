package com.phonepe.merchant.legion.tasks.search.query;

import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.location.model.common.responses.LocationBaseResponse;
import com.phonepe.merchant.legion.location.model.common.responses.LocationListResponse;
import com.phonepe.merchant.legion.location.model.geofence.sector.responses.SectorLocationResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


public class SectorGeofenceQueryBuilderTest {

    @Mock
    private LegionService legionService;

    @InjectMocks
    private SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testBuildSectorGeofenceQuery_Success() {
        String sectorId = "sector-1";
        List<List<Double>> geofence = Arrays.asList(
                Arrays.asList(1.0, 2.0),
                Arrays.asList(3.0, 4.0),
                Arrays.asList(5.0, 6.0),
                Arrays.asList(1.0, 2.0)
        );
        SectorLocationResponse location = new SectorLocationResponse();
        location.setGeofence(geofence);

        Map<String, LocationBaseResponse> locations = new HashMap<>();
        locations.put(sectorId, location);

        LocationListResponse response = new LocationListResponse();
        response.setLocations(locations);

        when(legionService.listLocations(anyString(), anyList())).thenReturn(response);

        BoolQueryBuilder query = sectorGeofenceQueryBuilder.buildSectorGeofenceQuery(List.of(sectorId), false);
        assertNotNull(query);
        assertTrue(query.must().size() > 0);
    }

    @Test
    void testBuildSectorGeofenceQuery_NoSectorsFound() {
        LocationListResponse response = new LocationListResponse();
        response.setLocations(Collections.emptyMap());
        when(legionService.listLocations(anyString(), anyList())).thenReturn(response);

        BoolQueryBuilder query = sectorGeofenceQueryBuilder.buildSectorGeofenceQuery(List.of("missing-sector"), false);
        assertNotNull(query);
        assertEquals(QueryBuilders.boolQuery().toString(), query.toString());
    }

    @Test
    void testBuildSectorGeofenceQuery_ThrowsExceptionIfSectorNotFound() {
        LocationListResponse response = new LocationListResponse();
        response.setLocations(Collections.emptyMap());
        when(legionService.listLocations(anyString(), anyList())).thenReturn(response);

        assertThrows(IllegalArgumentException.class, () -> {
            sectorGeofenceQueryBuilder.buildSectorGeofenceQuery(List.of("missing-sector"), true);
        });
    }

    @Test
    void testVisitInFilter() {
        InFilter filter = mock(InFilter.class);
        when(filter.getValues()).thenReturn(List.of("sector-1", "sector-2"));
        when(legionService.listLocations(anyString(), anyList())).thenReturn(new LocationListResponse());
        QueryBuilder query = sectorGeofenceQueryBuilder.visit(filter);
        assertNotNull(query);
        assertTrue(query instanceof BoolQueryBuilder);
    }

    @Test
    void testVisitEqualsFilter() {
        EqualsFilter filter = mock(EqualsFilter.class);
        when(filter.getValue()).thenReturn("sector-1");
        when(legionService.listLocations(anyString(), anyList())).thenReturn(new LocationListResponse());
        QueryBuilder query = sectorGeofenceQueryBuilder.visit(filter);
        assertNotNull(query);
        assertTrue(query instanceof BoolQueryBuilder);
    }
}