package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.HotspotAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.HotspotDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

 class TaskSearchRequestQueryBuilderFactoryTest extends LegionTaskBaseTest {

    private static TaskSearchRequestQueryBuilderFactory taskSearchRequestQueryBuilderFactory;
    private static TaskSearchRequestQueryBuilder<AssignedViewTaskFetchRequest> assignedViewTaskSearchRequestQueryBuilder;
    private static TaskSearchRequestQueryBuilder<DiscoveryViewTaskSearchRequest> discoveryViewTaskSearchRequestQueryBuilder;
    private static TaskSearchRequestQueryBuilder<SectorDiscoveryViewTaskSearchRequest> sectorDiscoveryViewTaskSearchRequestQueryBuilder;
    private static TaskSearchRequestQueryBuilder<EscalatedViewTaskSearchRequest> priorityViewRequestTaskSearchRequestQueryBuilder;
    private static HotspotRequestQueryBuilder hotspotRequestQueryBuilder;
    private static LegionService legionService;

    @BeforeEach
    public void setUpTest() {
        legionService = mock(LegionService.class);
        assignedViewTaskSearchRequestQueryBuilder = mock(AssignedViewTaskSearchRequestQueryBuilder.class);
        discoveryViewTaskSearchRequestQueryBuilder = mock(DiscoveryViewTaskSearchRequestQueryBuilder.class);
        sectorDiscoveryViewTaskSearchRequestQueryBuilder = mock(SectorDiscoveryViewTaskSearchRequestQueryBuilder.class);
        priorityViewRequestTaskSearchRequestQueryBuilder = mock(EscalatedViewTaskSearchRequestQueryBuilder.class);
        hotspotRequestQueryBuilder = mock(HotspotRequestQueryBuilder.class);
        taskSearchRequestQueryBuilderFactory = new TaskSearchRequestQueryBuilderFactory(assignedViewTaskSearchRequestQueryBuilder,
                discoveryViewTaskSearchRequestQueryBuilder, sectorDiscoveryViewTaskSearchRequestQueryBuilder,
                priorityViewRequestTaskSearchRequestQueryBuilder, hotspotRequestQueryBuilder, null);
    }

    @Test
     void testEnrichAssignedView() {
        AssignedViewTaskFetchRequest request = AssignedViewTaskFetchRequest.builder()
                .taskSearchRequestType(TaskSearchRequestType.ASSIGNED_VIEW).filters(new ArrayList<>())
                .pageNo(1).pageSize(15).assignedTo("agent").needScheduledTasks(true).build();

        when(assignedViewTaskSearchRequestQueryBuilder.buildQuery(anyString(), any())).thenReturn(null);
        Assertions.assertNull(taskSearchRequestQueryBuilderFactory.queryBuilder("agent", request));
        verify(assignedViewTaskSearchRequestQueryBuilder).buildQuery("agent", request);
    }

    @Test
     void testEnrichDiscoveryView() {
        DiscoveryViewTaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder()
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW).filters(new ArrayList<>())
                .pageNo(1).pageSize(15).addLocationCheck(false).build();


        when(discoveryViewTaskSearchRequestQueryBuilder.buildQuery(anyString(), any())).thenReturn(null);
        Assertions.assertNull(taskSearchRequestQueryBuilderFactory.queryBuilder("agent", request));
        verify(discoveryViewTaskSearchRequestQueryBuilder).buildQuery("agent", request);
    }


    @Test
     void testEnrichSectorAssignedView() {
        SectorAssignedViewTaskSearchRequest request = SectorAssignedViewTaskSearchRequest.builder()
                .taskSearchRequestType(TaskSearchRequestType.SECTOR_ASSIGNED_VIEW).filters(new ArrayList<>())
                .pageNo(1).pageSize(15).assignedTo("agent").needScheduledTasks(true).build();

        when(assignedViewTaskSearchRequestQueryBuilder.buildQuery(anyString(), any())).thenReturn(null);
        Assertions.assertNull(taskSearchRequestQueryBuilderFactory.queryBuilder("agent", request));
        verify(assignedViewTaskSearchRequestQueryBuilder).buildQuery("agent", request);
    }


    @Test
     void testEnrichSectorDiscoveryView() {

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .taskSearchRequestType(TaskSearchRequestType.SECTOR_DISCOVERY_VIEW).filters(new ArrayList<>())
                .sectorId("sector").pageNo(1).pageSize(15).addLocationCheck(false).build();

        when(sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery(anyString(), any())).thenReturn(null);
        Assertions.assertNull(taskSearchRequestQueryBuilderFactory.queryBuilder("agent", request));
        verify(sectorDiscoveryViewTaskSearchRequestQueryBuilder).buildQuery("agent", request);
    }

     @Test
     void testHotspotAssignedView() {
         HotspotAssignedViewTaskSearchRequest request = HotspotAssignedViewTaskSearchRequest.builder()
                 .taskSearchRequestType(TaskSearchRequestType.HOTSPOT_ASSIGNED_VIEW).filters(new ArrayList<>())
                 .hotspotId("hotspotId").pageNo(1).pageSize(15).build();

         BoolQueryBuilder inner = new BoolQueryBuilder();
         when(assignedViewTaskSearchRequestQueryBuilder.buildQuery(anyString(), any())).thenReturn(inner);
         BoolQueryBuilder outer = new BoolQueryBuilder();
         when(hotspotRequestQueryBuilder.buildQuery(anyString(), anyString())).thenReturn(outer);

         Assertions.assertEquals(outer.must(inner), taskSearchRequestQueryBuilderFactory.queryBuilder("agent", request));
         verify(assignedViewTaskSearchRequestQueryBuilder).buildQuery("agent", request);
         verify(hotspotRequestQueryBuilder).buildQuery("agent","hotspotId");
     }

     @Test
     void testHotspotDiscoveryView() {
         HotspotDiscoveryViewTaskSearchRequest request = HotspotDiscoveryViewTaskSearchRequest.builder()
                 .taskSearchRequestType(TaskSearchRequestType.HOTSPOT_DISCOVERY_VIEW).filters(new ArrayList<>())
                 .hotspotId("hotspotId").pageNo(1).pageSize(15).build();

         BoolQueryBuilder inner = new BoolQueryBuilder();
         when(sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery(anyString(), any())).thenReturn(inner);
         BoolQueryBuilder outer = new BoolQueryBuilder();
         when(hotspotRequestQueryBuilder.buildQuery(anyString(), anyString())).thenReturn(outer);

         Assertions.assertEquals(outer.must(inner), taskSearchRequestQueryBuilderFactory.queryBuilder("agent", request));
         verify(sectorDiscoveryViewTaskSearchRequestQueryBuilder).buildQuery("agent", request);
         verify(hotspotRequestQueryBuilder).buildQuery("agent", "hotspotId");
     }

}
