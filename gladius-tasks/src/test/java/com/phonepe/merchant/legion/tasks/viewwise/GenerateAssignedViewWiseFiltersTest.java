package com.phonepe.merchant.legion.tasks.viewwise;

import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateAssignedViewWiseFilters;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType.ASSIGNED_VIEW;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasks;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasksV2;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;


public class GenerateAssignedViewWiseFiltersTest extends LegionTaskBaseTest {

    private static GenerateAssignedViewWiseFilters generateAssignedViewWiseFilters;

    public GenerateAssignedViewWiseFiltersTest() {
        generateAssignedViewWiseFilters = new GenerateAssignedViewWiseFilters();

    }

    @Test
    public void testAssignedTaskFilterOptions() {

        TaskFilters taskFilters = getFiltersOnTasks();
        ViewWiseFilters expectedAssignedFilters = getFiltersOnTasks().getAssignedFilterOptions();
        expectedAssignedFilters.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedAssignedFilters.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        List<FilterOptions> filterList = new ArrayList<>();
        filterOptions.put(OBJECTIVES, filterList);
        filterOptions.put(POINTS, filterList);
        filterOptions.put(ACTION_ID, filterList) ;
        ViewWiseFilters filters = generateAssignedViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions,ASSIGNED_VIEW );
        Assertions.assertEquals(filters, expectedAssignedFilters);
    }

    @Test
    public void testAssignedTaskFilterOptionsV2() {

        TaskFiltersV2 taskFilters = getFiltersOnTasksV2();
        ViewWiseFiltersV2 expectedAssignedFilters = getFiltersOnTasksV2().getAssignedFilterOptions();
        expectedAssignedFilters.setMaxDate(LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1);
        expectedAssignedFilters.setMinDate(LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis());
        Map<String, List<FilterOptionsV2>> filterOptions = new HashMap<>();
        List<FilterOptionsV2> filterList = new ArrayList<>();
        filterOptions.put(OBJECTIVES, filterList);
        filterOptions.put(POINTS, filterList);
        filterOptions.put(ACTION_ID, filterList) ;
        filterOptions.put(LEAD_INTENT, filterList);
        ViewWiseFiltersV2 filters = generateAssignedViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions,ASSIGNED_VIEW);
        Assertions.assertEquals(filters, expectedAssignedFilters);
    }
}
