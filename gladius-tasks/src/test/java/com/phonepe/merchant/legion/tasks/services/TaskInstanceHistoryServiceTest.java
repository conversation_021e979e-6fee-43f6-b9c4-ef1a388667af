package com.phonepe.merchant.legion.tasks.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.gladius.models.tasks.enums.ChangeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.TaskHistoryRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceAuditResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceHistoryResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.converters.TaskInstanceAuditConverter;
import com.phonepe.merchant.legion.tasks.repository.CommentsOnTaskRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceHistoryServiceImpl;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class TaskInstanceHistoryServiceTest {

    @Mock
    private TaskInstanceRepository taskInstanceRepository;

    @Mock
    private CommentsOnTaskRepository commentsOnTaskRepository;

    @Mock
    private LegionService legionService;

    @Mock
    private TaskInstanceAuditConverter taskInstanceAuditConverter;

    private TaskInstanceHistoryService taskInstanceHistoryService;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        SerDe.init(new ObjectMapper());
        taskInstanceHistoryService = new TaskInstanceHistoryServiceImpl(taskInstanceRepository, commentsOnTaskRepository, taskInstanceAuditConverter, legionService);
    }

    @Test
    void testGetHistoryById() {
        TaskHistoryRequest taskHistoryRequest = new TaskHistoryRequest("test-task-id", 1, 10, Sorter.UPDATED_AT);

        // Mock data
        StoredTaskInstance storedTaskInstance1 = StoredTaskInstance.builder().updatedBy("agent1").build();
        StoredTaskInstance storedTaskInstance2 = StoredTaskInstance.builder().updatedBy("agent2").build();
        Audit<StoredTaskInstance> audit1 = new Audit<>(storedTaskInstance1, "v1", RevisionType.ADD, new Date());
        Audit<StoredTaskInstance> audit2 = new Audit<>(storedTaskInstance2, "v2", RevisionType.MOD, new Date());

        List<Audit<StoredTaskInstance>> audits = Arrays.asList(audit1, audit2);

        StoredCommentsOnTask comment1 = StoredCommentsOnTask.builder().content("comment1").createdBy("agent1").updatedBy("agent1").updatedAt(new Date()).build();
        StoredCommentsOnTask comment2 = StoredCommentsOnTask.builder().content("comment2").createdBy("agent2").updatedBy("agent2").updatedAt(new Date()).build();
        List<StoredCommentsOnTask> comments = Arrays.asList(comment1, comment2);

        AgentProfile agentProfile1 = AgentProfile.builder().name("Jane").agentId("agent1").agentType(AgentType.AGENT).build();

        // Mock behavior
        when(taskInstanceRepository.audit(any())).thenReturn(audits);
        when(commentsOnTaskRepository.getFromTaskInstanceId(anyString(), any(Sorter.class), anyInt(), anyInt())).thenReturn(comments);
        when(legionService.getAgentProfile("agent1")).thenReturn(agentProfile1);
        when(legionService.getAgentProfile("agent2")).thenThrow(LegionException.error(CoreErrorCode.INTERNAL_ERROR));


        List<TaskInstanceAuditResponse> mockAuditResponses = Arrays.asList(
                TaskInstanceAuditResponse.builder().fieldName("field1").changeType(ChangeType.MODIFIED).updatedAt(new Date())
                        .updatedById("agent2")
                        .build()
        );
        when(taskInstanceAuditConverter.convert(any(), any(), any(), any(), any())).thenReturn(mockAuditResponses);

        // Call the method under test
        TaskInstanceHistoryResponse result = taskInstanceHistoryService.getHistoryById(taskHistoryRequest);

        // Verify behavior and results
        assertNotNull(result);
        assertEquals(1, result.getHistory().size());
        verify(taskInstanceRepository).audit("test-task-id");
        verify(commentsOnTaskRepository).getFromTaskInstanceId("test-task-id", Sorter.CREATED_AT, 0, 10000);
        verify(taskInstanceAuditConverter, times(3)).convert(any(), any(), any(), any(), any());
    }

}
