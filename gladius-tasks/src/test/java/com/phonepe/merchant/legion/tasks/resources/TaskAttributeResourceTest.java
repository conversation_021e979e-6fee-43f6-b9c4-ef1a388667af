package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAttributeCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.models.response.GenericResponse;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskAttributeResourceTest {

    private static TaskAttributeService taskAttributeService;
    private static TaskAttributeResource taskAttributeResource;
    public static String ATTRIBUTE = "SARANSH_ATTR";

    @BeforeClass
    public static void init() {
        taskAttributeService = mock(TaskAttributeService.class);
        taskAttributeResource = new TaskAttributeResource(taskAttributeService);
    }

    @Test
    public void createAttributeTest() {

        //arrange
        UserDetails userDetails = getUserDetails();
        TaskAttributeCreateRequest taskAttributeCreateRequest = TaskAttributeCreateRequest.builder().build();
        TaskAttributeInstance expectedResponse = TaskAttributeInstance.builder().build();
        when(taskAttributeService.saveOrUpdate(taskAttributeCreateRequest, userDetails.getExternalReferenceId())).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskAttributeInstance> actualResponse = taskAttributeResource.createOrUpdate(Optional.empty(), taskAttributeCreateRequest, null, userDetails, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(actualResponse.getData(), expectedResponse);
    }

    @Test
    public void getByIdTest() {

        //arrange
        TaskAttributeInstance expectedResponse = TaskAttributeInstance.builder().build();
        when(taskAttributeService.getFromDB(ATTRIBUTE)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskAttributeInstance> actualResponse = taskAttributeResource.getById(Optional.empty(), ATTRIBUTE);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(actualResponse.getData(), expectedResponse);
    }
}
