package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotConfigCreationRequest;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotConfigDto;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.HotspotConfigService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HotspotConfigResourceTest {

    @Mock
    private HotspotConfigService hotspotConfigService;

    @Mock
    private ServiceUserPrincipal serviceUserPrincipal;

    @Mock
    private UserDetails userDetails;

    @Mock
    private AgentProfile agentProfile;

    @InjectMocks
    private HotspotConfigResource hotspotConfigResource;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }



    @Test
    public void testGetHotspotConfig_Success() {
        String type = "testType";
        HotspotConfigDto hotspotConfigDto = mock(HotspotConfigDto.class);
        when(hotspotConfigService.get(type)).thenReturn(hotspotConfigDto);
        GenericResponse<HotspotConfigDto> response = hotspotConfigResource.getHotspotConfig(serviceUserPrincipal, type);
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(hotspotConfigDto, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotConfigService, times(1)).get(type);
    }

    @Test
    public void testGetHotspotConfigNoException_Success() {
        String type = "testType";
        HotspotConfigDto hotspotConfigDto = mock(HotspotConfigDto.class);
        when(hotspotConfigService.get(type)).thenReturn(hotspotConfigDto);
        GenericResponse<HotspotConfigDto> response = hotspotConfigResource.getHotspotConfig(serviceUserPrincipal, type);
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(hotspotConfigDto, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotConfigService, times(1)).get(type);
    }

    @Test
    public void testDeactivateHotspotConfig_Success() {
        String actorId = "testActorId";
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        String type = "testType";
        HotspotConfigDto hotspotConfigDto = HotspotConfigDto.builder().build();
        when(hotspotConfigService.deactivate(anyString(), anyString())).thenReturn(hotspotConfigDto);

        GenericResponse<HotspotConfigDto> response = hotspotConfigResource.deactivateHotspotConfig(
                serviceUserPrincipal, agentProfile, type);

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(hotspotConfigDto, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotConfigService, times(1)).deactivate(actorId, type);
    }

    @Test
    public void testCreateOrUpdateHotspotType_Success() {
        String actorId = "testActorId";
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        when(hotspotConfigService.saveOrUpdate(anyString(), any())).thenReturn(HotspotConfigDto.builder().build());
        GenericResponse<HotspotConfigDto> response = hotspotConfigResource.saveOrUpdateHotspotConfig(
                serviceUserPrincipal, agentProfile,  HotspotConfigCreationRequest.builder().build());

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertTrue(response.isSuccess());
    }

}
