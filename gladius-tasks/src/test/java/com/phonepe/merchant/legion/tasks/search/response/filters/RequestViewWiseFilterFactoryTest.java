package com.phonepe.merchant.legion.tasks.search.response.filters;

import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateAssignedViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateDiscoveryViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateLeadViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.RequestViewWiseFilterFactory;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasks;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getFiltersOnTasksV2;
import static org.mockito.Mockito.mock;

public class RequestViewWiseFilterFactoryTest extends LegionTaskBaseTest {

    private static GenerateAssignedViewWiseFilters generateAssignedViewWiseFilters;
    private static GenerateDiscoveryViewWiseFilters generateDiscoveryViewWiseFilters;
    private static GenerateLeadViewWiseFilters generateLeadViewWiseFilters;
    private static RequestViewWiseFilterFactory requestViewWiseFilterFactory;

    @BeforeClass
    public static void setUpTest() {
        System.out.println("Method called");
        generateAssignedViewWiseFilters = mock(GenerateAssignedViewWiseFilters.class);
        generateDiscoveryViewWiseFilters = mock(GenerateDiscoveryViewWiseFilters.class);
        generateLeadViewWiseFilters = mock(GenerateLeadViewWiseFilters.class);
        requestViewWiseFilterFactory = new RequestViewWiseFilterFactory(generateAssignedViewWiseFilters, generateDiscoveryViewWiseFilters, generateLeadViewWiseFilters);
    }

    @Test
    public void testAssignedView() {
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        TaskFilters taskFilters = getFiltersOnTasks();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, TaskSearchRequestType.ASSIGNED_VIEW);
        });
    }

    @Test
    public void testAssignedViewV2() {
        Map<String, List<FilterOptionsV2>> filterOptions = new HashMap<>();
        TaskFiltersV2 taskFilters = getFiltersOnTasksV2();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilterV2(taskFilters, filterOptions, TaskSearchRequestType.ASSIGNED_VIEW);
        });
    }


    @Test
    public void testDiscoveryView() {
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        TaskFilters taskFilters = getFiltersOnTasks();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, TaskSearchRequestType.DISCOVERY_VIEW);
        });
    }

    @Test
    public void testEscalatedView() {
        Map<String, List<FilterOptionsV2>> filterOptions = new HashMap<>();
        TaskFiltersV2 taskFilters = getFiltersOnTasksV2();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilterV2(taskFilters, filterOptions, TaskSearchRequestType.ESCALATED_VIEW);
        });
    }

    @Test
    public void testHotspotDiscoveryView() {
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        TaskFilters taskFilters = getFiltersOnTasks();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, TaskSearchRequestType.HOTSPOT_DISCOVERY_VIEW);
        });
    }

    @Test
    public void testHotspotAssignedView() {
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        TaskFilters taskFilters = getFiltersOnTasks();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, TaskSearchRequestType.HOTSPOT_ASSIGNED_VIEW);
        });
    }

    @Test
    public void testLeadView() {
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        TaskFilters taskFilters = getFiltersOnTasks();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, TaskSearchRequestType.LEAD_VIEW);
        });
    }

    @Test
    public void testLeadViewV2() {
        Map<String, List<FilterOptionsV2>> filterOptions = new HashMap<>();
        TaskFiltersV2 taskFilters = getFiltersOnTasksV2();
        Assertions.assertDoesNotThrow(()->{
            requestViewWiseFilterFactory.generateViewWiseFilterV2(taskFilters, filterOptions, TaskSearchRequestType.LEAD_VIEW);
        });
    }

}
