package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.survey.storage.StoredFormConfig;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.Optional;

import static com.phonepe.merchant.gladius.models.survey.enums.FormAssetType.STORE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class FormConfigRepositoryTest extends LegionTaskBaseTest {

    private static StoredFormConfig buildStoredFormConfig(String formType) {
        return StoredFormConfig.builder()
                .formType(formType)
                .formName("formName")
                .formAssetType(STORE)
                .campaignId("campaignId")
                .createdAt(new Date())
                .updatedAt(new Date())
                .createdBy("createdBy")
                .updatedBy("updatedBy").build();
    }

    @Test
    public void testSaveAndGet() {
        String formType = "formType";
        StoredFormConfig storedFormConfig = buildStoredFormConfig(formType);
        assertDoesNotThrow(() -> {
            formConfigRepository.save(storedFormConfig);
        });
        Optional<StoredFormConfig> result = formConfigRepository.get(formType);
        assertNotNull(result.get());
    }

    @Test
    public void testUpdate() {
        String formType = "formType2";
        StoredFormConfig storedFormConfig = buildStoredFormConfig(formType);
        StoredFormConfig savedStoredFormConfig = formConfigRepository.save(storedFormConfig);
        assertEquals("campaignId", savedStoredFormConfig.getCampaignId());
        assertDoesNotThrow(() -> {
            formConfigRepository.update(formType, storedFormConfig1 -> {
                storedFormConfig1.setCampaignId("campaignId2");
                return storedFormConfig1;
            });
        });
        Optional<StoredFormConfig> result = formConfigRepository.get(formType);
        assertNotNull(result.get());
        assertEquals("campaignId2", result.get().getCampaignId());
    }

}
