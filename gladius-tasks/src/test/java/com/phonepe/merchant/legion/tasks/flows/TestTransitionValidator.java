package com.phonepe.merchant.legion.tasks.flows;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ActionMetaData;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.EsUtil;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionVerificationProcessor;
import com.phonepe.merchant.legion.tasks.flows.models.ActionsEligibleForSelfAssignTransition;
import com.phonepe.merchant.legion.tasks.flows.models.SelfAssignmentTransitionRules;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.models.merchants.BusinessUnit;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> puri
 */

public class TestTransitionValidator extends LegionTaskBaseTest {

    private static TransitionValidator transitionValidator;
    private static LegionService profileCRUDService;
    private static ESRepository esRepository;
    private static TaskDiscoveryService taskDiscoveryService;
    private static Validations validations;
    private static ChimeraRepositoryImpl chimeraLiteRepository;
    private static AtlasService atlasService;
    private static Miscellaneous miscellaneous;
    private static TaskActionServiceImpl taskActionService;
    private static FoxtrotEventIngestionService foxtrotEventIngestionService;

    private static final String IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY = "is_self_assigned_allowed_config";


    public TestTransitionValidator() {
        profileCRUDService = mock(LegionService.class);
        chimeraLiteRepository = mock(ChimeraRepositoryImpl.class);
        atlasService = mock(AtlasService.class);
        miscellaneous = mock(Miscellaneous.class);
        foxtrotEventIngestionService = mock(FoxtrotEventIngestionService.class);
        taskActionService = mock(TaskActionServiceImpl.class);
        esRepository = mock(ESRepository.class);
        taskDiscoveryService = mock(TaskDiscoveryService.class);
        validations = new Validations(profileCRUDService, atlasService, chimeraLiteRepository, taskActionService, miscellaneous, foxtrotEventIngestionService);
        transitionValidator = new TransitionValidator(profileCRUDService, esRepository,
                mock(FoxtrotEventIngestionService.class),
                mock(ActionValidationProcessor.class), mock(ActionValidationProcessor.class), mock(ActionValidationProcessor.class),
                mock(ActionVerificationProcessor.class), validations);
    }

    @Test(expected = Test.None.class)
    public void validateLocationOfActivitySuccessTest() {
        EsLocationRequest agentLocation = EsLocationRequest.builder()
                .lat(21.8977635)
                .lon(87.2736393)
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID")
                .build();
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build()).build());

        transitionValidator.validateLocationOfActivity(storedTaskInstance, agentLocation);
    }

    @Test(expected = LegionException.class)
    public void validateLocationOfActivityFailureTest() {
        EsLocationRequest agentLocation = EsLocationRequest.builder()
                .lat(28.62386715723713)
                .lon(77.0544863914707)
                .build();

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID")
                .build();
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build()).build());
        transitionValidator.validateLocationOfActivity(storedTaskInstance, agentLocation);
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentSuccess_Failure() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                        .actionId("ACTION_ID")
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID").actionId("action-id")
                .curState(LegionTaskStateMachineState.AVAILABLE).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());
        //call
        doNothing().when(foxtrotEventIngestionService).ingestSelfAssignmentLimitBreached(any(), any(), any(), anyInt(), anyInt());
        when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder().taskActionId("action-id").build()))
                .thenReturn(TaskActionInstance.builder().actionId("action-id")
                        .metaData(ActionMetaData.builder().actionLevelSelfAssignmentLimit(10).build())
                        .build());
        when(esRepository.search(any(), any(), anyInt(), anyInt(), any())).thenReturn(List.of());
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = LegionException.class)
    public void validateSelfAssignmentWithSelfAssignCurrState_Failure() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        taskAssignRequest.setActorCurrentLocation(EsLocationRequest.builder().lat(87.27374658577294).lon(21.897723666497882).build());
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.SELF_ASSIGNED).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        Map<String, SelfAssignmentTransitionRules> actionsEligiblemap = new HashMap<>();
        actionsEligiblemap.put("action-id", SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(true)
                .startedToSelfAssignedAllowed(false)
                .locationValidationRequired(true)
                .build());

        ActionsEligibleForSelfAssignTransition actionsEligibleForSelfAssignConfig = new ActionsEligibleForSelfAssignTransition(actionsEligiblemap);
        doReturn(actionsEligibleForSelfAssignConfig).when(chimeraLiteRepository).getChimeraConfig(IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY, ActionsEligibleForSelfAssignTransition.class);
        Validations validations1 = mock(Validations.class);
        when(validations1.isActionEligibleForSelfAssignment(LegionTaskStateMachineState.SELF_ASSIGNED,
                actionsEligibleForSelfAssignConfig.getEligibleActionsForSelfAssignment().get("action-id"))).thenReturn(true);
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentSuccess() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID").actionId("action-id")
                .curState(LegionTaskStateMachineState.AVAILABLE).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());
        //call
        doNothing().when(foxtrotEventIngestionService).ingestSelfAssignmentLimitBreached(any(), any(), any(), anyInt(), anyInt());
        when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder().taskActionId("action-id").build()))
                .thenReturn(TaskActionInstance.builder().actionId("action-id")
                        .metaData(ActionMetaData.builder().actionLevelSelfAssignmentLimit(0).build())
                        .build());
        Assertions.assertThrows(LegionException.class, () -> transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest));
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentWithSelfAssignCurrState() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        taskAssignRequest.setActorCurrentLocation(EsLocationRequest.builder().lat(87.27374658577294).lon(21.897723666497882).build());
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.SELF_ASSIGNED).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        Map<String, SelfAssignmentTransitionRules> actionsEligiblemap = new HashMap<>();
        actionsEligiblemap.put("action-id", SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(true)
                .startedToSelfAssignedAllowed(false)
                .locationValidationRequired(true)
                .build());

        ActionsEligibleForSelfAssignTransition actionsEligibleForSelfAssignConfig = new ActionsEligibleForSelfAssignTransition(actionsEligiblemap);
        doReturn(actionsEligibleForSelfAssignConfig).when(chimeraLiteRepository).getChimeraConfig(IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY, ActionsEligibleForSelfAssignTransition.class);
        Validations validations1 = mock(Validations.class);
        when(miscellaneous.getMaxAllowedDistanceForSelfAssign()).thenReturn(8000.0);
        when(validations1.isActionEligibleForSelfAssignment(LegionTaskStateMachineState.SELF_ASSIGNED,
                actionsEligibleForSelfAssignConfig.getEligibleActionsForSelfAssignment().get("action-id"))).thenReturn(true);
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentWithSelfAssignCurrState_Success() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        taskAssignRequest.setActorCurrentLocation(EsLocationRequest.builder().lat(87.27374658577294).lon(21.897723666497882).build());
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lat(87.27374658577294)
                        .lon(21.897723666497882)
                        .build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.SELF_ASSIGNED).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        Map<String, SelfAssignmentTransitionRules> actionsEligiblemap = new HashMap<>();
        actionsEligiblemap.put("action-id", SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(true)
                .startedToSelfAssignedAllowed(false)
                .locationValidationRequired(true)
                .build());

        ActionsEligibleForSelfAssignTransition actionsEligibleForSelfAssignConfig = new ActionsEligibleForSelfAssignTransition(actionsEligiblemap);
        doReturn(actionsEligibleForSelfAssignConfig).when(chimeraLiteRepository).getChimeraConfig(IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY, ActionsEligibleForSelfAssignTransition.class);
        Validations validations1 = mock(Validations.class);
        when(validations1.isActionEligibleForSelfAssignment(LegionTaskStateMachineState.SELF_ASSIGNED, actionsEligibleForSelfAssignConfig.getEligibleActionsForSelfAssignment().get("action-id"))).thenReturn(true);
        when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder().taskActionId("action-id").build()))
                .thenReturn(TaskActionInstance.builder().actionId("action-id")
                        .metaData(ActionMetaData.builder().actionLevelSelfAssignmentLimit(10).build())
                        .build());
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentWithSelfAssignCurrStateFailure() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.SELF_ASSIGNED).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        Map<String, SelfAssignmentTransitionRules> actionsEligiblemap = new HashMap<>();
        actionsEligiblemap.put("action-id", SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(false)
                .startedToSelfAssignedAllowed(false)
                .build());

        ActionsEligibleForSelfAssignTransition actionsEligibleForSelfAssignConfig = new ActionsEligibleForSelfAssignTransition(actionsEligiblemap);
        doReturn(actionsEligibleForSelfAssignConfig).when(chimeraLiteRepository).getChimeraConfig(IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY, ActionsEligibleForSelfAssignTransition.class);
        Validations validations1 = mock(Validations.class);
        when(validations1.isActionEligibleForSelfAssignment(LegionTaskStateMachineState.SELF_ASSIGNED,
                actionsEligibleForSelfAssignConfig.getEligibleActionsForSelfAssignment().get("action-id"))).thenReturn(false);
        LegionException thrownException = Assertions.assertThrows(LegionException.class, () -> {
            transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
        });
        Assertions.assertEquals("You are not allowed to self assign this task", thrownException.getMessage());
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentWithStartedCurrStateFailure() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .actionId("action-id")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.STARTED).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        Map<String, SelfAssignmentTransitionRules> actionsEligiblemap = new HashMap<>();
        ActionsEligibleForSelfAssignTransition actionsEligibleForSelfAssignConfig = new ActionsEligibleForSelfAssignTransition(actionsEligiblemap);
        doReturn(actionsEligibleForSelfAssignConfig).when(chimeraLiteRepository).getChimeraConfig(IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY, ActionsEligibleForSelfAssignTransition.class);
        Validations validations1 = mock(Validations.class);
        when(validations1.isActionEligibleForSelfAssignment(LegionTaskStateMachineState.SELF_ASSIGNED,
                actionsEligibleForSelfAssignConfig.getEligibleActionsForSelfAssignment().get("action-id"))).thenReturn(false);
        LegionException thrownException = Assertions.assertThrows(LegionException.class, () -> {
            transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
        });
        Assertions.assertEquals("You are not allowed to self assign this task", thrownException.getMessage());
    }

    @Test(expected = LegionException.class)
    public void validateSelfAssignmentFailureInvalidId() {
        //arrange
        String agentId = "RandomAgent2";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .assignedTo(agentId)
                .taskInstanceId("TID")
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder().businessUnit(BusinessUnit.MASS_RETAIL).build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .tags(Set.of("MASS_RETAIL"))
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build()).build());
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.AVAILABLE).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        //call
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = Test.None.class)
    public void validateSelfAssignmentForSelfServe_Success() {
        //arrange
        String agentId = "RandomAgent1";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .actionId("PB_SELF_SERVE_DEPLOY_PHONEPE_SMARTSPEAKER")
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.AVAILABLE).build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());
        //call
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = LegionException.class)
    public void   validateSelfAssignmentForSelfServe_Failure() {
        //arrange
        String agentId = "RandomAgent1";
        EsLocationRequest esLocationRequest = EsLocationRequest.builder()
                .lon(87.27374658577294)
                .lat(21.897723666497882).build();
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .taskInstanceId("TID")
                .assignedTo(agentId)
                .build();
        taskAssignRequest.setActorCurrentLocation(EsLocationRequest.builder().lat(90.0).lon(90.0).build());
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .sectors(List.of("sector2"))
                .active(true)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .actionId("PB_SELF_SERVE_DEPLOY_PHONEPE_SMARTSPEAKER")
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .location(esLocationRequest)
                .tags(Set.of("MASS_RETAIL"))
                .polygonIds(List.of("sector-1"))
                .build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(esLocationRequest)
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.AVAILABLE).build();
        when(atlasService.getSectorIds(esLocationRequest)).thenReturn(List.of("sector1"));
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());
        //call
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test(expected = LegionException.class)
    public void validateSelfAssignmentFailureInactiveAgent() {
        //arrange
        String agentId = "RandomAgent3";
        TaskAssignRequest taskAssignRequest = TaskAssignRequest.builder()
                .assignedTo(agentId)
                .taskInstanceId("TID")
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("string"))
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .active(false)
                .build());
        when(esRepository.get("TID", EsUtil.TASK_INDEX, DiscoveryTaskInstance.class)).thenReturn(DiscoveryTaskInstance.builder()
                .businessUnit(BusinessUnit.MASS_RETAIL)
                .taskInstanceId("TID")
                .campaign("DEFAULT")
                .location(EsLocationRequest.builder()
                        .lon(87.27374658577294)
                        .lat(21.897723666497882).build())
                .tags(Set.of("MASS_RETAIL")).build());

        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder()
                .pageNo(1)
                .pageSize(15)
                .taskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW)
                .location(EsLocationRequest.builder().build())
                .build();
        when(taskDiscoveryService.search(agentId, request)).thenReturn(TaskSearchResponse.builder()
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID")
                        .build()))
                .build());

        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder().taskInstanceId("TID")
                .curState(LegionTaskStateMachineState.AVAILABLE).build();

        //call
        transitionValidator.validateSelfAssignment(storedTaskInstance, taskAssignRequest);
    }

    @Test
    public void testAcceptSelfAssignedAllowed() {
        SelfAssignmentTransitionRules rules = SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(true)
                .startedToSelfAssignedAllowed(false)
                .build();

        Assertions.assertTrue(rules.accept(LegionTaskStateMachineState.SELF_ASSIGNED));
    }

    @Test
    public void testAcceptStartedAllowed() {
        SelfAssignmentTransitionRules rules = SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(false)
                .startedToSelfAssignedAllowed(true)
                .build();

        Assertions.assertTrue(rules.accept(LegionTaskStateMachineState.STARTED));
    }

    @Test
    public void testAcceptOtherState() {
        SelfAssignmentTransitionRules rules = SelfAssignmentTransitionRules.builder()
                .selfAssignedToSelfAssignedAllowed(true)
                .startedToSelfAssignedAllowed(true)
                .build();

        Assertions.assertFalse(rules.accept(LegionTaskStateMachineState.AVAILABLE));
    }
}
