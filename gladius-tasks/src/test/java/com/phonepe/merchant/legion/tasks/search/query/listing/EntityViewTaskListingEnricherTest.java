package com.phonepe.merchant.legion.tasks.search.query.listing;

import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.AgentTypePerSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.UserRestrictionResponse;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.ViewKillSwitchExecutor;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.tasks.enums.AttributeType.ROLES_NOT_ALLOWED;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class EntityViewTaskListingEnricherTest extends LegionTaskBaseTest {

    private static EntityViewTaskListingEnricher entityViewTaskListingEnricher;
    private static LegionService legionService;


    public EntityViewTaskListingEnricherTest () {
        legionService = mock(LegionService.class);
        entityViewTaskListingEnricher = new EntityViewTaskListingEnricher(legionService, mock(RestrictionQueryBuilder.class), mock(ViewKillSwitchExecutor.class));
    }

    @Test
    public void entityViewWithEmptySectorList_Success() {
        AgentProfile agentProfile = AgentProfile.builder().sectors(new ArrayList<>()).agentType(AgentType.AGENT).attributes(new ArrayList<>()).build();
        Mockito.when(legionService.getAgentProfile(Mockito.anyString())).thenReturn(agentProfile);
        when(legionService.getAllAccessibleSectors(anyString(), anyString())).thenReturn(List.of());
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());
        Assertions.assertNotNull(entityViewTaskListingEnricher.getQuery("agent", new ArrayList<>()));
    }

    @Test
    public void entityViewWithNotEmptySectorList_Success() {
        List<String>sectors = new ArrayList<>();
        sectors.add("sector-id");
        AgentProfile agentProfile = AgentProfile.builder().sectors(sectors).agentId("agent-id").agentType(AgentType.AGENT).attributes(new ArrayList<>()).build();
        when(legionService.getAgentProfile("agent-id")).thenReturn(agentProfile);
        when(legionService.getAllAccessibleSectors(anyString(), anyString())).thenReturn(List.of());
        when(legionService.getAllAccessibleSectorsOfAgent("agent-id")).thenReturn(sectors);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.emptyMap()).build());
        when(legionService.fetchUserRestrictions("agent-id")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());
        AgentProfilesInSectorResponse agentProfilesInSectorResponse = new AgentProfilesInSectorResponse();
        Map<String, List<AgentProfile>> profilesPerSector = new HashMap<>();
        agentProfilesInSectorResponse.setProfilesPerSector(profilesPerSector);
        when(legionService.getAgentProfilesInSector(sectors)).thenReturn(agentProfilesInSectorResponse);
        Assertions.assertNotNull(entityViewTaskListingEnricher.getQuery("agent-id", new ArrayList<>()));
    }

    @Test
    public void entityViewWithNotNullAllowedActions_Success() {
        List<String> sectors = new ArrayList<>();
        sectors.add("sector-id");
        AgentProfile agentProfile = AgentProfile.builder().sectors(sectors).agentId("agent-id").agentType(AgentType.AGENT).attributes(new ArrayList<>()).build();
        when(legionService.getAgentProfile("agent-id")).thenReturn(agentProfile);
        when(legionService.getAllAccessibleSectors(anyString(), anyString())).thenReturn(List.of());
        when(legionService.getAllAccessibleSectorsOfAgent("agent-id")).thenReturn(sectors);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.emptyMap()).build());
        when(legionService.fetchUserRestrictions("agent-id")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of(ROLES_NOT_ALLOWED))
                .allowedActions(List.of("action-id"))
                .build());
        Assertions.assertNotNull(entityViewTaskListingEnricher.getQuery("agent-id", new ArrayList<>()));
    }

    @Test
    public void entityViewWithDdpSectorAgentRoles_Success() {

        List<String> sectors = new ArrayList<>();
        sectors.add("sector-id");
        AgentProfile agentProfile = AgentProfile.builder()
                .sectors(sectors)
                .agentId("agent-id")
                .agentType(AgentType.AGENT)
                .attributes(new ArrayList<>())
                .build();

        UserRestrictionResponse restrictionResponse = UserRestrictionResponse.builder()
                .enabledAttributes(Set.of(ROLES_NOT_ALLOWED))
                .allowedActions(List.of("action-id"))
                .build();

        AgentTypePerSectorResponse agentTypePerSectorResponse = new AgentTypePerSectorResponse();
        Map<String, Set<AgentType>> agentTypesPerSector = new HashMap<>();
        agentTypesPerSector.put("sector-id", Set.of(AgentType.DDP_BM));
        agentTypePerSectorResponse.setAgentTypesPerSector(agentTypesPerSector);

        when(legionService.getAgentProfile("agent-id")).thenReturn(agentProfile);
        when(legionService.getAllAccessibleSectorsOfAgent("agent-id")).thenReturn(sectors);
        when(legionService.fetchUserRestrictions("agent-id")).thenReturn(restrictionResponse);
        when(legionService.getAgentProfilesInSector(any())).thenReturn(AgentProfilesInSectorResponse.builder().profilesPerSector(Collections.emptyMap()).build());
        Assertions.assertNotNull(entityViewTaskListingEnricher.getQuery("agent-id", new ArrayList<>()));
    }

}
