package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClient;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class EscalatedViewTaskSearchRequestQueryBuilderTest {
    EscalatedViewTaskSearchRequestQueryBuilder queryBuilder;

    @Mock
    LegionService legionService;

    @Mock
    ChimeraRepositoryImpl chimeraRepository;

    @Mock
    FilterCraftBuilderCache filterCraftBuilderCache;

    private static final String ESCALATION_VIEW_FILTER_CHIMERA_ENABLED_KEY = "gladius_escalated_view_filter_enabled";
    private static final String ESCALATION_VIEW_FILTER_CHIMERA_CONFIG_KEY = "gladius_escalated_view_filter_config_v2";
    private SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        queryBuilder = new EscalatedViewTaskSearchRequestQueryBuilder(legionService, chimeraRepository, filterCraftBuilderCache);
        sectorGeofenceQueryBuilder = mock(SectorGeofenceQueryBuilder.class);
        TaskEsUtils.init(sectorGeofenceQueryBuilder);
        when(sectorGeofenceQueryBuilder.visit(any(EqualsFilter.class))).thenReturn(QueryBuilders.boolQuery());
        when(sectorGeofenceQueryBuilder.visit(any(InFilter.class))).thenReturn(QueryBuilders.boolQuery());
    }

    @Test
    void testIsEscalationStrategyEnabled() {
        // arrange
        doReturn(true).when(chimeraRepository).isChimeraKeyEnabled(
                ESCALATION_VIEW_FILTER_CHIMERA_ENABLED_KEY);

        // act
        Boolean enabled = queryBuilder.isEscalationStrategyEnabled();

        // assert
        assertNotNull(enabled);
        assertTrue(enabled);
    }

    @Test
    void testIsEscalationStrategyEnabled_disabled() {
        // arrange
        doReturn(false).when(chimeraRepository).isChimeraKeyEnabled(
                ESCALATION_VIEW_FILTER_CHIMERA_ENABLED_KEY);

        // act and assert
        assertFalse(queryBuilder.isEscalationStrategyEnabled());
    }

    @Test
    void testBuildQuery_disabled() {
        // arrange
        doReturn(false).when(chimeraRepository).getChimeraConfig(
                ESCALATION_VIEW_FILTER_CHIMERA_ENABLED_KEY, Boolean.class);

        // act and assert
        assertThrows(LegionException.class, () -> queryBuilder.buildQuery("tsm123", new EscalatedViewTaskSearchRequest()));
    }

    @Test
    void testBuildQuery_noSector() {
        // arrange
        doReturn(Collections.emptyList()).when(legionService).getAllAccessibleSectors(anyString(), anyString());
        doReturn(true).when(chimeraRepository).isChimeraKeyEnabled(
                ESCALATION_VIEW_FILTER_CHIMERA_ENABLED_KEY);

        // act and assert
        assertThrows(IllegalStateException.class, () -> queryBuilder.buildQuery("tsm123", new EscalatedViewTaskSearchRequest()));
    }

    @Test
    void testBuildQuery_oneSector() throws IOException {
        // arrange
        when(legionService.getAllAccessibleSectors(anyString(), anyString())).thenReturn(Collections.singletonList("Sector"));
        doReturn(true).when(chimeraRepository).isChimeraKeyEnabled(
                ESCALATION_VIEW_FILTER_CHIMERA_ENABLED_KEY);
        when(chimeraRepository.getChimeraConfigString(ESCALATION_VIEW_FILTER_CHIMERA_CONFIG_KEY)).thenReturn("JSON");
        FilterCraftClient filterClient = mock(FilterCraftClient.class);
        doReturn(filterClient).when(filterCraftBuilderCache).get(any(FilterCraftBuilderCacheKey.class));
        when(filterClient.convertToESFilter()).thenReturn(mock(BoolQueryBuilder.class));
        EscalatedViewTaskSearchRequest escalatedViewTaskSearchRequest = new EscalatedViewTaskSearchRequest("tsmId", "token");
        List<Filter> filters = new ArrayList<>();
        escalatedViewTaskSearchRequest.setFilters(filters);
        // act and assert
        QueryBuilder queryBuilders = queryBuilder.buildQuery("tsm123", escalatedViewTaskSearchRequest);
        assertNotNull(queryBuilders);

    }

}