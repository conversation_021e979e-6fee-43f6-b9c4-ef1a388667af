package com.phonepe.merchant.legion.tasks.services.impl;

import com.phonepe.merchant.gladius.models.entitystore.StoreEntity;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByTaskTypeRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceExistResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.OdinService;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.flows.Validations;
import com.phonepe.merchant.legion.tasks.flows.models.ActionsEligibleForTransactionLocationTasks;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskTypeFilterQuery;
import com.phonepe.merchant.legion.tasks.services.CommentsService;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchants.odin.models.merchant.CompleteTransactionMetaDataResponse;
import com.phonepe.models.merchants.tasks.EntityType;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchResponseSections;
import com.phonepe.profile.attribute.DoubleAttribute;
import com.phonepe.profile.responses.AttributeReadResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHits;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.AVAILABLE;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.CREATED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.SELF_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.STARTED;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS;
import static com.phonepe.models.merchants.tasks.EntityType.STORE;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class TaskInstanceManagementServiceTest extends LegionTaskBaseTest {
    private static TaskInstanceManagementService taskInstanceManagementService;
    private static EntityStore entityStore = mock(EntityStore .class);
    private static TaskManagementService taskManagementService = mock(TaskManagementService.class);
    private static TaskTypeFilterQuery taskTypeFilterQuery = mock(TaskTypeFilterQuery.class);
    private static CommentsService commentsService = mock(CommentsService.class);
    private static AtlasService atlasService = mock(AtlasService.class);
    private static ChimeraRepositoryImpl chimeraRepository = mock(ChimeraRepositoryImpl.class);
    private static Validations validations = mock(Validations.class);
    private static final String ODIN_NAMESPACE = "odin";
    private static final String ODIN_TRANSACTION_LAT_ATTRIBUTE = "ODIN_TXN_LAT_METADATA";
    private static final String ODIN_TRANSACTION_LONG_ATTRIBUTE = "ODIN_TXN_LONG_METADATA";
    private static final String SELF_SERVE_ACTION_ID = "DEPLOY_PHONEPE_SMARTSPEAKER_PB_SELF_ORDER";
    private static final String SELF_SERVE_DEFINITION_ID = "TD_DEPLOY_PHONEPE_SMARTSPEAKER_PB_SELF_ORDER";
    private static TaskDefinitionService taskDefinitionService = mock(TaskDefinitionService.class);
    private static TaskActionService taskActionService = mock(TaskActionService.class);
    private static ESRepository esRepository = mock(ESRepository.class);

    @BeforeClass
    public static void init() {
        Mockito.reset(atlasService);
        odinService = mock(OdinService.class);
        taskInstanceManagementService =
                new TaskInstanceManagementServiceImpl(entityStore, taskESRepository,
                        taskTransitionRepository, taskInstanceRepository, taskManagementService, eventIngestionService, taskAttributeService, new Miscellaneous(25, 10, 90, 10, 5), eventExecutor,
                        taskTypeFilterQuery, taskDiscoveryService, legionService, odinService, commentsService, validationService, atlasService, chimeraRepository, validations,taskDefinitionService, taskActionService, transitionValidator);
    }


    @Test
    public void completeTaskById(){

        TaskCompletionByIdRequest taskCompletionByIdRequest = TaskCompletionByIdRequest.builder()
                .actorId("Actor")
                .completedBy("Actor")
                .taskInstanceId("TID12346")
                .build();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID12346")
                .secondaryIndexSyncRequired(false)
                .entityId("my_mxn")
                .entityType(EntityType.SECTOR)
                .actionId("actionId")
                .curState(STARTED)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID12346")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID12346")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(SELF_ASSIGNED)
                        .fromState(AVAILABLE)
                        .event(LegionTaskStateMachineEvent.ASSIGNMENT)
                        .taskInstanceId("TID12346")
                        .actor("mohit")
                        .transitionId(3)
                        .taskInstance(storedTaskInstance)
                        .build());

        taskInstanceManagementService.completeTaskById(taskCompletionByIdRequest);
        verify(taskManagementService, Mockito.times(1)).command(any(TaskCompleteRequest.class), anyString());
    }

    @Test
    public void completeTaskById2(){
        TaskCompletionByIdRequest taskCompletionByIdRequest = TaskCompletionByIdRequest.builder()
                .actorId("Actor")
                .completedBy("Actor")
                .taskInstanceId("TID1234")
                .build();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID1234")
                .secondaryIndexSyncRequired(false)
                .entityId("my_mxn")
                .entityType(EntityType.SECTOR)
                .actionId("actionId")
                .curState(STARTED)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID1234")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID1234")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(SELF_ASSIGNED)
                        .fromState(AVAILABLE)
                        .event(LegionTaskStateMachineEvent.ASSIGNMENT)
                        .taskInstanceId("TID1234")
                        .actor("mohit")
                        .transitionId(3)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(STARTED)
                        .fromState(SELF_ASSIGNED)
                        .event(LegionTaskStateMachineEvent.MARK_STARTED)
                        .taskInstanceId("TID1234")
                        .actor("mohit")
                        .transitionId(4)
                        .taskInstance(storedTaskInstance)
                        .build());

        taskInstanceManagementService.completeTaskById(taskCompletionByIdRequest);
        verify(taskManagementService, Mockito.times(2)).command(any(TaskCompleteRequest.class), anyString());
    }

    @Test
    public void completeTaskByIdFailure(){
        TaskCompletionByIdRequest taskCompletionByIdRequest = TaskCompletionByIdRequest.builder()
                .actorId("Actor")
                .completedBy("Actor")
                .taskInstanceId("TID1234123")
                .build();
        Assertions.assertThrows(LegionException.class, ()->taskInstanceManagementService.completeTaskById(taskCompletionByIdRequest));
    }

    @Test
    public void completeAllTaskTest(){
        taskManagementService = mock(TaskManagementService.class);
        when(taskTypeFilterQuery.taskTypeToFilterQuery(any())).thenReturn(new BoolQueryBuilder());
        TaskSearchResponse taskSearchResponse = TaskSearchResponse.builder()
                .taskCount(1)
                .taskList(List.of(TaskMetaResponse.builder()
                        .taskInstanceId("TID123455")
                        .build()))
                .build();
        when(taskDiscoveryService.getAllActiveTasksForCompletion(any(), any())).thenReturn(taskSearchResponse);
        doNothing().when(taskESRepository).syncWithDB(any(), anyBoolean());
        TaskCompletionByTaskTypeRequest manualTaskCompletionRequest = TaskCompletionByTaskTypeRequest.builder()
                .taskType("SS_DEPLOYMENTS")
                .completedBy("Actor")
                .entityId("entityId")
                .build();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID123455")
                .secondaryIndexSyncRequired(false)
                .entityId("my_mxn")
                .entityType(STORE)
                .actionId("actionId")
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID123455")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID123455")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskInstanceManagementService.completeAllTasksByType(manualTaskCompletionRequest);
        Assert.assertTrue(true);
    }

    @Test
    public void test_getById_Success() {
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID1234555")
                .secondaryIndexSyncRequired(false)
                .entityId("my_mxn")
                .entityType(STORE)
                .actionId("actionId")
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(new Date())
                .updatedBy("Mohit")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID1234555")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID1234555")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());
        TaskInstance taskInstance = taskInstanceManagementService.getById("TID1234555");
        Assert.assertNotNull(taskInstance);
        Assert.assertEquals("TID1234555", taskInstance.getTaskInstanceId());
    }


    @Test
    public void createTaskOnTransactionLocation() {

        CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                .points(8)
                .taskDefinitionId("TID12346")
                .entityId("MID_SID")
                .transactionLocation(EsLocationRequest.builder().lat(90.0).lon(90.0).build())
                .createdBy("test")
                .build();

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId("actionId")
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();

        Campaign campaign = Campaign.builder()
                .campaignId("CAMP-01")
                .expiryPeriod(ExpiryPeriod.RELATIVE_DAYS)
                .expiryValue(30)
                .build();

        Set<String> storeTags = new HashSet<>();

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId("TID12346")
                .actionId("actionId")
                .build();

        // Mock external service calls
        when(odinService.getMerchantTransactionData("MID"))
                .thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .competitionTransactionData(List.of())
                        .phonepeTransactionData(List.of())
                        .build());

        when(entityStore.getById(any()))
                .thenReturn(Optional.of(StoreEntity.builder().merchantId("MID").storeId("SID").build()));

        when(chimeraRepository.getChimeraConfig(eq(ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS),
                eq(ActionsEligibleForTransactionLocationTasks.class)))
                .thenReturn(ActionsEligibleForTransactionLocationTasks.builder()
                        .eligibleActionIds(Set.of("actionId"))
                        .build());

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID12346V3")
                .secondaryIndexSyncRequired(false)
                .entityId("MID_SID")
                .entityType(STORE)
                .actionId("actionId")
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID123455V3")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID123455V3")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());


        StoredTaskInstance result = taskInstanceManagementService.create(
                taskDefinitionInstance,
                createTaskInstanceRequest,
                taskActionInstance,
                campaign,
                storeTags
        );

        // Assertions
        Assertions.assertNotNull(result);
    }

    @Test
    public void createTaskOnTransactionLocation_Failure() {

        CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                .points(8)
                .taskDefinitionId("TID12346")
                .entityId("MID_SID")
                .transactionLocation(EsLocationRequest.builder().lat(90.0).lon(90.0).build())
                .createdBy("test")
                .build();

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId("actionId")
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();

        Campaign campaign = Campaign.builder()
                .campaignId("CAMP-01")
                .expiryPeriod(ExpiryPeriod.RELATIVE_DAYS)
                .expiryValue(30)
                .build();

        Set<String> storeTags = new HashSet<>();

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId("TID12346")
                .actionId("actionId")
                .build();

        // Mock external service calls
        when(odinService.getMerchantTransactionData("MID"))
                .thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .competitionTransactionData(List.of())
                        .phonepeTransactionData(List.of())
                        .build());

        when(entityStore.getById(any()))
                .thenReturn(Optional.of(StoreEntity.builder().merchantId("MID").storeId("SID").build()));

        when(chimeraRepository.getChimeraConfig(eq(ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS),
                eq(ActionsEligibleForTransactionLocationTasks.class)))
                .thenReturn(ActionsEligibleForTransactionLocationTasks.builder()
                        .eligibleActionIds(Set.of("actionId"))
                        .build());
        Mockito.doThrow(LegionException.error(CoreErrorCode.NOT_FOUND)).when(chimeraRepository).getChimeraConfig(eq(ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS),
                eq(ActionsEligibleForTransactionLocationTasks.class));

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID12346V2")
                .secondaryIndexSyncRequired(false)
                .entityId("MID_SID")
                .entityType(STORE)
                .actionId("actionId")
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID123455V2")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());

        // Assertions
        Assertions.assertThrows(LegionException.class, () -> taskInstanceManagementService.create(
                taskDefinitionInstance,
                createTaskInstanceRequest,
                taskActionInstance,
                campaign,
                storeTags));

    }

    @Test
    public void taskInstanceExists_ReturnsTrue_WhenTaskExists() {
        String entityId = "ENTITY123";
        String taskDefinitionId = "DEF123";
        String campaignId = "CAMP1";
        String actionId = "ACTION1";

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId(taskDefinitionId)
                .actionId(actionId)
                .build();
        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .entityType(EntityType.PHONE_NUMBER)
                .actionId(actionId)
                .build();
        Campaign campaign = Campaign.builder()
                .campaignId(campaignId)
                .build();

        // Ensure all stubbing is properly completed
        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId).build())).thenReturn(taskDefinitionInstance);
        when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                .taskActionId(actionId).build())).thenReturn(taskActionInstance);
        when(validationService.validateAndGetCampaign(campaignId)).thenReturn(campaign);
        when(transitionValidator.getDuplicateTasks(any(), any(), any(), any())).thenReturn(List.of(DiscoveryTaskInstance.builder().taskInstanceId("taskId1").build()));
        when(transitionValidator.checkIfTaskDeletedInES("taskId1")).thenReturn(false);

        TaskInstanceExistResponse result = taskInstanceManagementService.taskInstanceExists(entityId, taskDefinitionId, campaignId);

        assertTrue(result.isTaskExists());
    }

    @Test
    public void taskInstanceExists_ReturnsFalse_WhenNoTaskExists() {
        String entityId = "ENTITY2";
        String taskDefinitionId = "DEF2";
        String campaignId = "CAMP2";
        String actionId = "ACTION2";

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId(taskDefinitionId)
                .actionId(actionId)
                .build();
        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .entityType(EntityType.PHONE_NUMBER)
                .actionId(actionId)
                .build();
        Campaign campaign = Campaign.builder()
                .campaignId(campaignId)
                .build();

        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId).build())).thenReturn(taskDefinitionInstance);
        when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                .taskActionId(actionId).build())).thenReturn(taskActionInstance);
        when(validationService.validateAndGetCampaign(campaignId)).thenReturn(campaign);
        when(transitionValidator.getDuplicateTasks(any(), any(), any(), any())).thenReturn(Collections.emptyList());

        TaskInstanceExistResponse result = taskInstanceManagementService.taskInstanceExists(entityId, taskDefinitionId, campaignId);

        assertFalse(result.isTaskExists());
    }

    @Test
    public void taskInstanceExists_ThrowsException_WhenDefinitionNotPresent() {
        String entityId = "ENTITY3";
        String taskDefinitionId = "DEF3";
        String campaignId = "CAMP3";

        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId).build())).thenThrow(LegionException.class);

        assertThrows(LegionException.class, () ->
                taskInstanceManagementService.taskInstanceExists(entityId, taskDefinitionId, campaignId));
    }

    @Test
    public void taskInstanceExists_ThrowsException_WhenActionNotPresent() {
        String entityId = "ENTITY4";
        String taskDefinitionId = "DEF4";
        String campaignId = "CAMP4";
        String actionId = "ACTION5";
        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId(taskDefinitionId)
                .actionId(actionId)
                .build();
        when(taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId).build())).thenReturn(taskDefinitionInstance);
        when(taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                .taskActionId(actionId).build())).thenThrow(LegionException.class);

        assertThrows(LegionException.class, () ->
                taskInstanceManagementService.taskInstanceExists(entityId, taskDefinitionId, campaignId));
    }

    @Test
    public void createTaskOnTransactionLocationByFetchingFromPS() {

        CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                .points(8)
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .entityId("MID_SID")
                .createdBy("test")
                .build();

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(SELF_SERVE_ACTION_ID)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();

        Campaign campaign = Campaign.builder()
                .campaignId("CAMP-01")
                .expiryPeriod(ExpiryPeriod.RELATIVE_DAYS)
                .expiryValue(30)
                .build();

        Set<String> storeTags = new HashSet<>();

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .actionId(SELF_SERVE_ACTION_ID)
                .build();

        // Mock external service calls
        when(odinService.getMerchantTransactionData("MID"))
                .thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .competitionTransactionData(List.of())
                        .phonepeTransactionData(List.of())
                        .build());

        when(entityStore.getById(any()))
                .thenReturn(Optional.of(StoreEntity.builder().merchantId("MID").storeId("SID").build()));

        when(chimeraRepository.getChimeraConfig(eq(ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS),
                eq(ActionsEligibleForTransactionLocationTasks.class)))
                .thenReturn(ActionsEligibleForTransactionLocationTasks.builder()
                        .eligibleActionIds(Set.of(SELF_SERVE_ACTION_ID))
                        .build());

        when(validations.isSelfOrderTaskAction(SELF_SERVE_ACTION_ID)).thenReturn(true);
        when(odinService.readProfileStoreAttribute(anyString(), any(), any(), any())).thenReturn(AttributeReadResponse
                .builder()
                .attributes(Set.of(DoubleAttribute.builder().name(ODIN_TRANSACTION_LAT_ATTRIBUTE).value(12.223459).namespace(ODIN_NAMESPACE).build(),
                        DoubleAttribute.builder().name(ODIN_TRANSACTION_LONG_ATTRIBUTE).value(92.789901).build(),
                        DoubleAttribute.builder().name(ODIN_TRANSACTION_LONG_ATTRIBUTE).value(94.789901).build()))
                .build());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID12346V4")
                .secondaryIndexSyncRequired(false)
                .entityId("MID_SID")
                .entityType(STORE)
                .actionId(SELF_SERVE_ACTION_ID)
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID123455V4")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID123455V4")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());


        StoredTaskInstance result = taskInstanceManagementService.create(
                taskDefinitionInstance,
                createTaskInstanceRequest,
                taskActionInstance,
                campaign,
                storeTags
        );

        // Assertions
        Assertions.assertNotNull(result);
    }

    @Test
    public void createTaskOnTransactionLocationByFetchingFromPS_NullTxnLon() {

        CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                .points(8)
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .entityId("MID_SID")
                .createdBy("test")
                .build();

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(SELF_SERVE_ACTION_ID)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();

        Campaign campaign = Campaign.builder()
                .campaignId("CAMP-01")
                .expiryPeriod(ExpiryPeriod.RELATIVE_DAYS)
                .expiryValue(30)
                .build();

        Set<String> storeTags = new HashSet<>();

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .actionId(SELF_SERVE_ACTION_ID)
                .build();

        // Mock external service calls
        when(odinService.getMerchantTransactionData("MID"))
                .thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .competitionTransactionData(List.of())
                        .phonepeTransactionData(List.of())
                        .build());

        when(entityStore.getById(any()))
                .thenReturn(Optional.of(StoreEntity.builder().merchantId("MID").storeId("SID").build()));

        when(chimeraRepository.getChimeraConfig(eq(ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS),
                eq(ActionsEligibleForTransactionLocationTasks.class)))
                .thenReturn(ActionsEligibleForTransactionLocationTasks.builder()
                        .eligibleActionIds(Set.of(SELF_SERVE_ACTION_ID))
                        .build());

        when(validations.isSelfOrderTaskAction(SELF_SERVE_ACTION_ID)).thenReturn(true);
        when(odinService.readProfileStoreAttribute(anyString(), any(), any(), any())).thenReturn(AttributeReadResponse
                .builder()
                .attributes(Set.of(DoubleAttribute.builder().name(ODIN_TRANSACTION_LAT_ATTRIBUTE).value(12.223459).namespace(ODIN_NAMESPACE).build()))
                .build());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID12346V51")
                .secondaryIndexSyncRequired(false)
                .entityId("MID_SID")
                .entityType(STORE)
                .actionId(SELF_SERVE_ACTION_ID)
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID123455V51")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID123455V51")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());


        StoredTaskInstance result = taskInstanceManagementService.create(
                taskDefinitionInstance,
                createTaskInstanceRequest,
                taskActionInstance,
                campaign,
                storeTags
        );

        // Assertions
        Assertions.assertNotNull(result);
    }


    @Test
    public void createTaskOnTransactionLocationByFetchingFromPS_NullResponseFromPS() {

        CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                .points(8)
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .entityId("MID_SID")
                .createdBy("test")
                .build();

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(SELF_SERVE_ACTION_ID)
                .entityType(EntityType.STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();

        Campaign campaign = Campaign.builder()
                .campaignId("CAMP-01")
                .expiryPeriod(ExpiryPeriod.RELATIVE_DAYS)
                .expiryValue(30)
                .build();

        Set<String> storeTags = new HashSet<>();

        TaskDefinitionInstance taskDefinitionInstance = TaskDefinitionInstance.builder()
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .actionId(SELF_SERVE_ACTION_ID)
                .build();

        // Mock external service calls
        when(odinService.getMerchantTransactionData("MID"))
                .thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .competitionTransactionData(List.of())
                        .phonepeTransactionData(List.of())
                        .build());

        when(entityStore.getById(any()))
                .thenReturn(Optional.of(StoreEntity.builder().merchantId("MID").storeId("SID").build()));

        when(chimeraRepository.getChimeraConfig(eq(ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS),
                eq(ActionsEligibleForTransactionLocationTasks.class)))
                .thenReturn(ActionsEligibleForTransactionLocationTasks.builder()
                        .eligibleActionIds(Set.of(SELF_SERVE_ACTION_ID))
                        .build());

        when(validations.isSelfOrderTaskAction(SELF_SERVE_ACTION_ID)).thenReturn(true);
        when(odinService.readProfileStoreAttribute(anyString(), any(), any(), any())).thenReturn(AttributeReadResponse
                .builder()
                .attributes(Set.of())
                .build());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        Date tomorrowDate = calendar.getTime();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TID12346V5")
                .secondaryIndexSyncRequired(false)
                .entityId("MID_SID")
                .entityType(STORE)
                .actionId(SELF_SERVE_ACTION_ID)
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Mohit")
                .dueDate(tomorrowDate)
                .updatedBy("Mohit")
                .taskDefinitionId(SELF_SERVE_DEFINITION_ID)
                .build();
        taskInstanceRepository.save(storedTaskInstance);

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TID123455V5")
                        .actor("mohit")
                        .transitionId(1)
                        .taskInstance(storedTaskInstance)
                        .build());
        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(AVAILABLE)
                        .fromState(CREATED)
                        .event(LegionTaskStateMachineEvent.AVAILABLE)
                        .taskInstanceId("TID123455V5")
                        .actor("mohit")
                        .transitionId(2)
                        .taskInstance(storedTaskInstance)
                        .build());


        StoredTaskInstance result = taskInstanceManagementService.create(
                taskDefinitionInstance,
                createTaskInstanceRequest,
                taskActionInstance,
                campaign,
                storeTags
        );

        // Assertions
        Assertions.assertNotNull(result);
    }


}
