package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.PhoneNumberVerifierConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PhoneNumberVerifierTest {

    private final PhoneNumberVerifier verifier = new PhoneNumberVerifier();

    private final PhoneNumberVerifierConfig config = new PhoneNumberVerifierConfig();

    @Test
    void testValidate_withPhoneNumberEntityType_shouldPass() {
        assertDoesNotThrow(() -> verifier.validate(EntityType.PHONE_NUMBER, config));
    }

    @Test
    void testValidate_withInvalidEntityType_shouldThrow() {
        LegionException exception = assertThrows(LegionException.class,
                () -> verifier.validate(EntityType.STORE, config));

        assertTrue(exception.getMessage().contains("STORE cannot be used with this verifier"));
    }

    @Test
    void testVerify_shouldReturnVerifiedTrueAndPreserveContext() {
        Map<String, Object> context = Map.of("key", "value");
        TaskCompleteRequest request = new TaskCompleteRequest();

        VerifierResponse response = verifier.verify(request, config, context);

        assertTrue(response.getVerified());
        assertEquals("value", response.getContext().get("key"));
    }

    @Test
    void testValidateTaskCreation_withValidPhoneNumber_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setEntityId("9876543210");

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertTrue(result);
    }

    @Test
    void testValidateTaskCreation_withInvalidPhoneNumber_shouldReturnFalse() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setEntityId("12345"); // not 10 digits

        assertThrows(LegionException.class, () -> verifier.validateTaskCreation(request, new TaskActionInstance()));
    }

    @Test
    void testValidateTaskCreation_withNullEntityId_shouldReturnFalse() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setEntityId(null);

        assertThrows(LegionException.class, () -> verifier.validateTaskCreation(request, new TaskActionInstance()));
    }
}
