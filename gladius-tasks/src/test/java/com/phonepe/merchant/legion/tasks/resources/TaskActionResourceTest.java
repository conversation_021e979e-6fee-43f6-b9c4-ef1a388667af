package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.models.response.GenericResponse;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Set;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskActionResourceTest {

    private static TaskActionService taskActionService;
    private static TaskActionResource taskActionResource;

    @BeforeClass
    public static void init() {
        taskActionService = mock(TaskActionService.class);
        taskActionResource = new TaskActionResource(taskActionService);
    }

    @Test
    public void create() {
        //arrange
        UserDetails userDetails = getUserDetails();
        TaskActionInstance request = TaskActionInstance.builder().build();
        TaskActionInstance expectedResponse = TaskActionInstance.builder().build();
        when(taskActionService.save(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskActionInstance> actualResponse = taskActionResource.create(null,request,userDetails, null, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse,actualResponse.getData());

        //arrange
        when(taskActionService.save(request)).thenReturn(null);

        //call
        actualResponse = taskActionResource.create(null,request,userDetails, null, null);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());
    }

    @Test
    public void fetch() {
        //arrange
        String taskActionId = "TASK_ACTION_ID";
        TaskActionFetchByIdRequest request = TaskActionFetchByIdRequest.builder()
                .taskActionId(taskActionId)
                .build();
        TaskActionInstance expectedResponse = TaskActionInstance.builder()
                .actionId(taskActionId)
                .build();
        when(taskActionService.getFromDB(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskActionInstance> actualResponse = taskActionResource.fetch(null,taskActionId);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse,actualResponse.getData());

        //arrange
        when(taskActionService.getFromDB(request)).thenReturn(null);

        //call
        actualResponse = taskActionResource.fetch(null,taskActionId);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());
    }

    @Test
    public void update() {
        //arrange
        UserDetails userDetails = getUserDetails();
        TaskActionInstance request = TaskActionInstance.builder().build();
        TaskActionInstance expectedResponse = TaskActionInstance.builder().build();
        when(taskActionService.update(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskActionInstance> actualResponse = taskActionResource.update(null,request,userDetails, null, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse,actualResponse.getData());

        //arrange
        when(taskActionService.update(request)).thenReturn(null);

        //call
        actualResponse = taskActionResource.update(null,request,userDetails, null, null);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());
    }

    @Test
    public void testGetAllActions() {
        when(taskActionService.getAllActionIds()).thenReturn(Set.of("MXN_KYC"));
        Assert.assertNotNull(taskActionResource.getAllActionIds(java.util.Optional.empty()));
    }
}
