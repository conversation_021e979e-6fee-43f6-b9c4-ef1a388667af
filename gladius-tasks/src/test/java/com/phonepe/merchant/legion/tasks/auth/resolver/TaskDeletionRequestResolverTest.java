package com.phonepe.merchant.legion.tasks.auth.resolver;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionTenants;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsByTypeCache;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.authn.UserType;
import com.phonepe.olympus.im.models.user.SystemUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.ws.rs.container.ContainerRequestContext;
import java.io.ByteArrayInputStream;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskDeletionRequestResolverTest extends LegionTaskBaseTest {
    private static TaskDefinitionCache taskDefinitionCache = mock(TaskDefinitionCache.class);
    @Mock
    private static OlympusIMClient olympusIMClient;
    private static ContainerRequestContext containerRequestContext = mock(ContainerRequestContext.class);
    @Mock
    private static UserAuthDetails userAuthDetails;
    @Mock
    private static SystemUserDetails systemUserDetails;
    @Mock
    private static AccessAllowed accessAllowed;
    private static AccessResolverForClients accessResolver;
    private static TaskInstanceManagementServiceImpl taskInstanceManagementService = mock(TaskInstanceManagementServiceImpl.class);

    @BeforeClass
    public static void setUp() {
        SerDe.init(new ObjectMapper());
        accessResolver = new TaskDeletionRequestResolver(()->olympusIMClient, taskDefinitionCache, taskInstanceManagementService, foxtrotEventIngestionService, mock(TaskDefinitionIdsByTypeCache.class));

        taskDefinitionRepository.save(StoredTaskDefinition.builder()
                .taskDefinitionId("testDefinitionId")
                .actionId("KYC")
                .name("definitionID")
                .createdBy("mohit")
                .updatedBy("mohit")
                .definitionAttributes(SerDe.writeValueAsBytes(TaskDefinitionAttributes.builder()
                        .tenants(TaskDefinitionTenants.builder()
                                .tenants(Set.of("testGroupId"))
                                .build())
                        .build()))
                .points(5)
                .priority(Priority.P0)
                .namespace(Namespace.LEGION)
                .build());
    }


    @Test
    public void testDefinitionIdDerivative_WithDefinitionId() {
        String definitionId = "testDefinitionId";
        String componentInstanceGroupId = "testGroupId";
        ClientTaskDeleteRequest definition = new ClientTaskDeleteRequest();
        definition.setTaskInstanceId("InstanceId");

        when(containerRequestContext.hasEntity()).thenReturn(true);
        when(containerRequestContext.getEntityStream()).thenReturn(new ByteArrayInputStream("{\"taskInstanceId\":\"InstanceId\"}".getBytes()));
        when(olympusIMClient.getUserAuthDetails(containerRequestContext)).thenReturn(userAuthDetails);
        when(userAuthDetails.getUserDetails()).thenReturn(systemUserDetails);
        when(systemUserDetails.getUserType()).thenReturn(UserType.SYSTEM);
        when(systemUserDetails.getComponentInstanceGroupId()).thenReturn(componentInstanceGroupId);
        when(taskInstanceManagementService.getById("InstanceId")).thenReturn(TaskInstance.builder().taskDefinitionId(definitionId).build());
        when(taskDefinitionCache.get(any())).thenReturn(TaskDefinitionInstance.builder()
                .definitionAttributes(TaskDefinitionAttributes.builder()
                        .tenants(TaskDefinitionTenants.builder()
                                .tenants(Set.of(componentInstanceGroupId))
                                .build())
                        .build()).build());
        when(olympusIMClient.verifyPermissions(any(), any(), any())).thenReturn(true);

        Optional<Boolean> result = accessResolver.isAuthorized(containerRequestContext, accessAllowed);

        assertTrue(result.isPresent());
        assertTrue(result.get());
    }
}
