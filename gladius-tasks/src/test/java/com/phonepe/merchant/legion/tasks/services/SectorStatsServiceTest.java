package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.response.SectorStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskStats;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentSectorResponse;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.services.impl.SectorStatsServiceImpl;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.Filters;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.MockedStatic;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.tasks.services.impl.SectorStatsServiceImpl.extractSectorWiseStateCounts;
import static com.phonepe.merchant.legion.tasks.services.impl.SectorStatsServiceImpl.groupTaskStates;
import static com.phonepe.merchant.legion.tasks.services.impl.SectorStatsServiceImpl.mergeCounts;
import static com.phonepe.olympus.im.models.approvals.ApprovalHandlerResult.fail;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class SectorStatsServiceTest extends LegionTaskBaseTest {

    private static LegionService legionService;
    private static ESRepository esRepository;
    private static SectorStatsService sectorStatsService;
    private static SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    @BeforeClass
    public static void init() {
        esRepository = mock(ESRepository.class);
        legionService = mock(LegionService.class);
        sectorGeofenceQueryBuilder = mock(SectorGeofenceQueryBuilder.class);
        sectorStatsService= new SectorStatsServiceImpl(legionService,esRepository, sectorGeofenceQueryBuilder);

    }

    @Test
    public void testGetSectorTaskStats_validSectors() {
        String userId = "user-123";
        String sectorId = "sector-1";

        List<AgentSectorResponse> agentSectors = List.of(
                AgentSectorResponse.builder().sectorId(sectorId).active(true).build()
        );
        when(legionService.getAgentSectors(userId)).thenReturn(agentSectors);

        Aggregations mockAggregations = mock(Aggregations.class);
        when(esRepository.getAggregations(anyString(), any(BoolQueryBuilder.class), any(AggregationBuilder.class), anyInt()))
                .thenReturn(mockAggregations);

        Map<String, Long> taskStats = Map.of(
                "created", 5L,
                "completed", 2L,
                "pending", 3L
        );
        Map<String, Map<String, Long>> sectorWiseStats = Map.of(
                sectorId, taskStats
        );

        try (MockedStatic<SectorStatsServiceImpl> mockedStatic = mockStatic(SectorStatsServiceImpl.class)) {
            mockedStatic.when(() -> extractSectorWiseStateCounts(any()))
                    .thenReturn(sectorWiseStats);

            mockedStatic.when(() -> mergeCounts(anyMap(), anyMap()))
                    .thenAnswer(invocation -> {
                        Map<String, Long> total = invocation.getArgument(0);
                        Map<String, Long> current = invocation.getArgument(1);
                        current.forEach((k, v) -> total.merge(k, v, Long::sum));
                        return null;
                    });

            when(sectorGeofenceQueryBuilder.buildSectorGeofenceQuery(anyList(), anyBoolean()))
                    .thenReturn(mock(BoolQueryBuilder.class));

            // Act
            SectorStatsResponse response = sectorStatsService.getSectorTaskStats(userId);

            // Assert
            assertNotNull(response);
            assertEquals(taskStats, response.getTotalCounts());

            assertEquals(1, response.getSectorWiseStats().size());
            SectorTaskStats sectorStats = response.getSectorWiseStats().get(0);
            assertEquals(sectorId, sectorStats.getSectorId());
            assertEquals(taskStats, sectorStats.getTaskStats());
        }
    }

    @Test
        public void testGetSectorTaskStats_noSectors_shouldThrowLegionException () {
            String userId = "user-123";

            // Mock empty sector list
            when(legionService.getAgentSectors(userId)).thenReturn(Collections.emptyList());

            // Act & Assert
            try {
                sectorStatsService.getSectorTaskStats(userId);
                fail("Expected LegionException to be thrown");
            } catch (LegionException e) {
                assertEquals(CoreErrorCode.NO_SECTORS_MAPPED, e.getErrorCode());
            }
        }

    @Test
    public void testGroupTaskStates() {
        Map<String, Long> rawCounts = new HashMap<>();
        rawCounts.put("CREATED", 5L);
        rawCounts.put("COMPLETED", 3L);
        rawCounts.put("IN_PROGRESS", 2L);

        Map<String, Long> grouped = groupTaskStates(rawCounts);
        Assertions.assertNotNull(grouped);
        Assertions.assertFalse(grouped.isEmpty());
    }

    @Test
    public void testMergeCounts() {
        Map<String, Long> total = new HashMap<>(Map.of("created", 5L));
        Map<String, Long> partial = new HashMap<>(Map.of("created", 3L, "completed", 2L));

        mergeCounts(total, partial);

        Assertions.assertEquals(8L, total.get("created").longValue());
        Assertions.assertEquals(2L, total.get("completed").longValue());

    }

    @Test
    public void testExtractSectorWiseStateCounts() {
        // Mock sector and state buckets
        String sectorId = "sector-1";
        String stateKey = "COMPLETED";
        long docCount = 4L;

        Terms.Bucket stateBucket = mock(Terms.Bucket.class);
        when(stateBucket.getKeyAsString()).thenReturn(stateKey);
        when(stateBucket.getDocCount()).thenReturn(docCount);

        Terms stateAgg = mock(Terms.class);
        when(stateAgg.getBuckets()).thenReturn(Arrays.asList(new Terms.Bucket[]{stateBucket}));

        Filters.Bucket sectorBucket = mock(Filters.Bucket.class);
        when(sectorBucket.getKeyAsString()).thenReturn(sectorId);

        // Mock nested aggregations for the sector bucket
        Aggregations stateAggs = mock(Aggregations.class);
        when(stateAggs.get("stateCounts")).thenReturn(stateAgg);
        when(sectorBucket.getAggregations()).thenReturn(stateAggs);

        Filters sectorAgg = mock(Filters.class);

        when(sectorAgg.getBuckets()).thenReturn(Arrays.asList(new Filters.Bucket[]{sectorBucket}));

        Aggregations aggregations = mock(Aggregations.class);
        when(aggregations.get("sector_wise")).thenReturn(sectorAgg);

        // Call the method under test
        Map<String, Map<String, Long>> result = SectorStatsServiceImpl.extractSectorWiseStateCounts(aggregations);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey(sectorId));
        Map<String, Long> stateCounts = result.get(sectorId);
        assertNotNull(stateCounts);
        // The groupTaskStates method will group "COMPLETED" under "inReview"
        assertEquals(docCount, stateCounts.getOrDefault("inReview", 0L).longValue());
    }

}
