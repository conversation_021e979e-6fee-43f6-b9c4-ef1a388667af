package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.survey.enums.FormAssetType;
import com.phonepe.merchant.gladius.models.survey.request.FormConfigRequest;
import com.phonepe.merchant.gladius.models.survey.response.FeedbackInstance;
import com.phonepe.merchant.gladius.models.survey.response.FormConfigInstance;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFeedback;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFormConfig;
import com.phonepe.merchant.legion.core.FormsDataLoader;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.models.BrickbatFeedbackFlatResponse;
import com.phonepe.merchant.legion.core.models.BrickbatFlatFeedbackRequest;
import com.phonepe.merchant.legion.core.models.BrickbatFormAndFeedbackResponse;
import com.phonepe.merchant.legion.core.models.BrickbatFormsRequestV2;
import com.phonepe.merchant.legion.core.models.BrickbatSubmitFeedbackRequest;
import com.phonepe.merchant.legion.core.models.FormConfig;
import com.phonepe.merchant.legion.core.models.FormMeta;
import com.phonepe.merchant.legion.core.models.FormType;
import com.phonepe.merchant.legion.core.models.StoreData;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.services.impl.BrickbatFormServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.FeedbackTransformationUtils;
import com.phonepe.merchant.legion.tasks.utils.FormConfigTransformationUtils;
import com.phonepe.models.common.Location;
import com.phonepe.models.merchants.BusinessUnit;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.brickbat.models.feedback.StoredSurvey;
import com.phonepe.platform.brickbat.models.question.DateQuestion;
import com.phonepe.platform.brickbat.models.question.EntityRatingQuestion;
import com.phonepe.platform.brickbat.models.question.FeedbackReasonQuestion;
import com.phonepe.platform.brickbat.models.question.MCQGridQuestion;
import com.phonepe.platform.brickbat.models.question.MCQQuestion;
import com.phonepe.platform.brickbat.models.question.Question;
import com.phonepe.platform.brickbat.models.question.RatingQuestion;
import com.phonepe.platform.brickbat.models.question.TextQuestion;
import com.phonepe.platform.brickbat.models.question.questionresponse.DateQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.EntityRatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.FeedbackReasonQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQGridQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.OptionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.QuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.RatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.TextQuestionResponse;
import com.phonepe.platform.brickbat.models.user.request.CreateFeedbackRequestV2;
import com.phonepe.platform.brickbat.models.user.response.CreateFeedbackResponse;
import com.phonepe.platform.brickbat.models.user.response.CreateFeedbackStatus;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BrickbatFormServiceTest extends LegionTaskBaseTest {

    private static BrickbatFormService brickbatFormService;

    @BeforeClass
    public static void init() {
        brickbatFormService = new BrickbatFormServiceImpl(legionService, merchantService, feedbackService,
                formConfigService, brickbatService, eventExecutor, miscellaneous);
        Map<FormType, List<FormMeta>> formConfigs = new HashMap<>();
        FormConfig formConfig = FormConfig.builder().url("formUrl").radius(100.0).locationValidation(true).feedbackAuditConfigured(true).build();
        FormMeta formMeta = FormMeta.builder().formConfig(formConfig).build();
        FormConfig formConfigNoUrl = FormConfig.builder().radius(100.0).locationValidation(true).feedbackAuditConfigured(true).build();
        FormMeta formMetaNoUrl = FormMeta.builder().formConfig(formConfigNoUrl).build();
        List<FormMeta> formMetaList = Collections.singletonList(formMeta);
        List<FormMeta> formMetaNoUrlList = Collections.singletonList(formMetaNoUrl);
        formConfigs.put(FormType.EDC_MERCHANT_FORM_TD, formMetaList);
        formConfigs.put(FormType.NO_COMPETITION, formMetaNoUrlList);
        FormsDataLoader.initialize(formConfigs);
    }

    @Before
    public void testSetup() {
        reset(formConfigService);
        reset(merchantService);
        reset(miscellaneous);
        reset(brickbatService);
        reset(legionService);
        reset(feedbackService);
    }

    @Test
    public void testCreateFormConfig() {
        String formType = "formType";
        String formName = "formName";
        FormAssetType formAssetType = FormAssetType.STORE;
        String campaignId = "campaignId";
        String actor = "actor";
        FormConfigRequest formConfigRequest = FormConfigRequest.builder()
                .formType(formType)
                .formName(formName)
                .formAssetType(formAssetType)
                .campaignId(campaignId).build();
        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType(formType).build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.saveOrUpdateFormConfig(any(), any())).thenReturn(formConfigInstance);
        assertEquals(formConfigInstance, brickbatFormService.createFormConfig(formConfigRequest, actor));
    }

    @Test
    public void testCreateFeedbackAndAudit_Success() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        boolean locationValidation = true;
        String merchantId = "merchantId";
        String storeId = "storeId";
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        CreateFeedbackRequestV2 request = CreateFeedbackRequestV2.builder().userId(agentId).context(new HashMap<>()).build();
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatSubmitFeedbackRequest brickbatSubmitFeedbackRequest = BrickbatSubmitFeedbackRequest.builder()
                .formType(formType)
                .locationValidation(locationValidation)
                .merchant(merchant)
                .clientContext(clientContext)
                .feedbackRequests(Collections.singletonList(request)).build();

        PhysicalStore physicalStore = PhysicalStore.builder().latitude(10.4).longitude(12.5).build();
        when(merchantService.getStoreDetails(any(), any())).thenReturn(physicalStore);
        when(miscellaneous.getFormSubmitLocationRadiusInMeters()).thenReturn(100.0);

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        CreateFeedbackStatus createFeedbackStatus = CreateFeedbackStatus.builder().statusCode(1).message("feedbackId").build();
        Map<String, CreateFeedbackStatus> createFeedbackStatusMap = Map.of("key1", createFeedbackStatus);
        CreateFeedbackResponse createFeedbackResponse = new CreateFeedbackResponse();
        createFeedbackResponse.setCreateFeedbackStatus(createFeedbackStatusMap);
        GenericResponse<CreateFeedbackResponse> genericResponseFromBrickbat = GenericResponse.<CreateFeedbackResponse>builder()
                .success(true)
                .data(createFeedbackResponse).build();
        when(brickbatService.createSurveyBulkForAgentUser(any(), any())).thenReturn(genericResponseFromBrickbat);
        CreateFeedbackResponse result = brickbatFormService.createFeedbackAndAudit(brickbatSubmitFeedbackRequest, agentId);
        assertEquals(createFeedbackResponse, result);
    }

    @Test
    public void testCreateFeedbackAndAudit_LocationParamsInvalid() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        boolean locationValidation = true;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        CreateFeedbackRequestV2 request = CreateFeedbackRequestV2.builder().userId(agentId).context(new HashMap<>()).build();
        BrickbatSubmitFeedbackRequest brickbatSubmitFeedbackRequest = BrickbatSubmitFeedbackRequest.builder()
                .formType(formType)
                .locationValidation(locationValidation)
                .merchant(null)
                .clientContext(clientContext)
                .feedbackRequests(Collections.singletonList(request)).build();

        assertThrows(LegionException.class, () -> brickbatFormService.createFeedbackAndAudit(brickbatSubmitFeedbackRequest, agentId));
    }

    @Test
    public void testCreateFeedbackAndAudit_DistanceTooFar() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        boolean locationValidation = true;
        String merchantId = "merchantId";
        String storeId = "storeId";
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        CreateFeedbackRequestV2 request = CreateFeedbackRequestV2.builder().userId(agentId).context(new HashMap<>()).build();
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatSubmitFeedbackRequest brickbatSubmitFeedbackRequest = BrickbatSubmitFeedbackRequest.builder()
                .formType(formType)
                .locationValidation(locationValidation)
                .merchant(merchant)
                .clientContext(clientContext)
                .feedbackRequests(Collections.singletonList(request)).build();

        PhysicalStore physicalStore = PhysicalStore.builder().latitude(1000.0).longitude(1000.0).build();
        when(merchantService.getStoreDetails(any(), any())).thenReturn(physicalStore);
        when(miscellaneous.getFormSubmitLocationRadiusInMeters()).thenReturn(100.0);

        assertThrows(LegionException.class, () -> brickbatFormService.createFeedbackAndAudit(brickbatSubmitFeedbackRequest, agentId));
    }

    @Test
    public void testCreateFeedbackAndAudit_InvalidAssetOrMerchantStore() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        boolean locationValidation = false;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        CreateFeedbackRequestV2 request = CreateFeedbackRequestV2.builder().userId(agentId).context(new HashMap<>()).build();
        BrickbatSubmitFeedbackRequest brickbatSubmitFeedbackRequest = BrickbatSubmitFeedbackRequest.builder()
                .formType(formType)
                .locationValidation(locationValidation)
                .merchant(null)
                .clientContext(clientContext)
                .feedbackRequests(Collections.singletonList(request)).build();

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        assertThrows(LegionException.class, () -> brickbatFormService.createFeedbackAndAudit(brickbatSubmitFeedbackRequest, agentId));
    }

    @Test
    public void testCreateFeedbackAndAudit_ContextIsNullUserIdNotPresent() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        boolean locationValidation = true;
        String merchantId = "merchantId";
        String storeId = "storeId";
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        CreateFeedbackRequestV2 request = CreateFeedbackRequestV2.builder().build();
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatSubmitFeedbackRequest brickbatSubmitFeedbackRequest = BrickbatSubmitFeedbackRequest.builder()
                .formType(formType)
                .locationValidation(locationValidation)
                .merchant(merchant)
                .clientContext(clientContext)
                .feedbackRequests(Collections.singletonList(request)).build();

        PhysicalStore physicalStore = PhysicalStore.builder().latitude(10.4).longitude(12.5).build();
        when(merchantService.getStoreDetails(any(), any())).thenReturn(physicalStore);
        when(miscellaneous.getFormSubmitLocationRadiusInMeters()).thenReturn(100.0);

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        CreateFeedbackStatus createFeedbackStatus = CreateFeedbackStatus.builder().statusCode(1).message("feedbackId").build();
        Map<String, CreateFeedbackStatus> createFeedbackStatusMap = Map.of("key1", createFeedbackStatus);
        CreateFeedbackResponse createFeedbackResponse = new CreateFeedbackResponse();
        createFeedbackResponse.setCreateFeedbackStatus(createFeedbackStatusMap);
        GenericResponse<CreateFeedbackResponse> genericResponseFromBrickbat = GenericResponse.<CreateFeedbackResponse>builder()
                .success(true)
                .data(createFeedbackResponse).build();
        when(brickbatService.createSurveyBulkForAgentUser(any(), any())).thenReturn(genericResponseFromBrickbat);
        CreateFeedbackResponse result = brickbatFormService.createFeedbackAndAudit(brickbatSubmitFeedbackRequest, agentId);
        assertEquals(createFeedbackResponse, result);
    }

    @Test
    public void testGetFlatMerchantStoreFeedback_Success() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String merchantId = "merchantId";
        String storeId = "storeId";
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest = BrickbatFlatFeedbackRequest.builder()
                .formType(formType)
                .merchant(merchant).build();

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        StoredFeedback storedFeedback = StoredFeedback.builder().formType("EDC_MERCHANT_FORM_TD").feedbackAssetId("merchantId_storeId").build();
        FeedbackInstance feedbackInstance = FeedbackTransformationUtils.toFeedbackInstance(storedFeedback);
        when(feedbackService.getFeedback(any(), any())).thenReturn(feedbackInstance);

        Question q1 = MCQQuestion.builder().questionId("q1").options(Collections.emptyMap()).title("MCQQuestion").build();
        Question q2 = TextQuestion.builder().questionId("q2").title("TextQuestion").build();
        Question q3 = FeedbackReasonQuestion.builder().questionId("q3").defaultTitle("FeedbackReasonQuestion").build();
        Question q4 = EntityRatingQuestion.builder().questionId("q4").title("EntityRatingQuestion").build();
        Question q5 = RatingQuestion.builder().questionId("q5").title("RatingQuestion").build();
        Question q6 = MCQGridQuestion.builder().questionId("q6").title("MCQGridQuestion").build();
        Question q7 = DateQuestion.builder().questionId("q7").title("DateQuestion").build();
        MCQQuestionResponse qr1 = new MCQQuestionResponse();
        OptionResponse optionResponse = new OptionResponse();
        optionResponse.setKey("KEY");
        qr1.setAnswers(Collections.singletonList(optionResponse));
        TextQuestionResponse qr2 = new TextQuestionResponse();
        qr2.setText("text");
        FeedbackReasonQuestionResponse qr3 = new FeedbackReasonQuestionResponse();
        qr3.setFeedbackReasons(Collections.emptyList());
        EntityRatingQuestionResponse qr4 = new EntityRatingQuestionResponse();
        qr4.setRating(0);
        RatingQuestionResponse qr5 = new RatingQuestionResponse();
        qr5.setRating(0);
        MCQGridQuestionResponse qr6 = new MCQGridQuestionResponse();
        qr6.setAnswers(Collections.emptyList());
        DateQuestionResponse qr7 = new DateQuestionResponse();
        qr7.setEpoch(1);
        Map<String, QuestionResponse> map = new HashMap<>();
        map.put("q1", qr1);
        map.put("q2", qr2);
        map.put("q3", qr3);
        map.put("q4", qr4);
        map.put("q5", qr5);
        map.put("q6", qr6);
        map.put("q7", qr7);
        List<Question> questionList = new ArrayList<>();
        questionList.add(q1);
        questionList.add(q2);
        questionList.add(q3);
        questionList.add(q4);
        questionList.add(q5);
        questionList.add(q6);
        questionList.add(q7);
        StoredSurvey storedSurvey = new StoredSurvey();
        storedSurvey.setUserResponses(map);
        GenericResponse<com.phonepe.platform.brickbat.models.feedback.StoredFeedback> genericResponseFromBrickbat =
                GenericResponse.<com.phonepe.platform.brickbat.models.feedback.StoredFeedback>builder()
                .success(true)
                .data(storedSurvey).build();
        GenericResponse<List<Question>> genericResponseOfQuestionsFromBrickbat = GenericResponse.<List<Question>>builder()
                        .success(true)
                        .data(questionList).build();
        when(brickbatService.getSurveyResult(any())).thenReturn(genericResponseFromBrickbat);
        when(brickbatService.getQuestionsFromQuestionIds(any())).thenReturn(genericResponseOfQuestionsFromBrickbat);

        BrickbatFeedbackFlatResponse result = brickbatFormService.getFlatMerchantStoreFeedback(brickbatFlatFeedbackRequest);
        assertNotNull(result.getFeedbackQuestionResponses());
        assertNotNull(result.getFeedbackQuestionResponses().get(0));
    }

    @Test
    public void testGetFlatMerchantStoreFeedback_NullFormType() {
        String merchantId = "merchantId";
        String storeId = "storeId";
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest = BrickbatFlatFeedbackRequest.builder()
                .formType(null)
                .merchant(merchant).build();

        assertThrows(LegionException.class, () -> brickbatFormService.getFlatMerchantStoreFeedback(brickbatFlatFeedbackRequest));
    }

    @Test
    public void testGetFlatMerchantStoreFeedback_UnableToRetrieveDataFromBrickbat() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String merchantId = "merchantId";
        String storeId = "storeId";
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest = BrickbatFlatFeedbackRequest.builder()
                .formType(formType)
                .merchant(merchant).build();

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        StoredFeedback storedFeedback = StoredFeedback.builder().formType("EDC_MERCHANT_FORM_TD").feedbackAssetId("merchantId_storeId").build();
        FeedbackInstance feedbackInstance = FeedbackTransformationUtils.toFeedbackInstance(storedFeedback);
        when(feedbackService.getFeedback(any(), any())).thenReturn(feedbackInstance);

        GenericResponse<com.phonepe.platform.brickbat.models.feedback.StoredFeedback> genericResponseFromBrickbat =
                GenericResponse.<com.phonepe.platform.brickbat.models.feedback.StoredFeedback>builder()
                        .success(true)
                        .data(null).build();
        when(brickbatService.getSurveyResult(any())).thenReturn(genericResponseFromBrickbat);

        assertThrows(LegionException.class, () -> brickbatFormService.getFlatMerchantStoreFeedback(brickbatFlatFeedbackRequest));
    }

    @Test
    public void testGetFlatMerchantStoreFeedback_WithFormConfigNull() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String assetId = "assetId";
        BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest = BrickbatFlatFeedbackRequest.builder()
                .formType(formType)
                .assetId(assetId)
                .build();
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(null);

        BrickbatFeedbackFlatResponse response = brickbatFormService.getFlatMerchantStoreFeedback(brickbatFlatFeedbackRequest);

        assertEquals(false, response.isFeedbackAuditConfigured());
    }

    @Test
    public void testGetFlatMerchantStoreFeedback_NotAssetProvided_ThrowException() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest = BrickbatFlatFeedbackRequest.builder()
                .formType(formType)
                .build();

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);


        assertThrows(LegionException.class, () -> brickbatFormService.getFlatMerchantStoreFeedback(brickbatFlatFeedbackRequest));
    }

    @Test
    public void testGetFlatMerchantStoreFeedback_WithFeedbackInstanceNull() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String assetId = "assetId";
        BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest = BrickbatFlatFeedbackRequest.builder()
                .formType(formType)
                .assetId(assetId)
                .build();
        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);
        when(feedbackService.getFeedback(any(), any())).thenReturn(null);

        BrickbatFeedbackFlatResponse response = brickbatFormService.getFlatMerchantStoreFeedback(brickbatFlatFeedbackRequest);

        assertEquals(true, response.isFeedbackAuditConfigured());
        assertEquals(false, response.isExistingFeedbackPresent());
    }

    @Test
    public void testGetBrickbatFormAndFeedback_Success() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String merchantId = "merchantId";
        String storeId = "storeId";
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).merchant(merchant).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();

        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(merchantService.getMerchantFactDetails(any())).thenReturn(MerchantProfile.builder().businessUnit(BusinessUnit.NKA).build());

        PhysicalStore physicalStore = PhysicalStore.builder().latitude(10.4).longitude(12.5).build();
        when(merchantService.getStoreDetails(any(), any())).thenReturn(physicalStore);

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        StoredFeedback storedFeedback = StoredFeedback.builder().formType("EDC_MERCHANT_FORM_TD").feedbackAssetId("merchantId_storeId").build();
        FeedbackInstance feedbackInstance = FeedbackTransformationUtils.toFeedbackInstance(storedFeedback);
        when(feedbackService.getFeedback(any(), any())).thenReturn(feedbackInstance);

        MCQQuestionResponse qr1 = new MCQQuestionResponse();
        OptionResponse optionResponse = new OptionResponse();
        optionResponse.setKey("KEY");
        qr1.setAnswers(Collections.singletonList(optionResponse));
        Map<String, QuestionResponse> map = new HashMap<>();
        map.put("q1", qr1);
        StoredSurvey storedSurvey = new StoredSurvey();
        storedSurvey.setUserResponses(map);
        GenericResponse<com.phonepe.platform.brickbat.models.feedback.StoredFeedback> genericResponseFromBrickbat =
                GenericResponse.<com.phonepe.platform.brickbat.models.feedback.StoredFeedback>builder()
                        .success(true)
                        .data(storedSurvey).build();
        when(brickbatService.getSurveyResult(any())).thenReturn(genericResponseFromBrickbat);

        BrickbatFormAndFeedbackResponse result = brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location);
        assertNotNull(result.getFeedbackUserResponse());
        assertNotNull(result.getFeedbackUserResponse().get(0));
    }

    @Test
    public void testGetBrickbatFormAndFeedback_InvalidAgentProfile() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).merchant(null).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();
        when(legionService.getAgentProfile(any())).thenReturn(null);

        assertThrows(LegionException.class, () -> brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location));
    }

    @Test
    public void testGetBrickbatFormAndFeedback_FormConfigNotPresentInConfigurationFile() {
        FormType formType = FormType.NOC;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).merchant(null).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();
        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(merchantService.getMerchantFactDetails(any())).thenReturn(MerchantProfile.builder().businessUnit(BusinessUnit.NKA).build());

        assertThrows(LegionException.class, () -> brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location));
    }

    @Test
    public void testGetBrickbatFormAndFeedback_FormUrlNotPresentInConfigurationFile() {
        FormType formType = FormType.NO_COMPETITION;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).merchant(null).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();

        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(merchantService.getMerchantFactDetails(any())).thenReturn(MerchantProfile.builder().businessUnit(BusinessUnit.NKA).build());

        assertThrows(LegionException.class, () -> brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location));
    }

    @Test
    public void testGetBrickbatFormAndFeedback_LocationParamsInvalid() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        String merchantId = "merchantId";
        String storeId = "storeId";
        StoreData merchant = StoreData.builder().merchantId(merchantId).storeId(storeId).build();
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).merchant(merchant).clientContext(clientContext).build();

        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(merchantService.getMerchantFactDetails(any())).thenReturn(MerchantProfile.builder().businessUnit(BusinessUnit.NKA).build());

        assertThrows(LegionException.class, () -> brickbatFormService.getBrickbatFormAndFeedback(request, agentId, null));
    }

    @Test
    public void testGetBrickbatFormAndFeedback_InvalidAssetOrMerchantStore() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).merchant(null).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();

        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(merchantService.getMerchantFactDetails(any())).thenReturn(MerchantProfile.builder().businessUnit(BusinessUnit.NKA).build());

        PhysicalStore physicalStore = PhysicalStore.builder().latitude(10.4).longitude(12.5).build();
        when(merchantService.getStoreDetails(any(), any())).thenReturn(physicalStore);

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        assertThrows(LegionException.class, () -> brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location));
    }

    @Test
    public void testGetBrickbatFormAndFeedbackWithNoInstance_Success() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String assetId = "assetId";
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).assetId(assetId).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();

        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(merchantService.getMerchantFactDetails(any())).thenReturn(MerchantProfile.builder().businessUnit(BusinessUnit.NKA).build());

        PhysicalStore physicalStore = PhysicalStore.builder().latitude(10.4).longitude(12.5).build();
        when(merchantService.getStoreDetails(any(), any())).thenReturn(physicalStore);

        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType("EDC_MERCHANT_FORM_TD").campaignId("campaignId").build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(formConfigInstance);

        when(feedbackService.getFeedback(any(), any())).thenReturn(null);

        BrickbatFormAndFeedbackResponse result = brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location);
        assertFalse(result.isExistingFeedbackPresent());
        assertTrue(result.isFeedbackAuditConfigured());
    }

    @Test
    public void testGetBrickbatFormAndFeedbackWithNoFormExist_Success() {
        FormType formType = FormType.EDC_MERCHANT_FORM_TD;
        String assetId = "assetId";
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        String agentId = "agentId";
        BrickbatFormsRequestV2 request = BrickbatFormsRequestV2.builder().formType(formType).assetId(assetId).clientContext(clientContext).build();
        Location location = Location.builder().latitude(10.4).longitude(12.5).build();
        when(legionService.getAgentProfile(any())).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        when(formConfigService.getFormConfigFromCache(any())).thenReturn(null);
        BrickbatFormAndFeedbackResponse result = brickbatFormService.getBrickbatFormAndFeedback(request, agentId, location);
        assertFalse(result.isFeedbackAuditConfigured());
    }

}
