package com.phonepe.merchant.legion.tasks.search.query.listing;

import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.UserRestrictionResponse;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.ViewKillSwitchExecutor;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Set;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class AgentTaskEligibilityEnricherTest {

    private static AgentTaskEligibilityEnricher agentTaskEligibilityEnricher;
    private final LegionService legionService;

    public AgentTaskEligibilityEnricherTest() {
        legionService = mock(LegionService.class);
        agentTaskEligibilityEnricher = new AgentTaskEligibilityEnricher(legionService,  mock(RestrictionQueryBuilder.class), mock(ViewKillSwitchExecutor.class));
    }

    @Test
    public void testAllTaskListingEnricher() {
        // Set up the mock responses
        AgentProfile agentProfile = AgentProfile.builder()
                .sectors(new ArrayList<>())
                .agentType(AgentType.AGENT)
                .attributes(new ArrayList<>())
                .build();

        Mockito.when(legionService.getAgentProfile(Mockito.anyString())).thenReturn(agentProfile);
        when(legionService.fetchUserRestrictions("agent")).thenReturn(UserRestrictionResponse.builder()
                .enabledAttributes(Set.of())
                .build());
        BoolQueryBuilder query = agentTaskEligibilityEnricher.getQuery("agent", new ArrayList<>());
        Assertions.assertNotNull(query);
        Mockito.verify(legionService).getAgentProfile(Mockito.eq("agent"));
        Mockito.verify(legionService).fetchUserRestrictions(Mockito.eq("agent"));
    }




}
