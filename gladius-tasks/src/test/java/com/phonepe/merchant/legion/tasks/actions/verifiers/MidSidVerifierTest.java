package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.MidSidVerifierConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_STORE_MAPPED;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class MidSidVerifierTest {

    private MerchantService merchantService;
    private MidSidFromTaskMetaVerifier verifier;
    private MidSidVerifierConfig config;

    @BeforeEach
    void setUp() {
        merchantService = Mockito.mock(MerchantService.class);
        verifier = new MidSidFromTaskMetaVerifier(merchantService);
        config = new MidSidVerifierConfig();
    }

    @Test
    void validate_withPhoneNumber_shouldNotThrow() {
        assertDoesNotThrow(() ->
                verifier.validate(EntityType.PHONE_NUMBER, config));
    }

    @Test
    void validate_withInvalidEntityType_shouldThrow() {
        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validate(EntityType.STORE, config));
        assertEquals(INVALID_TASK_VERIFIER, ex.getErrorCode());
    }

    @Test
    void verify_shouldReturnVerifiedTrueWithContext() {
        Map<String, Object> context = Map.of("key", "value");

        VerifierResponse response = verifier.verify(
                new TaskCompleteRequest(), config, context);

        assertTrue(response.getVerified());
        assertEquals(context, response.getContext());
    }

    @Test
    void validateTaskCreation_withMissingMeta_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setTaskInstanceMeta(TaskInstanceMeta.builder().taskMetaList(null).build());

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }

    @Test
    void validateTaskCreation_withValidMerchantAndStore_shouldSetLocation() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        TaskMetaInformation mid = TaskMetaInformation.builder()
                .type(TaskMetaType.MID)
                .value("M123")
                .build();
        TaskMetaInformation sid = TaskMetaInformation.builder()
                .type(TaskMetaType.SID)
                .value("S456")
                .build();
        request.setTaskInstanceMeta(TaskInstanceMeta.builder()
                .taskMetaList(List.of(mid, sid))
                .build());

        PhysicalStore store = new PhysicalStore();
        store.setLatitude(12.34d);
        store.setLongitude(56.78d);

        when(merchantService.getStoreDetails("M123", "S456")).thenReturn(store);

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertTrue(result);
        EsLocationRequest location = request.getTransactionLocation();
        assertNotNull(location);
        assertEquals(12.34d, location.getLat());
        assertEquals(56.78d, location.getLon());

        verify(merchantService, times(1)).getStoreDetails("M123", "S456");
    }

    @Test
    void validateTaskCreation_whenStoreDoesNotExist_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        TaskMetaInformation mid = TaskMetaInformation.builder()
                .type(TaskMetaType.MID)
                .value("M123")
                .build();
        TaskMetaInformation sid = TaskMetaInformation.builder()
                .type(TaskMetaType.SID)
                .value("S456")
                .build();
        request.setTaskInstanceMeta(TaskInstanceMeta.builder()
                .taskMetaList(List.of(mid, sid))
                .build());

        when(merchantService.getStoreDetails("M123", "S456"))
                .thenThrow(new RuntimeException("Not found"));

        LegionException ex = assertThrows(LegionException.class, () ->
                verifier.validateTaskCreation(request, new TaskActionInstance()));
        assertEquals(INVALID_STORE_MAPPED, ex.getErrorCode());
    }
}
