package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.TaskInstanceVerifierConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

public class TaskInstanceVerifierTest {

    private TaskInstanceRepository taskInstanceRepository;
    private TaskInstanceVerifier verifier;
    private TaskInstanceVerifierConfig config;

    @BeforeEach
    void setUp() {
        taskInstanceRepository = Mockito.mock(TaskInstanceRepository.class);
        verifier = new TaskInstanceVerifier(taskInstanceRepository);
        config = new TaskInstanceVerifierConfig();
    }


    @Test
    void testValidate_withPhoneNumberEntityType_shouldPass() {
        assertDoesNotThrow(() -> verifier.validate(EntityType.PHONE_NUMBER, config));
    }

    @Test
    void testValidate_withInvalidEntityType_shouldThrow() {
        LegionException exception = assertThrows(LegionException.class,
                () -> verifier.validate(EntityType.STORE, config));

        assertTrue(exception.getMessage().contains("STORE cannot be used with this verifier"));
    }


    @Test
    void testVerify_shouldReturnVerifiedTrueAndPreserveContext() {
        Map<String, Object> context = Map.of("foo", "bar");
        TaskCompleteRequest request = new TaskCompleteRequest();

        VerifierResponse response = verifier.verify(request, config, context);

        assertTrue(response.getVerified());
        assertEquals("bar", response.getContext().get("foo"));
    }


    @Test
    void testValidateTaskCreation_withNullMeta_shouldReturnFalse() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setTaskInstanceMeta(null);

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }

    @Test
    void testValidateTaskCreation_withEmptyMeta_shouldReturnFalse() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setTaskInstanceMeta(new TaskInstanceMeta(0, Collections.emptyList(), Collections.emptyMap()));

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }

    @Test
    void testValidateTaskCreation_withBlankTaskInstanceId_shouldReturnFalse() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();

        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.TASK_INSTANCE_ID)
                .value("   ") // blank id
                .build();

        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }

    @Test
    void testValidateTaskCreation_withNonExistingTaskInstance_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();

        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.TASK_INSTANCE_ID)
                .value("task-123")
                .build();

        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        when(taskInstanceRepository.get("task-123")).thenReturn(Optional.empty());

        LegionException ex = assertThrows(LegionException.class,
                () -> verifier.validateTaskCreation(request, new TaskActionInstance()));

        assertTrue(ex.getMessage().contains("Invalid task linked"));
    }

    @Test
    void testValidateTaskCreation_withExistingTaskInstance_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();

        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.TASK_INSTANCE_ID)
                .value("task-123")
                .build();

        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        when(taskInstanceRepository.get("task-123")).thenReturn(Optional.of(StoredTaskInstance.builder().taskInstanceId("task-123").build()));

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertTrue(result);
    }

    @Test
    void testValidateTaskCreation_withUnsupportedMetaType_shouldReturnFalse() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();

        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.MID)
                .value("merchant-001")
                .build();

        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }
}
