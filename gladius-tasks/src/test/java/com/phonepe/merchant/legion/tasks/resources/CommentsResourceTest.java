package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.merchant.gladius.models.tasks.request.CreateCommentRequest;
import com.phonepe.merchant.gladius.models.tasks.response.CommentsCreationResponse;
import com.phonepe.merchant.gladius.models.tasks.response.CommentsOnTaskResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.CommentsService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.models.response.GenericResponse;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.util.List;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getAgentProfile;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CommentsResourceTest {
    private static CommentsService commentsService;
    private static ValidationService validationService;
    private static AgentProfile agentProfile;
    private static CommentsResource commentsResource;

    @BeforeClass
    public static void init() {
        commentsService = mock(CommentsService.class);
        validationService = mock(ValidationService.class);
        doNothing().when(validationService).validateAccessibleSectors(anyString(), anyString());
        agentProfile = getAgentProfile();
        commentsResource = new CommentsResource(commentsService, validationService);
    }

    @Test
    public void testCreateCommentSuccess() {
        CreateCommentRequest createCommentRequest = CreateCommentRequest.builder()
                .content("comment")
                .taskInstanceId("task123")
                .build();
        StoredCommentsOnTask storedCommentsOnTask = StoredCommentsOnTask.builder().commentId("commentId").build();
        when(commentsService.createComment(any(), anyString())).thenReturn(storedCommentsOnTask);
        GenericResponse<CommentsCreationResponse> response = commentsResource.create(createCommentRequest, null, null, agentProfile);
        Assertions.assertNotNull(response);
        Assertions.assertNotNull(response.getData());
        Assertions.assertNotNull(response.getData().getCommentId());
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testCreateCommentFailure() {
        CreateCommentRequest createCommentRequest = CreateCommentRequest.builder()
                .content("comment")
                .taskInstanceId("task123")
                .build();
        StoredCommentsOnTask storedCommentsOnTask = StoredCommentsOnTask.builder().commentId("commentId").build();
        when(commentsService.createComment(any(), anyString())).thenReturn(null);
        GenericResponse<CommentsCreationResponse> response = commentsResource.create(createCommentRequest, null, null, agentProfile);
        Assertions.assertNotNull(response);
        Assertions.assertNull(response.getData());
        Assertions.assertFalse(response.isSuccess());
    }

    @Test
    public void testGetByTaskInstanceIdSuccess() {
        CommentsOnTaskResponse storedCommentsOnTask = CommentsOnTaskResponse.builder().commentId("commentId").build();
        when(commentsService.getByTaskInstanceId(anyString(), any(), anyInt(), anyInt())).thenReturn(List.of(storedCommentsOnTask));
        GenericResponse<List<CommentsOnTaskResponse>> response = commentsResource.getByTaskInstanceId("taskInstanceId",1,1, null,null, agentProfile);
        Assertions.assertNotNull(response);
        Assertions.assertNotNull(response.getData());
        Assertions.assertTrue(response.isSuccess());
        Assertions.assertEquals(1, response.getData().size());
    }

    @Test
    public void testGetByTaskInstanceIdFailure() {
        when(commentsService.getByTaskInstanceId(anyString(), any(), anyInt(), anyInt())).thenReturn(null);
        GenericResponse<List<CommentsOnTaskResponse>> response = commentsResource.getByTaskInstanceId("taskInstanceId",10,10, null, null, agentProfile);
        Assertions.assertNotNull(response);
        Assertions.assertNull(response.getData());
        Assertions.assertFalse(response.isSuccess());
    }

}
