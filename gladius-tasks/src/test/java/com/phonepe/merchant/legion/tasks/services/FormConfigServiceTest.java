package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.survey.enums.FormAssetType;
import com.phonepe.merchant.gladius.models.survey.request.FormConfigRequest;
import com.phonepe.merchant.gladius.models.survey.response.FormConfigInstance;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFormConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.repository.FormConfigRepository;
import com.phonepe.merchant.legion.tasks.services.impl.FormConfigServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.merchant.legion.tasks.utils.FormConfigTransformationUtils;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FormConfigServiceTest extends LegionTaskBaseTest {

    private static CacheUtils cacheUtils;

    @BeforeClass
    public static void init() {
        formConfigRepository = mock(FormConfigRepository.class);
        cacheUtils = mock(CacheUtils.class);
        formConfigService = new FormConfigServiceImpl(formConfigRepository, eventExecutor, cacheUtils);
    }

    @Before
    public void testSetup() {
        reset(formConfigRepository);
    }

    @Test
    public void testGetFormConfig() {
        String formType = "formType";
        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType(formType).build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigRepository.get(formType)).thenReturn(Optional.of(storedFormConfig));
        assertEquals(formConfigInstance, formConfigService.getFormConfig(formType));
    }

    @Test
    public void testGetFormConfig_EmptyOptional() {
        String formType = "formType";
        when(formConfigRepository.get(formType)).thenReturn(Optional.empty());
        assertNull(formConfigService.getFormConfig(formType));
    }

    @Test
    public void testSaveOrUpdateFormConfig_SaveCase() {
        String formType = "formType";
        String formName = "formName";
        FormAssetType formAssetType = FormAssetType.STORE;
        String campaignId = "campaignId";
        String actor = "actor";
        FormConfigRequest formConfigRequest = FormConfigRequest.builder()
                .formType(formType)
                .formName(formName)
                .formAssetType(formAssetType)
                .campaignId(campaignId).build();
        when(formConfigRepository.get(formType)).thenReturn(Optional.empty());
        StoredFormConfig storedFormConfig = StoredFormConfig.builder()
                .formType(formType)
                .formName(formName)
                .formAssetType(formAssetType)
                .campaignId(campaignId)
                .createdBy(actor)
                .updatedBy(actor).build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(formConfigRepository.save(storedFormConfig)).thenReturn(storedFormConfig);
        assertEquals(formConfigInstance, formConfigService.saveOrUpdateFormConfig(formConfigRequest, actor));
    }

    @Test
    public void testSaveOrUpdateFormConfig_UpdateCase() {
        String formType = "formType";
        String oldFormName = "formName";
        FormAssetType oldFormAssetType = FormAssetType.STORE;
        String oldCampaignId = "campaignId";
        String oldActor = "actor";
        String newFormName = "formName2";
        FormAssetType newFormAssetType = FormAssetType.STORE;
        String newCampaignId = "campaignId2";
        String newActor = "actor2";
        FormConfigRequest formConfigRequest = FormConfigRequest.builder()
                .formType(formType)
                .formName(newFormName)
                .formAssetType(newFormAssetType)
                .campaignId(newCampaignId).build();
        StoredFormConfig oldStoredFormConfig = StoredFormConfig.builder()
                .formType(formType)
                .formName(oldFormName)
                .formAssetType(oldFormAssetType)
                .campaignId(oldCampaignId)
                .createdBy(oldActor)
                .updatedBy(oldActor).build();
        StoredFormConfig newStoredFormConfig = StoredFormConfig.builder()
                .formType(formType)
                .formName(newFormName)
                .formAssetType(newFormAssetType)
                .campaignId(newCampaignId)
                .createdBy(newActor)
                .updatedBy(newActor).build();
        when(formConfigRepository.get(formType)).thenReturn(Optional.of(oldStoredFormConfig));
        when(formConfigRepository.update(any(), any())).thenReturn(newStoredFormConfig);
        assertNotNull(formConfigService.saveOrUpdateFormConfig(formConfigRequest, newActor));
    }

    @Test
    public void testSaveOrUpdateFormConfig_ExceptionCase() {
        String formType = "formType";
        String formName = "formName";
        FormAssetType formAssetType = FormAssetType.STORE;
        String campaignId = "campaignId";
        String actor = "actor";
        FormConfigRequest formConfigRequest = FormConfigRequest.builder()
                .formType(formType)
                .formName(formName)
                .formAssetType(formAssetType)
                .campaignId(campaignId).build();
        when(formConfigRepository.get(formType)).thenThrow(LegionException.class);
        assertThrows(LegionException.class, () -> formConfigService.saveOrUpdateFormConfig(formConfigRequest, actor));
    }

    @Test
    public void testGetFormConfigFromCache() {
        String formType = "formType";
        StoredFormConfig storedFormConfig = StoredFormConfig.builder().formType(formType).build();
        FormConfigInstance formConfigInstance = FormConfigTransformationUtils.toFormConfigInstance(storedFormConfig);
        when(cacheUtils.getValue(formType, CacheName.FORM_CONFIG)).thenReturn(formConfigInstance);
        assertEquals(formConfigInstance, formConfigService.getFormConfigFromCache(formType));
    }

    @Test
    public void testGetFormConfigFromCache_NullCase() {
        String formType = "formType";
        when(cacheUtils.getValue(formType, CacheName.FORM_CONFIG)).thenReturn(null);
        assertNull(formConfigService.getFormConfigFromCache(formType));
    }

}
