package com.phonepe.merchant.legion.tasks.validators;

import com.phonepe.gemini.model.response.order.DeliveryHubOrderResponse;
import com.phonepe.gemini.model.response.order.states.OrderProductState;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.SmartSpeakerDeploymentValidatorConfig;
import com.phonepe.merchant.legion.core.services.GeminiService;
import com.phonepe.merchant.legion.tasks.actions.validators.SmartSpeakerDeploymentValidator;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.DEVICE_NOT_IN_DELIVERED_STATE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SmartSpeakerDeploymentValidatorTest {

    private static GeminiService geminiService;

    private static SmartSpeakerDeploymentValidator smartSpeakerDeploymentValidator;


    @BeforeClass
    public static void init() {
        geminiService = mock(GeminiService.class);
        smartSpeakerDeploymentValidator = new SmartSpeakerDeploymentValidator(geminiService);
    }


    @Test
    public void test_validationSuccess() {
        TaskCompleteRequest request = TaskCompleteRequest.builder().taskInstanceId("task-instance-id").build();
        when(geminiService.getOrderDetails(request.getTaskInstanceId())).thenReturn(DeliveryHubOrderResponse.builder()
                .currentState(OrderProductState.DELIVERED).build());
        ValidatorResponse expected = ValidatorResponse.builder()
                .validated(true)
                .build();
        SmartSpeakerDeploymentValidatorConfig validatorConfig = new SmartSpeakerDeploymentValidatorConfig();
        ValidatorResponse actual = smartSpeakerDeploymentValidator.validate(request, validatorConfig);
        Assertions.assertEquals(expected, actual);
    }

    @Test
    public void test_validationFailure() {
        TaskCompleteRequest request = TaskCompleteRequest.builder().taskInstanceId("task-instance-id").build();
        when(geminiService.getOrderDetails(request.getTaskInstanceId())).thenReturn(DeliveryHubOrderResponse.builder()
                .currentState(OrderProductState.CREATED).build());
        ValidatorResponse expected = ValidatorResponse.builder()
                .validated(false)
                .errorCode(DEVICE_NOT_IN_DELIVERED_STATE)
                .errorMessage("You are not allowed to mark the task completed until the device is marked delivered")
                .build();
        SmartSpeakerDeploymentValidatorConfig validatorConfig = new SmartSpeakerDeploymentValidatorConfig();
        ValidatorResponse actual = smartSpeakerDeploymentValidator.validate(request, validatorConfig);
        Assertions.assertEquals(expected, actual);
    }
}
