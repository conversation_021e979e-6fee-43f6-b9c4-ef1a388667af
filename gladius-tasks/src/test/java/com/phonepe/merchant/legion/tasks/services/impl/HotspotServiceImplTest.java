package com.phonepe.merchant.legion.tasks.services.impl;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.filtercraft.es.internal.converter.filters.impl.ConditionToEsQueryConverterImpl;
import com.phonepe.merchant.filtercraft.es.internal.converter.filters.impl.FilterToEsQueryConverterImpl;
import com.phonepe.merchant.filtercraft.filters.client.FilterCraftClientBuilder;
import com.phonepe.merchant.filtercraft.filters.client.impl.FilterCraftClientBuilderImpl;
import com.phonepe.merchant.filtercraft.internal.evaluators.impl.ConditionEvaluatorImpl;
import com.phonepe.merchant.filtercraft.internal.evaluators.impl.FilterEvaluatorImpl;
import com.phonepe.merchant.filtercraft.internal.models.Filter;
import com.phonepe.merchant.filtercraft.internal.models.conditions.Condition;
import com.phonepe.merchant.filtercraft.internal.models.conditions.InCondition;
import com.phonepe.merchant.filtercraft.internal.parsers.impl.FilterParserImpl;
import com.phonepe.merchant.gladius.models.hotspots.Coordinates;
import com.phonepe.merchant.gladius.models.hotspots.GeoRegionHashEntity;
import com.phonepe.merchant.gladius.models.hotspots.HotspotRegion;
import com.phonepe.merchant.gladius.models.hotspots.enums.HotspotStatus;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotAccessDetailsRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotMetaData;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotSyncRequest;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotAccessDetailsResponse;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotDto;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.hotspots.HotspotGenerator;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.GeneralPurposeCache;
import com.phonepe.merchant.legion.tasks.cache.HotspotConfigCache;
import com.phonepe.merchant.legion.tasks.cache.HotspotTaskStatCache;
import com.phonepe.merchant.legion.tasks.repository.HotspotConfigRepository;
import com.phonepe.merchant.legion.tasks.repository.HotspotRepository;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskSearchRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.Filters;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.ACTION_ID;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.HOTSPOT_TYPE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TOTAL_COUNT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TOTAL_POINTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


class HotspotServiceImplTest {

    @Mock
    private HotspotRepository hotspotRepository;

    @Mock
    private HotspotConfigRepository hotspotConfigRepository;

    @Mock
    private TaskSearchRequestQueryBuilderFactory taskSearchRequestQueryBuilderFactory;

    @Mock
    private HotspotRequestQueryBuilder hotspotRequestQueryBuilder;

    @Mock
    private ESRepository esRepository;

    @Mock
    private HotspotGenerator hotspotGenerator;

    @Mock
    private FilterCraftBuilderCache filterCraftBuilderCache;

    @Mock
    private LegionService legionService;

    @Mock
    private FoxtrotEventIngestionService foxtrotEventIngestionService;

    @Mock
    private GeneralPurposeCache generalPurposeCache;

    @Mock
    private HotspotTaskStatCache hotspotTaskStatCache;

    @Mock
    private AtlasService atlasService;

    private HotspotServiceImpl hotspotServiceImpl;

    @Mock
    private SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    @Mock
    private HotspotConfigCache hotspotConfigCache;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private FilterCraftClientBuilder filterCraftClientBuilder = new FilterCraftClientBuilderImpl(
            new FilterParserImpl(objectMapper),
            new FilterEvaluatorImpl(new ConditionEvaluatorImpl(), objectMapper),
            new FilterToEsQueryConverterImpl(new ConditionToEsQueryConverterImpl()));

    private Map<CacheName, CacheConfig> cacheConfigs;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        SerDe.init(objectMapper);
        cacheConfigs = new HashMap<>();
        cacheConfigs.put(CacheName.FILTERCRAFT_BUILDER, new CacheConfig());
        cacheConfigs.put(CacheName.HOTSPOT_TASK_STAT, new CacheConfig());
        filterCraftBuilderCache = new FilterCraftBuilderCache(cacheConfigs, mock(MetricRegistry.class),
                mock(CacheUtils.class), filterCraftClientBuilder);
        hotspotTaskStatCache = new HotspotTaskStatCache(cacheConfigs, mock(CacheUtils.class), mock(MetricRegistry.class));
        hotspotServiceImpl = new HotspotServiceImpl(hotspotRepository, legionService,
                taskSearchRequestQueryBuilderFactory, hotspotRequestQueryBuilder, esRepository, hotspotGenerator, filterCraftBuilderCache,
                foxtrotEventIngestionService, generalPurposeCache, atlasService, hotspotConfigCache, hotspotTaskStatCache, sectorGeofenceQueryBuilder);
        QueryBuilder queryBuilder = new BoolQueryBuilder();
        when(taskSearchRequestQueryBuilderFactory.queryBuilder(any(), any())).thenReturn(queryBuilder);
        when(hotspotRequestQueryBuilder.buildQuery(anyString(), any(StoredHotspot.class))).thenReturn(QueryBuilders.boolQuery());
    }


    @Test
    void testSyncHotspots_Failure() {
        // Arrange
        String actorId = "actor123";
        String hotspotType = "SS";
        HotspotSyncRequest syncRequest = new HotspotSyncRequest();
        syncRequest.setSectorId("sector123");
        syncRequest.setHotspotType(hotspotType);
        syncRequest.setGeoRegionHashEntity(GeoRegionHashEntity.GEO_HASH);
        List<StoredHotspot> activeHotspots = Collections.singletonList(createMockStoredHotspot());
        when(hotspotRepository.getSectorHotspots(anyString(), anyString())).thenReturn(activeHotspots);
        StoredHotspotConfig hotspotConfig = new StoredHotspotConfig();
        when(hotspotConfigRepository.get(hotspotType)).thenReturn(Optional.of(hotspotConfig));
        Assertions.assertThrows(LegionException.class, () -> hotspotServiceImpl.syncHotspots(actorId, syncRequest));
    }


    @Test
    void testSyncHotspotsConfigNotFound() {
        // Arrange
        String actorId = "actor123";
        HotspotSyncRequest syncRequest = new HotspotSyncRequest();
        syncRequest.setSectorId("sector123");
        syncRequest.setHotspotType("INVALID_TYPE");
        when(hotspotConfigCache.getHotspotConfigFromCache("INVALID_TYPE", true))
                .thenThrow(LegionException.error(NOT_FOUND));
        // Act & Assert
        assertThrows(LegionException.class, () -> hotspotServiceImpl.syncHotspots(actorId, syncRequest));
        verify(hotspotRepository, never()).deactivateHotspot(anyString(), anyString());
    }

    @Test
    void testGetStoredHotspotsSuccess() {
        String actorId = "actor123";
        String sectorId = "sector123";
        String type = "SS";
        List<StoredHotspot> storedHotspots = Collections.singletonList(createMockStoredHotspot());
        when(hotspotRepository.getSectorHotspots(sectorId, type)).thenReturn(storedHotspots);
        Sum searchHits = mock(Sum.class);
        ValueCount totalHits = mock(ValueCount.class);
        when(totalHits.getValue()).thenReturn(1L);
        when(searchHits.getValue()).thenReturn(1.0);
        Aggregations subAggregations = mock(Aggregations.class);
        when(subAggregations.get(TOTAL_COUNT)).thenReturn(totalHits);
        when(subAggregations.get(TOTAL_POINTS)).thenReturn(searchHits);
        Filters.Bucket bucket = mock(Filters.Bucket.class);
        when(bucket.getKeyAsString()).thenReturn("HS123");
        when(bucket.getAggregations()).thenReturn(subAggregations);
        Filters filters = mock(Filters.class);
        Mockito.doReturn(Collections.singletonList(bucket)).when(filters).getBuckets();
        Aggregations aggregations = Mockito.mock(Aggregations.class);
        when(aggregations.get("hotspotFilters")).thenReturn(filters);
        SearchResponse searchResponse = mock(SearchResponse.class);
        when(searchResponse.getAggregations()).thenReturn(aggregations);
        when(esRepository.getAggregationResult(anyString(), any(), any())).thenReturn(searchResponse);
        when(hotspotConfigCache.fetchHotspotTypeForCategoryAndSector(type, sectorId)).thenReturn("SS");
        List<HotspotDto> result = hotspotServiceImpl.getStoredHotspots(actorId, sectorId, type);
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(hotspotRepository).getSectorHotspots(sectorId, type);
    }

    @Test
    void testGetHotspotAccessDetails() {
        // Test Data
        String actorId = "testActorId";
        HotspotAccessDetailsRequest request = new HotspotAccessDetailsRequest(); // Replace with appropriate instantiation
        Set<String> storedHotspotTypes = Set.of("SS", "PIDF");
        AgentProfile profile = AgentProfile.builder().agentType(AgentType.BM).build();
        List<String> accessibleSectors = List.of("Sector1", "Sector2");
        Filter filtercraftConfig = createFilterCraftFilter();


        // Mocking dependencies
        when(generalPurposeCache.get(eq(HOTSPOT_TYPE), any())).thenReturn(storedHotspotTypes);
        when(legionService.getAgentProfile(actorId)).thenReturn(profile);
        when(legionService.getAllAccessibleSectorsOfAgent(actorId)).thenReturn(accessibleSectors);
        String hotspotMetaData = SerDe.writeValueAsString(HotspotMetaData.builder().filtercraftConfig(SerDe.toJsonNode(filtercraftConfig)).build());
        when(hotspotConfigCache.getHotspotConfigFromCache("SS", false))
                .thenReturn(StoredHotspotConfig.builder()
                        .hotspotType("SS").category("SS").whitelistedSectors(Set.of("Sector1", "Sector2"))
                        .hotspotMetadata(hotspotMetaData).build());
        when(hotspotConfigCache.getHotspotConfigFromCache("PIDF", false))
                .thenReturn(StoredHotspotConfig.builder()
                        .hotspotType("PIDF").category("PIDF").whitelistedSectors(Set.of("Sector1", "Sector2"))
                        .hotspotMetadata(hotspotMetaData).build());


        HotspotAccessDetailsResponse response = hotspotServiceImpl.getHotspotAccessDetails(actorId, request);

        // Verify results
        assertNotNull(response);
        assertEquals(2, response.getDetails().size());
        assertEquals(List.of("Sector1", "Sector2"), response.getDetails().get(0).getSectors());
    }



    // Helper methods to create mock objects
    private StoredHotspot createMockStoredHotspot() {
        StoredHotspot hotspot = new StoredHotspot();
        hotspot.setId("HS123");
        hotspot.setSectorId("sector123");
        hotspot.setStatus(HotspotStatus.ACTIVE);
        hotspot.setRegion(createMockHotspotRegion());
        return hotspot;
    }

    private HotspotRegion createMockHotspotRegion() {
        HotspotRegion region = new HotspotRegion();
        region.setCentroid(new Coordinates(12.9716, 77.5946));
        region.setGeofence(List.of(new Coordinates(12.9716, 77.5946)));
        region.setValue(100);
        return region;
    }

    @Test
    void testDeactivateHotspots_Success() {
        // Arrange
        String actorId = "actor123";
        String hotspotType = "SS";
        String hotspotId = "hId1";
        when(hotspotRepository.get(hotspotId)).thenReturn(Optional.of(StoredHotspot.builder().id(hotspotId)
                        .region(HotspotRegion.builder().build())
                .hotspotType(hotspotType).build()));
        doNothing().when(hotspotRepository).deactivateHotspot(hotspotId, actorId);
        HotspotDto hotspotDto = HotspotDto.builder().id(hotspotId).type(hotspotType).region(HotspotRegion.builder().build()).build();
        Assertions.assertEquals(hotspotDto, hotspotServiceImpl.deactivateHotspot(actorId, hotspotId));
    }

    @Test
    void testDeactivateHotspots_Failure() {
        // Arrange
        String actorId = "actor123";
        String hotspotType = "SS";
        String hotspotId = "hId1";
        when(hotspotRepository.get(hotspotId)).thenReturn(Optional.of(StoredHotspot.builder().id(hotspotId)
                .region(HotspotRegion.builder().build())
                .hotspotType(hotspotType).build()));
        doThrow(new RuntimeException()).when(hotspotRepository).deactivateHotspot(hotspotId, actorId);
        Assertions.assertThrows(RuntimeException.class, () -> hotspotServiceImpl.deactivateHotspot(actorId, hotspotId));
    }

    @Test
    void testDeactivateHotspots_NotFoundFailure() {
        // Arrange
        String actorId = "actor123";
        String hotspotId = "hId1";
        when(hotspotRepository.get(hotspotId)).thenReturn(Optional.empty());
        doThrow(new RuntimeException()).when(hotspotRepository).deactivateHotspot(hotspotId, actorId);
        Assertions.assertThrows(RuntimeException.class, () -> hotspotServiceImpl.deactivateHotspot(actorId, hotspotId));
    }

    @Test
    void test_fetchHotspotsByLocation() {
        double latitude = 12.9716;
        double longitude = 77.5946;
        List<StoredHotspot> storedHotspots = Collections.singletonList(createMockStoredHotspot());
        when(atlasService.getSectorIdByLatLong(latitude, longitude)).thenReturn(List.of("sector123"));
        when(hotspotRepository.fetchSectorHotspots(List.of("sector123"))).thenReturn(storedHotspots);
        List<HotspotDto> result = hotspotServiceImpl.fetchHotspotsByLocation(latitude, longitude);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void test_fetchHotspotsByLocation_EmptyList() {
        double latitude = 12.9716;
        double longitude = 77.5946;
        List<StoredHotspot> storedHotspots = List.of();
        when(atlasService.getSectorIdByLatLong(latitude, longitude)).thenReturn(List.of("sector123"));
        when(hotspotRepository.fetchSectorHotspots(List.of("sector123"))).thenReturn(storedHotspots);
        List<HotspotDto> result = hotspotServiceImpl.fetchHotspotsByLocation(latitude, longitude);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void test_fetchHotspotsByLocation_NullRegion() {
        double latitude = 12.9716;
        double longitude = 77.5946;
        StoredHotspot storedHotspot = createMockStoredHotspot();
        storedHotspot.setRegion(null);
        when(atlasService.getSectorIdByLatLong(latitude, longitude)).thenReturn(List.of("sector123"));
        when(hotspotRepository.fetchSectorHotspots(List.of("sector123"))).thenReturn(Collections.singletonList(storedHotspot));
        List<HotspotDto> result = hotspotServiceImpl.fetchHotspotsByLocation(latitude, longitude);
        assertNotNull(result);
        assertEquals(0, result.size());
    }


    private Filter createFilterCraftFilter() {
        List<Condition> conditionList = new ArrayList<>();
        List<String>actions = new ArrayList<>();
        InCondition<String> actionCondition = new InCondition<>();
        actionCondition.setField(ACTION_ID);
        actionCondition.setValues(actions);
        conditionList.add(actionCondition);
        Filter filter = new Filter();
        filter.setOrConditions(Collections.singletonList(conditionList));
        return filter;
    }


}