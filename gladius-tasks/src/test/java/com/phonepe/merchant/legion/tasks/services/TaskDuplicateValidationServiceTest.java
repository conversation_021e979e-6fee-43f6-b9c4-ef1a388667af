package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.UserTaskCreationConfig;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.flows.TaskEngine;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDuplicateValidationServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskManagementServiceImpl;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.CREATED;
import static org.mockito.Mockito.mock;

public class TaskDuplicateValidationServiceTest extends LegionTaskBaseTest {

    private static TaskDuplicateValidationService taskDuplicateValidationService;
    private static LeadConfig leadConfig;
    private static final String taskDefId = IdGenerator.generate("TD").getId();
    private static final String taskInstanceId = IdGenerator.generate("TI").getId();
    public static final UserTaskCreationConfig userTaskCreationConfigs = mock(UserTaskCreationConfig.class);

    @BeforeClass
    public static void init() {
        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId(taskInstanceId)
                .taskDefinitionId(taskDefId)
                .campaignId("campaignId")
                .entityType(EntityType.SECTOR)
                .curState(CREATED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .rescheduledAt(new Date())
                .build());
        leadConfig = mock(LeadConfig.class);
        TaskManagementService taskManagementService = new TaskManagementServiceImpl(mock(TaskEngine.class), taskInstanceRepository, transitionValidator, validationService, taskInstanceManagementService, userTaskCreationConfigs, miscellaneous, foxtrotEventIngestionService);
        taskDuplicateValidationService = new TaskDuplicateValidationServiceImpl(taskManagementService);
    }

    @Test
    public void validateAndGetDuplicateTaskInstanceSuccess() {
        ClientTaskCreateAndAssignRequest request1 = ClientTaskCreateAndAssignRequest.builder()
                .assigneeId("shivam")
                .campaignId("CAMPAIGN99")
                .entityId("MID_SID")
                .taskDefinitionId(taskDefId)
                .createdBy("ACE")
                .createTaskForManager(true)
                .markAvailable(false)
                .forceTaskCreationRequest(false)
                .build();
        StoredTaskInstance responseTaskInstance = taskDuplicateValidationService.validateAndGetDuplicateTaskInstance(
                List.of(DiscoveryTaskInstance.builder().taskDefinitionId(taskDefId).taskInstanceId(taskInstanceId).build()),
                request1);
        Assert.assertFalse(Objects.isNull(responseTaskInstance));
        Assert.assertEquals(taskInstanceId, responseTaskInstance.getTaskInstanceId());
    }
}
