package com.phonepe.merchant.legion.tasks.converters;

import com.phonepe.merchant.gladius.models.tasks.enums.ChangeType;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceAuditResponse;
import com.phonepe.merchant.legion.core.config.AuditConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;

class TaskInstanceAuditConverterTest {

    private TaskInstanceAuditConverter converter;
    private Date updatedAt;

    @BeforeEach
    void setUp() {
        AuditConfig auditConfig = new AuditConfig(); // Mock or set up audit config as needed
        converter = new TaskInstanceAuditConverter(auditConfig);

        updatedAt = new Date();
    }

    @Test
    void testCreateAuditResponse() {
        // Define parameters for the audit response creation
        ChangeType changeType = ChangeType.MODIFIED;
        String fieldName = "status";
        Object initialFieldValue = "PENDING";
        Object finalFieldValue = "COMPLETED";
        String agentId = "A1";
        String referenceId = "taskInstanceId";

        // Call the method under test
        TaskInstanceAuditResponse response = converter.createAuditResponse(referenceId,
                changeType, fieldName, initialFieldValue, finalFieldValue, updatedAt, agentId
        );

        // Verify the response fields
        assertEquals(changeType, response.getChangeType());
        assertEquals(fieldName, response.getFieldName());
        assertEquals(initialFieldValue, response.getInitialValue());
        assertEquals(finalFieldValue, response.getFinalValue());
        assertEquals(updatedAt, response.getUpdatedAt());
        assertEquals(agentId, response.getUpdatedById());
    }
}
