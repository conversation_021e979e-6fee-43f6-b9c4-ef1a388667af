package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.entitystore.ExternalEntity;
import com.phonepe.merchant.gladius.models.survey.ClientQcTaskConfig;
import com.phonepe.merchant.gladius.models.survey.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.enums.TaskSchedulingType;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EventBasedTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCampaign;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.OdinService;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.DiscoveryTestUtils;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskTypeFilterQuery;
import com.phonepe.merchant.legion.tasks.services.impl.ClientTaskServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDefinitionServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.TaskDefinitionTransformationUtils;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.tasks.EligibleEventBasedTaskType;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationClient;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationConfig;
import com.phonepe.models.merchants.tasks.TaskCreationConfig;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.AVAILABLE;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.BOUNDED_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.COMPLETED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.CREATED;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_DEFINITION_ID_PREFIX;
import static com.phonepe.models.merchants.tasks.EntityType.EXTERNAL;
import static com.phonepe.models.merchants.tasks.EntityType.MERCHANT;
import static com.phonepe.models.merchants.tasks.EntityType.STORE;
import static com.phonepe.models.merchants.tasks.EntityType.TASK;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR> puri
 */
@RunWith(MockitoJUnitRunner.class)
public class ClientTaskServiceTest extends LegionTaskBaseTest {

    private static ClientTaskService clientTaskService;
    private static TaskInstanceManagementService taskInstanceManagementService;
    private static LegionService profileCRUDService = mock(LegionService.class);
    private static TaskManagementService taskManagementService = mock(TaskManagementService.class);
    private static TaskAttributeService taskAttributeService = mock(TaskAttributeService.class);
    private static TaskDefinitionService taskDefinitionService;
    private static EventBasedTaskCreationConfig eventBasedTaskCreationConfig;
    private static EntityStore entityStore = mock(EntityStore.class);
    private static ValidationService validationService = mock(ValidationService.class);
    private static ExternalEntityService externalEntityService = mock(ExternalEntityService.class);
    private static OdinService odinService = mock(OdinService.class);
    private static AtlasService atlasService = mock(AtlasService.class);
    private static ChimeraRepositoryImpl chimeraRepository = mock(ChimeraRepositoryImpl.class);
    private static LeadCreationConfig leadCreationConfig = mock(LeadCreationConfig.class);

    private static String TASK_DEFINITION_ID = IdGenerator.generate(TASK_DEFINITION_ID_PREFIX).getId();
    private static String MISMATCHING_TASK_DEFINITION_ID = IdGenerator.generate(TASK_DEFINITION_ID_PREFIX).getId();
    private static String ACTION_ID = EligibleEventBasedTaskType.OQC_VALIDATION.name();
    private static String CAMPAIGN_ID = IdGenerator.generate("C").getId();
    private static String AGENT_ID = IdGenerator.generate("A").getId();
    private static String DISTRIBUTOR = "DISTRIBUTOR_ID";
    private static String DFOS = "DFOS_ID";
    private static String TSM_ID = "UNIQUE_TSM";
    private static TaskESRepository taskESRepository = mock(TaskESRepository.class);

    @BeforeClass
    public static void init(){
        TaskDefinitionMeta definitionMeta = TaskDefinitionMeta.builder().taskDefinitionId("123").description("test description").build();
        ClientQcTaskConfig clientQcTaskConfig = ClientQcTaskConfig.builder()
                .mapping(Map.of(TASK_DEFINITION_ID, definitionMeta)).build();
        taskDefinitionService = new TaskDefinitionServiceImpl(taskDefinitionRepository, taskActionService, eventExecutor, mock(TagService.class), taskAttributeCache, cacheUtils, null, validationService, leadCreationConfig);
        taskInstanceManagementService = new TaskInstanceManagementServiceImpl(null, taskESRepository, null, taskInstanceRepository, taskManagementService, mock(FoxtrotEventIngestionService.class), taskAttributeService, new Miscellaneous(50, 10, 90, 10, 5),
                eventExecutor, mock(TaskTypeFilterQuery.class), taskDiscoveryService,legionService, odinService, null, validationService, atlasService, chimeraRepository, validations, null ,null ,null);
        clientTaskService = new ClientTaskServiceImpl(taskInstanceManagementService, taskManagementService, profileCRUDService, clientQcTaskConfig, eventExecutor, taskDefinitionService,validationService,merchantService, externalEntityService, transitionValidator, taskDuplicateValidationService);

        Map<EligibleEventBasedTaskType,TaskCreationConfig> map = new HashMap<>();
        map.put(EligibleEventBasedTaskType.OQC_VALIDATION, TaskCreationConfig.builder()
                .campaignId(CAMPAIGN_ID)
                .taskDefinitionId(TASK_DEFINITION_ID)
                .build());
        map.put(EligibleEventBasedTaskType.COMP_VPA_MAPPING, TaskCreationConfig.builder()
                .campaignId(CAMPAIGN_ID)
                .taskDefinitionId(MISMATCHING_TASK_DEFINITION_ID)
                .build());
        eventBasedTaskCreationConfig = EventBasedTaskCreationConfig.builder()
                .clientName(EventBasedTaskCreationClient.MERCHANT_ONBOARDING)
                .taskCreationConfigMap(map)
                .build();

        taskDefinitionRepository.save(StoredTaskDefinition.builder()
                .actionId("MISMATCHING_ACTION")
                .taskDefinitionId(MISMATCHING_TASK_DEFINITION_ID)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("NAME")
                .points(1)
                .createdBy("jakshat")
                .priority(Priority.P1)
                .build());
    }

    private StoredTaskInstance happyFlowTaskSetup(EntityType entityType, String entityId, Boolean markAvailable) {
        if (!taskDefinitionRepository.get(TASK_DEFINITION_ID).isPresent()) {
            taskDefinitionRepository.save(StoredTaskDefinition.builder()
                    .actionId(ACTION_ID)
                    .taskDefinitionId(TASK_DEFINITION_ID)
                    .namespace(Namespace.MERCHANT_ONBOARDING)
                    .name("NAME")
                    .points(1)
                    .createdBy("jakshat")
                    .priority(Priority.P1)
                    .build());
        }
        if (!campaignRepository.get(CAMPAIGN_ID).isPresent()) {
            campaignRepository.save(StoredCampaign.builder()
                    .expiryPeriod(ExpiryPeriod.RELATIVE_DAYS)
                    .expiryValue((short) 1)
                    .campaignId(CAMPAIGN_ID)
                    .createdBy("jakshat")
                    .startDate(new Date())
                    .build());
        }

        StoredTaskInstance createdTask = StoredTaskInstance.builder()
                .entityId(entityId)
                .entityType(entityType)
                .curActor(TSM_ID)
                .curState(CREATED)
                .taskInstanceId(IdGenerator.generate("T").getId())
                .campaignId("CID")
                .taskDefinitionId("TID")
                .actionId("AID")
                .createdBy("saransh")
                .updatedBy("saransh")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();
        when(taskManagementService.command(
                any(TaskCreateRequest.class), any()))
                .thenReturn(createdTask);

        when(profileCRUDService.getAgentProfile(any())).thenReturn(AgentProfile.builder()
                .agentId("agent")
                .active(true)
                .managerId("manager")
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.ASM)
                .build());
        if (!markAvailable) {
            StoredTaskInstance assignedTask = StoredTaskInstance.builder()
                    .entityId(createdTask.getEntityId())
                    .entityType(createdTask.getEntityType())
                    .curActor(TSM_ID)
                    .dueDate(new Date())
                    .curState(BOUNDED_ASSIGNED)
                    .actionId(createdTask.getActionId())
                    .namespace(createdTask.getNamespace())
                    .taskDefinitionId(createdTask.getTaskDefinitionId())
                    .createdBy(createdTask.getCreatedBy())
                    .updatedBy(createdTask.getUpdatedBy())
                    .taskInstanceId(createdTask.getTaskInstanceId())
                    .build();
            when(taskManagementService.command(any(TaskAssignRequest.class), Mockito.anyString()))
                    .thenReturn(assignedTask);
            return taskInstanceRepository.save(assignedTask);
        } else {
            StoredTaskInstance availableTask = StoredTaskInstance.builder()
                    .entityId(createdTask.getEntityId())
                    .entityType(createdTask.getEntityType())
                    .curActor(TSM_ID)
                    .dueDate(new Date())
                    .curState(AVAILABLE)
                    .actionId(createdTask.getActionId())
                    .namespace(createdTask.getNamespace())
                    .taskDefinitionId(createdTask.getTaskDefinitionId())
                    .updatedBy(createdTask.getUpdatedBy())
                    .createdBy(createdTask.getUpdatedBy())
                    .taskInstanceId(createdTask.getTaskInstanceId())
                    .build();

            when(taskManagementService.command(any(TaskCreateRequest.class), any()))
                    .thenReturn(availableTask);
            return taskInstanceRepository.save(availableTask);
        }
    }

    @Test
    public void createAndAssignQcTask(){
      String instanceId = IdGenerator.generate("T").getId();
      String entityId = IdGenerator.generate("M").getId();
      StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId(instanceId)
                .taskDefinitionId(TASK_DEFINITION_ID)
                .actionId(ACTION_ID)
                .campaignId(CAMPAIGN_ID)
                .curState(LegionTaskStateMachineState.COMPLETED)
                .entityId(entityId)
                .entityType(MERCHANT)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .curActor(AGENT_ID)
              .createdBy("Saransh")
              .updatedBy("Saransh")
                .build();
      taskInstanceRepository.save(storedTaskInstance);
      StoredTaskInstance expectedResponse = happyFlowTaskSetup(TASK,instanceId,false);
      StoredTaskInstance instance = clientTaskService.createAndAssignQCTask(instanceId);
      Assert.assertEquals(expectedResponse.getCurActor(), instance.getCurActor());
      Assert.assertEquals(BOUNDED_ASSIGNED, instance.getCurState());
      Assert.assertEquals(TASK, instance.getEntityType());
    }

    @Test
    public void createBoundedAssignedTaskFromEvent(){
        String entityId = IdGenerator.generate("M").getId();
        StoredTaskInstance expectedResponse = happyFlowTaskSetup(MERCHANT,entityId,false);
        StoredTaskInstance actualResponse = clientTaskService.createTaskFromEvent(EventBasedTaskCreationRequest.builder()
                .entityId("entityId")
                .config(eventBasedTaskCreationConfig)
                .agentId(AGENT_ID)
                .role(AgentType.TSM)
                .markAvailable(false)
                .taskType(EligibleEventBasedTaskType.OQC_VALIDATION)
                .build());
        Assert.assertEquals(expectedResponse.getTaskDefinitionId(),actualResponse.getTaskDefinitionId());
        Assert.assertEquals(expectedResponse.getCampaignId(),actualResponse.getCampaignId());
        Assert.assertEquals(BOUNDED_ASSIGNED,actualResponse.getCurState());
        Assert.assertEquals(TSM_ID,actualResponse.getCurActor());
    }

    @Test
    public void createAvailableTaskFromEvent(){
        String entityId = IdGenerator.generate("M").getId();
        StoredTaskInstance expectedResponse = happyFlowTaskSetup(MERCHANT,entityId,true);
        StoredTaskInstance actualResponse = clientTaskService.createTaskFromEvent(EventBasedTaskCreationRequest.builder()
                .entityId("entityId")
                .config(eventBasedTaskCreationConfig)
                .markAvailable(true)
                .taskType(EligibleEventBasedTaskType.OQC_VALIDATION)
                .build());
        Assert.assertEquals(expectedResponse.getTaskDefinitionId(),actualResponse.getTaskDefinitionId());
        Assert.assertEquals(expectedResponse.getCampaignId(),actualResponse.getCampaignId());
        Assert.assertEquals(AVAILABLE,actualResponse.getCurState());
    }

    @Test(expected = LegionException.class)
    public void createTaskFromEventTaskTypeMismatch() {
        clientTaskService.createTaskFromEvent(EventBasedTaskCreationRequest.builder()
                .config(eventBasedTaskCreationConfig)
                .taskType(EligibleEventBasedTaskType.COMP_VPA_MAPPING)
                .markAvailable(true)
                .entityId("abcd")
                .build());
    }

    @Test(expected = LegionException.class)
    public void createTaskFromEventAgentManagerMissing() {
        when(profileCRUDService.getAgentProfile(DFOS)).thenReturn(AgentProfile.builder()
                .agentId(DFOS)
                .active(true)
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.DFOS)
                .build());
        clientTaskService.createTaskFromEvent(EventBasedTaskCreationRequest.builder()
                .entityId("entityId")
                .config(eventBasedTaskCreationConfig)
                .agentId(DFOS)
                .role(AgentType.DFOS)
                .markAvailable(false)
                .taskType(EligibleEventBasedTaskType.OQC_VALIDATION)
                .build());
    }

    @Test(expected = LegionException.class)
    public void createTaskFromEventL2ManagerMissing() {
        when(profileCRUDService.getAgentProfile(DFOS)).thenReturn(AgentProfile.builder()
                .agentId(DFOS)
                .active(true)
                .managerId(DISTRIBUTOR)
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.DFOS)
                .build());
        when(profileCRUDService.getAgentProfile(DISTRIBUTOR)).thenReturn(AgentProfile.builder()
                .agentId(DISTRIBUTOR)
                .active(true)
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.DISTRIBUTOR)
                .build());
        clientTaskService.createTaskFromEvent(EventBasedTaskCreationRequest.builder()
                .entityId("entityId")
                .config(eventBasedTaskCreationConfig)
                .agentId(DFOS)
                .role(AgentType.DFOS)
                .markAvailable(false)
                .taskType(EligibleEventBasedTaskType.OQC_VALIDATION)
                .build());
    }

    @Test(expected = Test.None.class)
    public void createTaskFromEventInvalidAgentType() {
        when(profileCRUDService.getAgentProfile(DFOS)).thenReturn(AgentProfile.builder()
                .agentId(DFOS)
                .active(true)
                .managerId(DISTRIBUTOR)
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.DFOS)
                .build());
        when(profileCRUDService.getAgentProfile(DISTRIBUTOR)).thenReturn(AgentProfile.builder()
                .agentId(DISTRIBUTOR)
                .active(true)
                .managerId(TSM_ID)
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.DISTRIBUTOR)
                .build());
        when(profileCRUDService.getAgentProfile(TSM_ID)).thenReturn(AgentProfile.builder()
                .agentId(TSM_ID)
                .active(true)
                .sectors(Collections.singletonList("string"))
                .agentType(AgentType.TSM)
                .build());
        String entityId = IdGenerator.generate("M").getId();
        clientTaskService.createTaskFromEvent(EventBasedTaskCreationRequest.builder()
                .entityId("entityId")
                .config(eventBasedTaskCreationConfig)
                .agentId(DFOS)
                .role(AgentType.DFOS)
                .markAvailable(false)
                .taskType(EligibleEventBasedTaskType.OQC_VALIDATION)
                .build());
    }

        @Test(expected = Test.None.class)
        public void tagMxnToExternalEntity() {
        String storeId = "storeId";
        String merchantId = "merchantId";
        String externalEntityId = "external";

        StoredTaskInstance storedTaskInstance = happyFlowTaskSetup(EXTERNAL, externalEntityId, false);
        String taskInstanceId = storedTaskInstance.getTaskInstanceId();

        when(merchantService.getMerchantDetails(merchantId))
                .thenReturn(MerchantProfile.builder()
                        .phoneNumber("1234567890")
                        .build());
        when(externalEntityService.get(any()))
                .thenReturn(ExternalEntity.builder()
                        .clientProvidedId(merchantId + "_" + storeId)
                        .build());

        clientTaskService.tagMxnToExternalEntity(merchantId,storeId,taskInstanceId);
    }


    @Test(expected = Test.None.class)
    public void rescheduleTask() {
        String entityId = IdGenerator.generate("M").getId();


        StoredTaskInstance storedTaskInstance = happyFlowTaskSetup(MERCHANT,entityId,false);
        storedTaskInstance.setDueDate(new Date());

        Date date = new Date();
        long currentTime = date.getTime();

        currentTime += 10000;
        when(taskESRepository.get(storedTaskInstance.getTaskInstanceId())).thenReturn(DiscoveryTaskInstance.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .actionId(storedTaskInstance.getActionId())
                .dueDate(currentTime+100000)
                .taskState(BOUNDED_ASSIGNED)
                .assignedTo(TSM_ID).build());
        TaskSchedulingPayload taskSchedulingPayload = TaskSchedulingPayload.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .rescheduleAt(currentTime+100000)
                .scheduleType(TaskSchedulingType.SCHEDULE)
                .build();
        taskInstanceManagementService.rescheduleTask(taskSchedulingPayload, TSM_ID);
    }

    @Test(expected = Test.None.class)
    public void cancelRescheduleTask() {
        String entityId = IdGenerator.generate("M").getId();


        StoredTaskInstance storedTaskInstance = happyFlowTaskSetup(MERCHANT,entityId,false);

        Date date = new Date();
        long currentTime = date.getTime();

        currentTime += 10000;
        when(taskESRepository.get(storedTaskInstance.getTaskInstanceId())).thenReturn(DiscoveryTaskInstance.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .actionId(storedTaskInstance.getActionId())
                .dueDate(currentTime+100000)
                .taskState(BOUNDED_ASSIGNED)
                .assignedTo(TSM_ID).build());
        TaskSchedulingPayload taskSchedulingPayload = TaskSchedulingPayload.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .scheduleType(TaskSchedulingType.CANCEL)
                .build();
        taskInstanceManagementService.rescheduleTask(taskSchedulingPayload, TSM_ID);
    }

    @Ignore(value = "This will be fixed later")
    @Test(expected = LegionException.class)
    public void dateValidationCheck() {
        String entityId = IdGenerator.generate("M").getId();


        StoredTaskInstance storedTaskInstance = happyFlowTaskSetup(MERCHANT,entityId,false);

        Date date = new Date();
        long currentTime = date.getTime();


        when(taskESRepository.get(storedTaskInstance.getTaskInstanceId())).thenReturn(DiscoveryTaskInstance.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .actionId(storedTaskInstance.getActionId())
                .dueDate(currentTime-1000)
                .taskState(BOUNDED_ASSIGNED)
                .assignedTo(TSM_ID).build());
        TaskSchedulingPayload taskSchedulingPayload = TaskSchedulingPayload.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .rescheduleAt(currentTime)
                .scheduleType(TaskSchedulingType.SCHEDULE)
                .build();
        taskInstanceManagementService.rescheduleTask(taskSchedulingPayload, TSM_ID);
    }

    @Test(expected = LegionException.class)
    public void dateValidationCheck2() {
        String entityId = IdGenerator.generate("M").getId();


        StoredTaskInstance storedTaskInstance = happyFlowTaskSetup(MERCHANT,entityId,false);

        Date date = new Date();
        long currentTime = date.getTime();


        when(taskESRepository.get(storedTaskInstance.getTaskInstanceId())).thenReturn(DiscoveryTaskInstance.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .actionId(storedTaskInstance.getActionId())
                .dueDate(currentTime+10000)
                .taskState(BOUNDED_ASSIGNED)
                .assignedTo(TSM_ID).build());
        TaskSchedulingPayload taskSchedulingPayload = TaskSchedulingPayload.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .rescheduleAt(currentTime-1000)
                .scheduleType(TaskSchedulingType.SCHEDULE)
                .build();
        taskInstanceManagementService.rescheduleTask(taskSchedulingPayload, TSM_ID);
    }

    @Test(expected = LegionException.class)
    public void invalidTaskInstanceId() {
        TaskSchedulingPayload taskSchedulingPayload = TaskSchedulingPayload.builder()
                .taskInstanceId("invalid")
                .scheduleType(TaskSchedulingType.CANCEL)
                .build();
        taskInstanceManagementService.rescheduleTask(taskSchedulingPayload, TSM_ID);
    }

    @Test(expected = Test.None.class)
    public void createTaskForClient() {
            StoredTaskDefinition definition = StoredTaskDefinition.builder()
                    .taskDefinitionId("Random_definition")
                    .actionId("MERCHANT_KYC_VERIFICATION")
                    .namespace(Namespace.MERCHANT_ONBOARDING)
                    .name("FIRST TASK DEFINITION")
                    .createdBy("SaranshUser")
                    .priority(Priority.P1)
                    .build();

        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1L))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN1")
                .build();

        when(validationService.validateAndGetTaskDefinition("Random_definition")).thenReturn(TaskDefinitionTransformationUtils.toTaskDefinitionInstance(definition));
        when(validationService.validateAndGetCampaign("CAMPAIGN1")).thenReturn(expectedOutput);

        when(profileCRUDService.getAgentProfile("Saransh_user123")).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).managerId("SARANSH_TSM").build());
        com.phonepe.merchant.gladius.models.entitystore.StoreEntity expectedResponse = DiscoveryTestUtils.getStoreEntity();
        validationService.validateEntity(expectedResponse.getEntityId(), STORE);
        validationService.validateBoundedAssignmentWithMissingAssignee(false, "Saransh_user123");

        ClientTaskCreateAndAssignRequest request1 = ClientTaskCreateAndAssignRequest.builder()
                .assigneeId("Saransh_user123")
                .campaignId("CAMPAIGN1")
                .entityId("SARANSH_KIRANA1")
                .taskDefinitionId("Random_definition")
                .createdBy("ACE")
                .createTaskForManager(true)
                .markAvailable(true)
                .taskInstanceMeta(TaskInstanceMeta.builder().taskMetaAsMap(new HashMap<>()).build())
                .forceTaskCreationRequest(false)
                .build();
        TaskCreateRequest taskCreateRequest = TaskCreateRequest.builder()
                .taskInstance(CreateTaskInstanceRequest.builder()
                                .campaignId("CAMPAIGN1")
                                .taskDefinitionId("Random_definition")
                                .createdBy("ACE")
                                .build())
                .markAvailable(false)
                .build();

        String instanceId = IdGenerator.generate("T").getId();
        StoredTaskInstance createdTask = StoredTaskInstance.builder()
                .entityId("SARANSH_KIRANA1")
                .entityType(STORE)
                .curActor("SARANSH_TSM")
                .curState(BOUNDED_ASSIGNED)
                .taskInstanceId(instanceId)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .campaignId("CAMPAIGN1")
                .taskDefinitionId("Random_definition")
                .actionId("MERCHANT_KYC_VERIFICATION")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();
        taskInstanceRepository.save(createdTask);
        when(taskManagementService.command(
                any(TaskCreateRequest.class), any()))
                .thenReturn(createdTask);
        when(transitionValidator.getDuplicateTasks(any(EntityType.class), any(String.class), any(String.class), any(Campaign.class))).thenReturn(anyList());
        clientTaskService.createAndAssignClientTask(request1);
    }

    @Test(expected = LegionException.class)
    public void createTaskForClientWithDeletion() {
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskDefinitionId("Random_definition1")
                .actionId("MERCHANT_KYC_VERIFICATION")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("FIRST TASK DEFINITION")
                .createdBy("SaranshUser")
                .priority(Priority.P1)
                .build();
        System.out.println(storedTaskDefinition.toString());
        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1L))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN99")
                .build();
        when(validationService.validateAndGetTaskDefinition("Random_definition1")).thenReturn(TaskDefinitionTransformationUtils.toTaskDefinitionInstance(storedTaskDefinition));
        when(validationService.validateAndGetCampaign("CAMPAIGN99")).thenReturn(expectedOutput);


        when(profileCRUDService.getAgentProfile("Saransh_user123")).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).managerId("SARANSH_TSM").build());
        com.phonepe.merchant.gladius.models.entitystore.StoreEntity expectedResponse = DiscoveryTestUtils.getStoreEntity();
        validationService.validateEntity(expectedResponse.getEntityId(), STORE);
        validationService.validateBoundedAssignmentWithMissingAssignee(false, "Saransh_user123");

        ClientTaskCreateAndAssignRequest request1 = ClientTaskCreateAndAssignRequest.builder()
                .assigneeId("Saransh_user123")
                .campaignId("CAMPAIGN99")
                .entityId("SARANSH_KIRANA")
                .taskDefinitionId("Random_definition1")
                .createdBy("ACE")
                .createTaskForManager(true)
                .markAvailable(false)
                .forceTaskCreationRequest(false)
                .build();

        when(transitionValidator.getDuplicateTasks(any(), any(), any(), any())).thenReturn(List.of(DiscoveryTaskInstance.builder()
                .taskInstanceId("TID").build()));
        clientTaskService.createAndAssignClientTask(request1);
    }

    @Test
    public void createTaskForClientWithDeletionSuccess() {
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskDefinitionId("Random_definition1")
                .actionId("MERCHANT_KYC_VERIFICATION")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("FIRST TASK DEFINITION")
                .createdBy("SaranshUser")
                .priority(Priority.P1)
                .build();

        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1L))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN99")
                .build();
        when(validationService.validateAndGetTaskDefinition("Random_definition1")).thenReturn(TaskDefinitionTransformationUtils.toTaskDefinitionInstance(storedTaskDefinition));
        when(validationService.validateAndGetCampaign("CAMPAIGN99")).thenReturn(expectedOutput);


        when(profileCRUDService.getAgentProfile("Saransh_user123")).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).managerId("SARANSH_TSM").build());
        com.phonepe.merchant.gladius.models.entitystore.StoreEntity expectedResponse = DiscoveryTestUtils.getStoreEntity();
        validationService.validateEntity(expectedResponse.getEntityId(), STORE);
        validationService.validateBoundedAssignmentWithMissingAssignee(false, "Saransh_user123");

        ClientTaskCreateAndAssignRequest request1 = ClientTaskCreateAndAssignRequest.builder()
                .assigneeId("Saransh_user123")
                .campaignId("CAMPAIGN99")
                .entityId("SARANSH_KIRANA")
                .taskDefinitionId("Random_definition1")
                .createdBy("ACE")
                .createTaskForManager(true)
                .markAvailable(false)
                .forceTaskCreationRequest(true)
                .build();

        String instanceId = IdGenerator.generate("T").getId();
        StoredTaskInstance createdTask = StoredTaskInstance.builder()
                .entityId("SARANSH_KIRANA")
                .entityType(STORE)
                .curActor("SARANSH_TSM")
                .curState(BOUNDED_ASSIGNED)
                .taskInstanceId(instanceId)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .campaignId("CAMPAIGN1")
                .taskDefinitionId("Random_definition")
                .actionId("MERCHANT_KYC_VERIFICATION")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .build();
        taskInstanceRepository.save(createdTask);
        when(taskManagementService.command(any(TaskAssignRequest.class), any())).thenReturn(createdTask);
        when(taskManagementService.deleteTask(any())).thenReturn(createdTask);
        when(transitionValidator.getDuplicateTasks(any(), any(), any(), any())).thenReturn(List.of(DiscoveryTaskInstance.builder()
                .taskInstanceId(instanceId).build()));

        clientTaskService.createAndAssignClientTask(request1);
        verify(taskManagementService, times(2)).deleteTask(any()); //TODO to check with 2 it should be 1
    }

    @Test(expected = Test.None.class)
    public void deleteTaskForClient() {
        String instanceId = IdGenerator.generate("T").getId();
        StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
                .actor("ACTOR")
                .taskInstance(StoredTaskInstance.builder()
                        .taskInstanceId(instanceId)
                        .build())
                .toState(LegionTaskStateMachineState.DELETED)
                .fromState(AVAILABLE)
                .event(LegionTaskStateMachineEvent.MARK_TASK_DELETED)
                .transitionId(1)
                .build();
        StoredTaskInstance storedTask = StoredTaskInstance.builder().entityType(STORE).entityId("PRABH_KIRANA").taskDefinitionId("Random_definition").taskInstanceId(instanceId).namespace(Namespace.MERCHANT_ONBOARDING).curState(LegionTaskStateMachineState.DELETED).actionId("MERCHANT_KYC_VERIFICATION").updatedBy("saransh").createdBy("saransh").build();

        taskInstanceRepository.save(storedTask);
        taskTransitionRepository.save(storedTaskTransition);

        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1L))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN12")
                .build();
        campaignService.save(expectedOutput);

        com.phonepe.merchant.gladius.models.entitystore.StoreEntity expectedResponse = DiscoveryTestUtils.getStoreEntity();
        validationService.validateEntity(expectedResponse.getEntityId(), STORE);
        validationService.validateBoundedAssignmentWithMissingAssignee(false, "Saransh_user123");

        ClientTaskDeleteRequest request1 = ClientTaskDeleteRequest.builder().deletedBy("prabh").taskInstanceId(instanceId).reason("reason_abc").build();

        when(taskManagementService.deleteTask(any(ClientTaskDeleteRequest.class))).thenReturn(storedTask);
        clientTaskService.deleteClientTask(request1);
    }

    @Test(expected = Test.None.class)
    public void verifyTaskForClient() {
        String instanceId = IdGenerator.generate("T").getId();
        StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
                .actor("ACTOR")
                .taskInstance(StoredTaskInstance.builder()
                        .taskInstanceId(instanceId)
                        .build())
                .toState(LegionTaskStateMachineState.VERIFICATION_SUCCESS)
                .fromState(COMPLETED)
                .event(LegionTaskStateMachineEvent.MANUAL_VERIFICATION)
                .transitionId(1)
                .build();
        StoredTaskInstance storedTask = StoredTaskInstance.builder().entityType(STORE).entityId("PRABH_KIRANA").taskDefinitionId("Random_definition").taskInstanceId(instanceId).namespace(Namespace.MERCHANT_ONBOARDING).curState(LegionTaskStateMachineState.VERIFICATION_SUCCESS).actionId("MERCHANT_KYC_VERIFICATION").createdBy("Saransh").updatedBy("Saransh").build();

        taskInstanceRepository.save(storedTask);
        taskTransitionRepository.save(storedTaskTransition);

        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1L))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN99")
                .build();
        campaignService.save(expectedOutput);

        com.phonepe.merchant.gladius.models.entitystore.StoreEntity expectedResponse = DiscoveryTestUtils.getStoreEntity();
        validationService.validateEntity(expectedResponse.getEntityId(), STORE);
        validationService.validateBoundedAssignmentWithMissingAssignee(false, "Saransh_user123");


        TaskManualVerificationRequest request1 = TaskManualVerificationRequest.builder().build();

        when(taskManagementService.verifyTask(any(TaskManualVerificationRequest.class))).thenReturn(storedTask);
        clientTaskService.verifyClientTask(request1);
    }

    @Test(expected = LegionException.class)
    public void verifyTaskForClientException() {
        when(taskManagementService.verifyTask(any(TaskManualVerificationRequest.class))).thenThrow(LegionException.class);
        TaskManualVerificationRequest request1 = TaskManualVerificationRequest.builder().build();
        clientTaskService.verifyClientTask(request1);
    }

    @Test
    public void clientTaskCreateBadRequest() {
        StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
                .taskDefinitionId("xyzabcdef")
                .actionId("MERCHANT_KYC_VERIFICATION_123")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("FIRST TASK DEFINITION")
                .createdBy("SARANSH99")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTask);

        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1L))
                .updatedBy("SARANSH99")
                .createdBy("SARANSH99")
                .campaignId("viscous_campaign12399")
                .build();
        campaignService.save(expectedOutput);

        when(profileCRUDService.getAgentProfile("SARANSH99")).thenReturn(AgentProfile.builder().agentType(AgentType.AGENT).build());
        validationService.validateEntity(IdGenerator.generate("MS").getId(), STORE);
        when(validationService.validateAndGetTaskDefinition("xyzabcdef")).thenReturn(TaskDefinitionTransformationUtils.toTaskDefinitionInstance(storedTask));

        ClientTaskCreateAndAssignRequest request = ClientTaskCreateAndAssignRequest.builder()
                .markAvailable(false)
                .createTaskForManager(true)
                .taskDefinitionId("xyzabcdef")
                .assigneeId("SARANSH99")
                .createdBy("SARANSH99")
                .campaignId("viscous_campaign12399")
                .build();
        try{
            clientTaskService.createAndAssignClientTask(request);
        } catch (LegionException exception){
            Assert.assertEquals(LegionTaskErrorCode.MANAGER_NOT_FOUND, exception.getErrorCode());
        }

    }
}
