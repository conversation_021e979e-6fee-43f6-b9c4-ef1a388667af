package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.merchant.gladius.models.tasks.request.CampaignFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.legion.tasks.services.CampaignService;
import com.phonepe.models.response.GenericResponse;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CampaignResourceTest {

    private static CampaignService campaignService;
    private static CampaignResource campaignResource;

    @BeforeClass
    public static void init() {
        campaignService = mock(CampaignService.class);
        campaignResource = new CampaignResource(campaignService);
    }

    @Test
    public void fetch() {
        //arrange
        String campaignId = "campaignId";
        Campaign expectedOutput = Campaign.builder().build();
        when(campaignService.get(CampaignFetchByIdRequest.builder()
                .campaignId(campaignId)
                .build())).thenReturn(expectedOutput);

        //call
        GenericResponse<Campaign> actualOutput = campaignResource.fetch(null,campaignId);

        //assert
        Assert.assertTrue(actualOutput.isSuccess());
        Assert.assertEquals(expectedOutput,actualOutput.getData());
    }

    @Test
    public void create() {
        //arrange
        Campaign expectedOutput = Campaign.builder().build();
        when(campaignService.save(expectedOutput)).thenReturn(expectedOutput);

        //call
        GenericResponse<Campaign> actualOutput = campaignResource.create(null,expectedOutput,getUserDetails(), null, null);

        //assert
        Assert.assertTrue(actualOutput.isSuccess());
        Assert.assertEquals(expectedOutput,actualOutput.getData());
    }

}
