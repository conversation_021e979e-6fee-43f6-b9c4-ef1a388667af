package com.phonepe.merchant.legion.tasks.repository.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import io.appform.dropwizard.sharding.dao.LookupDao;
import org.hibernate.Session;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HotspotConfigRepositoryImplTest {

    @Mock
    private Session session;

    @Mock
    private LookupDao<StoredHotspotConfig> lookupDao;

    @Mock
    private FoxtrotEventIngestionService eventIngestionService;

    @InjectMocks
    private HotspotConfigRepositoryImpl hotspotConfigRepository;

    private StoredHotspotConfig hotspotConfig;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        hotspotConfigRepository = new HotspotConfigRepositoryImpl(lookupDao, eventIngestionService);
        hotspotConfig = new StoredHotspotConfig();
        hotspotConfig.setHotspotType("testType");
        hotspotConfig.setCategory("Test Type");
        hotspotConfig.setActive(true);
        hotspotConfig.setUpdatedBy("testActor");
    }


    @Test
    void testSaveOrUpdateHotspot_Success() {
        StoredHotspotConfig config = new StoredHotspotConfig();
        config.setHotspotType("HOTSPOT_1");

        when(lookupDao.runInSession(eq("HOTSPOT_1"), any())).thenAnswer(invocation -> {
            Function<Session, StoredHotspotConfig> fn = invocation.getArgument(1);
            return fn.apply(session);
        });

        StoredHotspotConfig result = hotspotConfigRepository.saveOrUpdate(config);

        assertEquals("HOTSPOT_1", result.getHotspotType());
    }

    @Test
    public void testGetHotspotConfig_Success() throws Exception {
        when(lookupDao.get(anyString())).thenReturn(Optional.of(hotspotConfig));
        Optional<StoredHotspotConfig> retrievedHotspotConfig = hotspotConfigRepository.get("testType");
        assertTrue(retrievedHotspotConfig.isPresent());
        assertEquals("testType", retrievedHotspotConfig.get().getHotspotType());
        verify(lookupDao, times(1)).get("testType");
    }

    @Test
    public void testGetHotspotConfig_NotFound() throws Exception {
        when(lookupDao.get(anyString())).thenReturn(Optional.empty());
        Optional<StoredHotspotConfig> retrievedHotspotConfig = hotspotConfigRepository.get("testType");

        assertFalse(retrievedHotspotConfig.isPresent());
        verify(lookupDao, times(1)).get("testType");
    }

    @Test
    public void testDeactivateHotspotConfig_Success() throws Exception {
        when(lookupDao.update(anyString(), any())).thenReturn(true);
        when(lookupDao.get(anyString())).thenReturn(Optional.of(StoredHotspotConfig.builder().build()));
        hotspotConfigRepository.deactivate("testActor", "testType");
        verify(lookupDao, times(1)).update(eq("testType"), any());
    }

    @Test
    public void testGetAllActiveHotspotConfigs_Success() {
        ObjectMapper objectMapper = new ObjectMapper();
        SerDe.init(objectMapper);
        when(lookupDao.scatterGather(any())).thenReturn(List.of(StoredHotspotConfig.builder()
                .hotspotType("SS").build()));
        List<StoredHotspotConfig> hotspotConfigs = hotspotConfigRepository.getAllActiveHotspotConfigs();
        Assertions.assertEquals(1, hotspotConfigs.size());
        verify(lookupDao, times(1)).scatterGather(any());
    }

}
