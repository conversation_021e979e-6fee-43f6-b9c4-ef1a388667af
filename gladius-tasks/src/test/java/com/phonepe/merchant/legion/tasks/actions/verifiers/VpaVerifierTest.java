package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VpaVerifierConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class VpaVerifierTest {

    private IntelService intelService;
    private VpaFromTaskMetaVerifier verifier;
    private VpaVerifierConfig config;

    @BeforeEach
    void setUp() {
        intelService = Mockito.mock(IntelService.class);
        verifier = new VpaFromTaskMetaVerifier(intelService);
        config = new VpaVerifierConfig();
    }

    @Test
    void testValidate_withPhoneNumberEntityType_shouldPass() {
        assertDoesNotThrow(() -> verifier.validate(EntityType.PHONE_NUMBER, config));
    }

    @Test
    void testValidate_withInvalidEntityType_shouldThrow() {
        LegionException exception = assertThrows(LegionException.class,
                () -> verifier.validate(EntityType.STORE, config));

        assertTrue(exception.getMessage().contains("STORE cannot be used with this verifier"));
    }

    @Test
    void testVerify_shouldReturnVerifiedTrueAndPreserveContext() {
        Map<String, Object> context = Map.of("key", "value");
        TaskCompleteRequest request = new TaskCompleteRequest();

        VerifierResponse response = verifier.verify(request, config, context);

        assertTrue(response.getVerified());
        assertEquals("value", response.getContext().get("key"));
    }

    @Test
    void testValidateTaskCreation_withNullMeta_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setTaskInstanceMeta(null);

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }

    @Test
    void testValidateTaskCreation_withEmptyMeta_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        request.setTaskInstanceMeta(new TaskInstanceMeta(0, Collections.emptyList(), Collections.emptyMap()));

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertFalse(result);
    }

    @Test
    void testValidateTaskCreation_withInvalidVpa_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();

        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.COMP_VPA)
                .value(List.of("invalid-vpa@"))
                .displayInformation(true)
                .build();

        TaskInstanceMeta metaWrapper = new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap());
        request.setTaskInstanceMeta(metaWrapper);

        assertThrows(LegionException.class,
                () -> verifier.validateTaskCreation(request, new TaskActionInstance()));
    }

    @Test
    void testValidateTaskCreation_withValidVpa_shouldSetLocationAndReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();

        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.COMP_VPA)
                .value(List.of("validvpa@bank"))
                .displayInformation(true)
                .build();

        TaskInstanceMeta metaWrapper = new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap());
        request.setTaskInstanceMeta(metaWrapper);

        CompetitionQrResponse qrResponse = new CompetitionQrResponse();
        qrResponse.setLatitude(12.34f);
        qrResponse.setLongitude(56.78f);
        when(intelService.getVpaDetails("validvpa@bank")).thenReturn(qrResponse);

        boolean result = verifier.validateTaskCreation(request, new TaskActionInstance());

        assertTrue(result);
        EsLocationRequest location = request.getTransactionLocation();
        assertNotNull(location);
        assertEquals(12.34f, location.getLat());
        assertEquals(56.78f, location.getLon());
        verify(intelService, times(2)).getVpaDetails("validvpa@bank");
    }

    @Test
    void testValidateTaskCreation_withIntelServiceFailure_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.COMP_VPA)
                .value(List.of("validvpa@bank"))
                .build();
        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        when(intelService.getVpaDetails("validvpa@bank")).thenThrow(new RuntimeException("Service failure"));

        LegionException ex = assertThrows(LegionException.class,
                () -> verifier.validateTaskCreation(request, new TaskActionInstance()));

        assertTrue(ex.getMessage().contains("Invalid comp-vpa mapped: validvpa@bank"));
    }

    @Test
    void testValidateTaskCreation_withMultipleVpas_someFailing_shouldThrow() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.COMP_VPA)
                .value(List.of("vpa1@bank", "vpa2@bank"))
                .build();
        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        when(intelService.getVpaDetails("vpa1@bank")).thenReturn(new CompetitionQrResponse());
        when(intelService.getVpaDetails("vpa2@bank")).thenThrow(new RuntimeException("not found"));

        LegionException ex = assertThrows(LegionException.class,
                () -> verifier.validateTaskCreation(request, new TaskActionInstance()));

        assertTrue(ex.getMessage().contains("vpa2@bank"));
    }

    @Test
    void testValidateTaskCreation_withUnsupportedMetaValue_shouldReturnTrue() {
        CreateTaskInstanceRequest request = new CreateTaskInstanceRequest();
        TaskMetaInformation meta = TaskMetaInformation.builder()
                .type(TaskMetaType.COMP_VPA)
                .value(12345) // invalid type
                .build();
        request.setTaskInstanceMeta(new TaskInstanceMeta(0, List.of(meta), Collections.emptyMap()));

        // meta.getValue() returns empty list
        assertFalse(verifier.validateTaskCreation(request, new TaskActionInstance()));
    }

}