package com.phonepe.merchant.legion.tasks.converters;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.enums.ChangeType;
import com.phonepe.merchant.legion.core.config.AuditConfig;
import com.phonepe.merchant.legion.core.utils.SerDe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AuditConverterTest {

    @Mock private AuditConfig auditConfig;
    private TestAuditConverter testAuditConverter;
    private String agentId = "A1";

    private Date updatedAt = new Date();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        testAuditConverter = new TestAuditConverter(auditConfig);
        SerDe.init(new ObjectMapper());
    }

    @Test
    void testConvert_NoInitialAndFinalInstance() {
        String referenceId = "taskInstanceId";
        List<TestAuditDTO> result = testAuditConverter.convert(referenceId, null, null, agentId, updatedAt);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvert_NoChange() {
        Object instance = new SampleInstance("fieldValue");
        String referenceId = "taskInstanceId";
        setupAuditConfigForClass(SampleInstance.class, "$.field", "field");

        List<TestAuditDTO> result = testAuditConverter.convert(referenceId, instance, instance, agentId, updatedAt);

        assertTrue(result.isEmpty());
    }

    @Test
    void testConvert_FieldAdded() {
        Object initialInstance = null;
        Object finalInstance = new SampleInstance("newValue");
        String referenceId = "taskInstanceId";
        setupAuditConfigForClass(SampleInstance.class, "$.field", "field");

        List<TestAuditDTO> result = testAuditConverter.convert(referenceId, initialInstance, finalInstance, agentId, updatedAt);

        assertEquals(1, result.size());
        assertEquals(ChangeType.ADDED, result.get(0).getChangeType());
    }

    @Test
    void testConvert_FieldDeleted() {
        Object initialInstance = new SampleInstance("initialValue");
        Object finalInstance = null;
        String referenceId = "taskInstanceId";
        setupAuditConfigForClass(SampleInstance.class, "$.field", "field");

        List<TestAuditDTO> result = testAuditConverter.convert(referenceId, initialInstance, finalInstance, agentId, updatedAt);

        assertEquals(1, result.size());
        assertEquals(ChangeType.DELETED, result.get(0).getChangeType());
    }

    @Test
    void testConvert_FieldModified() {
        Object initialInstance = new SampleInstance("initialValue");
        Object finalInstance = new SampleInstance("modifiedValue");
        String referenceId = "taskInstanceId";
        setupAuditConfigForClass(SampleInstance.class, "$.field", "field");

        List<TestAuditDTO> result = testAuditConverter.convert(referenceId, initialInstance, finalInstance, agentId, updatedAt);

        assertEquals(1, result.size());
        assertEquals(ChangeType.MODIFIED, result.get(0).getChangeType());
    }

    private void setupAuditConfigForClass(Class<?> clazz, String jsonPath, String fieldName) {
        // Create an instance of AuditFieldConfig and set its fields
        AuditConfig.AuditFieldConfig fieldConfig = new AuditConfig.AuditFieldConfig();
        fieldConfig.setFieldName(fieldName);
        fieldConfig.setJsonPath(jsonPath);

        // Mock the configuration to return the field configuration for the class name
        when(auditConfig.getAuditedFields())
                .thenReturn(Map.of(clazz.getSimpleName(), List.of(fieldConfig)));
    }

    // Mock DTO and Instance classes for testing
    private static class SampleInstance {
        private final String field;
        SampleInstance(String field) { this.field = field; }
        public String getField() { return field; }
    }

    private static class TestAuditDTO {
        private final ChangeType changeType;
        TestAuditDTO(ChangeType changeType) { this.changeType = changeType; }
        public ChangeType getChangeType() { return changeType; }
    }

    private static class TestAuditConverter extends AuditConverter<TestAuditDTO> {
        public TestAuditConverter(AuditConfig auditConfig) { super(auditConfig); }
        @Override
        TestAuditDTO createAuditResponse(String referenceId, ChangeType change, String fieldName, Object initialFieldValue, Object finalFieldValue, Date updatedAt, String profile) {
            return new TestAuditDTO(change);
        }
    }
}
