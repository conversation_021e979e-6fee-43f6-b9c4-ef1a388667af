package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.CreateCommentRequest;
import com.phonepe.merchant.gladius.models.tasks.response.CommentsOnTaskResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.repository.CommentsOnTaskRepository;
import com.phonepe.merchant.legion.tasks.services.impl.CommentsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

public class CommentsServiceTest {

    @Mock
    private CommentsOnTaskRepository commentsOnTaskRepository;
    @Mock
    private LegionService legionService;

    private CommentsService commentsService;

    private FoxtrotEventExecutor foxtrotEventExecutor;

    @BeforeEach
    public void setup() {
        commentsOnTaskRepository = mock(CommentsOnTaskRepository.class);
        legionService = mock(LegionService.class);
        foxtrotEventExecutor = mock(FoxtrotEventExecutor.class);
        doNothing().when(foxtrotEventExecutor).ingest(any());
        commentsService = new CommentsServiceImpl(legionService, foxtrotEventExecutor, commentsOnTaskRepository);
    }

    @Test
    public void testCreateComment() {
        // Mock request data
        CreateCommentRequest request = CreateCommentRequest.builder()
                .taskInstanceId("task-123")
                .content("This is a test comment.")
                .build();

        String actor = "user-456";
        String generatedCommentId = "comment-789";

        // Mock repository behavior
        when(commentsOnTaskRepository.generateCommentId("task-123")).thenReturn(generatedCommentId);
        StoredCommentsOnTask storedComment = StoredCommentsOnTask.builder()
                .commentId(generatedCommentId)
                .taskInstanceId("task-123")
                .content("This is a test comment.")
                .createdBy(actor)
                .createdAt(new Date())
                .taskInstance(StoredTaskInstance.builder().build())
                .build();
        when(commentsOnTaskRepository.save(any(StoredCommentsOnTask.class))).thenReturn(storedComment);

        // Execute the method under test
        StoredCommentsOnTask result = commentsService.createComment(request, actor);

        // Verify behavior and assertions
        assertNotNull(result);
        assertEquals(generatedCommentId, result.getCommentId());
        assertEquals("task-123", result.getTaskInstanceId());
        assertEquals("This is a test comment.", result.getContent());
        assertEquals(actor, result.getCreatedBy());
        verify(commentsOnTaskRepository).generateCommentId("task-123");
        verify(commentsOnTaskRepository).save(any(StoredCommentsOnTask.class));
    }


    @Test
    public void testCreateCommentException() {
        // Mock request data
        CreateCommentRequest request = CreateCommentRequest.builder()
                .taskInstanceId("task-123")
                .content("This is a test comment.")
                .build();
        String actor = "user-456";
        // Mock repository behavior
        when(commentsOnTaskRepository.generateCommentId("task-123")).thenThrow(IllegalArgumentException.class);
        // Execute the method under test
        assertThrows(IllegalArgumentException.class, () -> commentsService.createComment(request, actor));
    }

    @Test
    public void testGetByTaskInstanceId() {
        // Mock repository response
        List<StoredCommentsOnTask> storedComments = Arrays.asList(
                StoredCommentsOnTask.builder()
                        .commentId("comment-789")
                        .taskInstanceId("task-123")
                        .content("First comment")
                        .createdBy("user-456")
                        .createdAt(new Date())
                        .build(),
                StoredCommentsOnTask.builder()
                        .commentId("comment-790")
                        .taskInstanceId("task-123")
                        .content("Second comment")
                        .createdBy("user-457")
                        .createdAt(new Date())
                        .build()
        );
        when(commentsOnTaskRepository.getFromTaskInstanceId("task-123", Sorter.CREATED_AT , 0, 10)).thenReturn(storedComments);

        // Mock LegionService behavior
        Map<String, AgentProfile> agentProfiles = new HashMap<>();
        agentProfiles.put("user-456", AgentProfile.builder().agentType(AgentType.AGENT).name("John Doe").build());
        agentProfiles.put("user-457", AgentProfile.builder().agentType(AgentType.TSE).name("Jane Doe").build());

        when(legionService.getAgentProfile("user-456")).thenReturn(agentProfiles.get("user-456"));
        when(legionService.getAgentProfile("user-457")).thenReturn(agentProfiles.get("user-457"));

        // Execute the method under test
        List<CommentsOnTaskResponse> responses = commentsService.getByTaskInstanceId("task-123", Sorter.CREATED_AT , 0, 10);

        // Verify behavior and assertions
        assertNotNull(responses);
        assertEquals(2, responses.size());

        // Check first response
        CommentsOnTaskResponse response1 = responses.get(0);
        assertEquals("comment-789", response1.getCommentId());
        assertEquals("First comment", response1.getContent());
        assertEquals("user-456", response1.getCommenterId());
        assertEquals("John Doe", response1.getCommenterName());
        assertEquals(AgentType.AGENT, response1.getCommenterAgentType());

        // Check second response
        CommentsOnTaskResponse response2 = responses.get(1);
        assertEquals("comment-790", response2.getCommentId());
        assertEquals("Second comment", response2.getContent());
        assertEquals("user-457", response2.getCommenterId());
        assertEquals("Jane Doe", response2.getCommenterName());
        assertEquals(AgentType.TSE, response2.getCommenterAgentType());

        verify(commentsOnTaskRepository).getFromTaskInstanceId("task-123",Sorter.CREATED_AT , 0, 10);
        verify(legionService, times(2)).getAgentProfile(anyString());
    }

    @Test
    public void testGetByTaskInstanceIdEmptyList() {
        // Mock repository response
        List<StoredCommentsOnTask> storedComments = Arrays.asList();
        when(commentsOnTaskRepository.getFromTaskInstanceId("task-123", Sorter.CREATED_AT , 0, 10)).thenReturn(storedComments);

        List<CommentsOnTaskResponse> responses = commentsService.getByTaskInstanceId("task-123", Sorter.CREATED_AT , 0, 10);
        // Verify behavior and assertions
        assertNotNull(responses);
        assertEquals(0, responses.size());
    }

    @Test
    public void testGetByTaskInstanceIdError() {
        when(commentsOnTaskRepository.getFromTaskInstanceId("task-123", Sorter.CREATED_AT, 0, 10)).thenThrow(NullPointerException.class);
        assertThrows(NullPointerException.class, () -> commentsService.getByTaskInstanceId("task-123",Sorter.CREATED_AT , 0, 10));
    }
}

