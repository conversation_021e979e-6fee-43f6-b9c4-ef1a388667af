package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.request.CampaignFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.services.impl.CampaignServiceImpl;
import edu.emory.mathcs.backport.java.util.Collections;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.Date;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MAX_RELATIVE_EXPIRY_FROM_TODAY_IN_DAYS;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MAX_START_DATE_FROM_TODAY_IN_DAYS;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.DAY_IN_MS;
import static org.mockito.Mockito.mock;

public class CampaignServiceTest extends LegionTaskBaseTest {

    private static CampaignService campaignService;

    @BeforeClass
    public static void init() {
        campaignService = new CampaignServiceImpl(campaignRepository, eventExecutor, mock(TagService.class));
    }

    @Test(expected = LegionException.class)
    public void saveErrorStartDateMoreThanEndDate() {
        Campaign expectedOutput = Campaign.builder()
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(0L)
                .startDate(new Date(1L))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN1")
                .tags(Collections.emptySet())
                .build();
        campaignService.save(expectedOutput);
    }

    @Test(expected = LegionException.class)
    public void saveErrorStartDateMoreThanAcceptable() {
        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(1 + System.currentTimeMillis() + DAY_IN_MS * (1+MAX_START_DATE_FROM_TODAY_IN_DAYS)))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN1")
                .build();
        campaignService.save(expectedOutput);
    }

    @Test(expected = LegionException.class)
    public void saveErrorEndDateMinusStartDateGreaterThanThreshold() {
        Campaign expectedOutput = Campaign.builder()
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(0L)
                .startDate(new Date(DAY_IN_MS * MAX_RELATIVE_EXPIRY_FROM_TODAY_IN_DAYS))
                .updatedBy("me")
                .createdBy("me")
                .campaignId("CAMPAIGN1")
                .build();
        campaignService.save(expectedOutput);
    }

    @Test(expected = LegionException.class)
    public void getErrorInvalidCampaign() {
        campaignService.get(CampaignFetchByIdRequest.builder()
                .campaignId("INVALID_CAMPAIGN_ID")
                .build());
    }

    @Test
    public void saveAndGet() {
        //arrange
        String campaignId = IdGenerator.generate("C").getId();
        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(System.currentTimeMillis()))
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(System.currentTimeMillis() + DAY_IN_MS * (MAX_RELATIVE_EXPIRY_FROM_TODAY_IN_DAYS-1))
                .updatedBy("me")
                .createdBy("me")
                .campaignId(campaignId)
                .tags(Collections.emptySet())
                .build();
        campaignService.save(expectedOutput);

        //call
        Campaign actualOutput = campaignService.get(CampaignFetchByIdRequest.builder()
                .campaignId(campaignId)
                .build());

        //assert
        Assert.assertEquals(expectedOutput.getCampaignId(),actualOutput.getCampaignId());
    }

    @Test(expected = LegionException.class)
    public void saveDuplicateCampaign() {
        //arrange
        String campaignId = IdGenerator.generate("C").getId();
        Campaign expectedOutput = Campaign.builder()
                .startDate(new Date(System.currentTimeMillis()))
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(System.currentTimeMillis() + DAY_IN_MS * (MAX_RELATIVE_EXPIRY_FROM_TODAY_IN_DAYS-1))
                .updatedBy("me")
                .createdBy("me")
                .campaignId(campaignId)
                .tags(Collections.emptySet())
                .build();
        campaignService.save(expectedOutput);

        //call
        campaignService.save(expectedOutput);
    }

}
