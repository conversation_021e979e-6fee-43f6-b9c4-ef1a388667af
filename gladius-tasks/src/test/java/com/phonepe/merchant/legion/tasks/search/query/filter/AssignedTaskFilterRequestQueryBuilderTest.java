package com.phonepe.merchant.legion.tasks.search.query.filter;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.NotEqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.GreaterEqualsNumberFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.LesserEqualsNumberFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.models.merchants.tasks.EntityType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;

import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

public class AssignedTaskFilterRequestQueryBuilderTest extends LegionTaskBaseTest {

    private final long epoch;
    private final AssignedTaskFilterRequestQueryBuilder filterGenerator;

    public AssignedTaskFilterRequestQueryBuilderTest() {
        this.epoch = 1234567890L;
        this.filterGenerator
                = new AssignedTaskFilterRequestQueryBuilder(Clock.fixed(Instant.ofEpochMilli(epoch), ZoneId.systemDefault()));
    }

    @Test
    public void test() {
        AssignedLocationTaskFilterRequest request = AssignedLocationTaskFilterRequest.builder()
                .location(EsLocationRequest.builder().lat(0.0).lon(0.0).build()).build();


        List<Filter> filters = List.of(
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true),
                new LesserEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.START_DATE, epoch),
                new GreaterEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, epoch),
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, "agent"),
                new NotEqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, EntityType.PHONE_NUMBER.name())
        );

        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        BoolQueryBuilder actualResponse = filterGenerator.enrichFilters("agent", request);
        Assertions.assertEquals(actualResponse, boolQueryBuilder);
    }

}
