package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.merchant.gladius.models.survey.request.FormConfigRequest;
import com.phonepe.merchant.gladius.models.survey.response.FormConfigInstance;
import com.phonepe.merchant.legion.core.models.BrickbatFeedbackFlatResponse;
import com.phonepe.merchant.legion.core.models.BrickbatFlatFeedbackRequest;
import com.phonepe.merchant.legion.core.models.BrickbatFormAndFeedbackResponse;
import com.phonepe.merchant.legion.core.models.BrickbatFormsRequestV2;
import com.phonepe.merchant.legion.core.models.BrickbatSubmitFeedbackRequest;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.BrickbatFormService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.SystemUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.brickbat.models.user.response.CreateFeedbackResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


public class BrickbatFormResourceV2Test {

    @Mock
    private BrickbatFormService brickbatFormService;

    @InjectMocks
    private BrickbatFormResourceV2 brickbatFormResourceV2;

    @Mock
    private AgentProfile agentProfile;

    private FormConfigRequest formConfigRequest;

    private BrickbatFormsRequestV2 brickbatFormsRequestV2;

    private BrickbatFlatFeedbackRequest brickbatFlatFeedbackRequest;

    private BrickbatSubmitFeedbackRequest brickbatSubmitFeedbackRequest;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateFormConfig_Success() {
        String actorId = "testActorId";
        com.phonepe.olympus.im.models.user.UserDetails userDetails = SystemUserDetails.builder().userId(actorId).build();
        ServiceUserPrincipal serviceUserPrincipal = ServiceUserPrincipal.builder().userAuthDetails(UserAuthDetails.builder().userDetails(userDetails).build()).build();
        FormConfigInstance formConfigInstance = mock(FormConfigInstance.class);
        when(brickbatFormService.createFormConfig(any(), any())).thenReturn(formConfigInstance);
        GenericResponse<FormConfigInstance> response = brickbatFormResourceV2.createFormConfig(serviceUserPrincipal, formConfigRequest);
        assertNotNull(response);
        assertTrue(response.isSuccess());
    }

    @Test
    public void testFetchFormConfigAndFeedback_Success() {
        String agentId = "testAgentId";
        com.phonepe.olympus.im.models.user.UserDetails userDetails = SystemUserDetails.builder().userId(agentId).build();
        ServiceUserPrincipal serviceUserPrincipal = ServiceUserPrincipal.builder().userAuthDetails(UserAuthDetails.builder().userDetails(userDetails).build()).build();
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        brickbatFormsRequestV2 = BrickbatFormsRequestV2.builder().clientContext(clientContext).build();
        BrickbatFormAndFeedbackResponse brickbatFormAndFeedbackResponse = mock(BrickbatFormAndFeedbackResponse.class);
        when(brickbatFormService.getBrickbatFormAndFeedback(any(), any(), any())).thenReturn(brickbatFormAndFeedbackResponse);
        GenericResponse<BrickbatFormAndFeedbackResponse> response = brickbatFormResourceV2.fetchFormConfigAndFeedback(serviceUserPrincipal,
                brickbatFormsRequestV2, agentProfile);
        assertNotNull(response);
        assertTrue(response.isSuccess());
    }

    @Test
    public void testFetchFlatMerchantStoreFeedback_Success() {
        com.phonepe.olympus.im.models.user.UserDetails userDetails = SystemUserDetails.builder().userId("testAgentId").build();
        ServiceUserPrincipal serviceUserPrincipal = ServiceUserPrincipal.builder().userAuthDetails(UserAuthDetails.builder().userDetails(userDetails).build()).build();
        BrickbatFeedbackFlatResponse brickbatFeedbackFlatResponse = mock(BrickbatFeedbackFlatResponse.class);
        when(brickbatFormService.getFlatMerchantStoreFeedback(any())).thenReturn(brickbatFeedbackFlatResponse);
        GenericResponse<BrickbatFeedbackFlatResponse> response = brickbatFormResourceV2.fetchFlatMerchantStoreFeedback(serviceUserPrincipal,
                brickbatFlatFeedbackRequest);
        assertNotNull(response);
        assertTrue(response.isSuccess());
    }

    @Test
    public void testCreateFeedbackAndAudit_Success() {
        String agentId = "testAgentId";
        com.phonepe.olympus.im.models.user.UserDetails userDetails = SystemUserDetails.builder().userId(agentId).build();
        ServiceUserPrincipal serviceUserPrincipal = ServiceUserPrincipal.builder().userAuthDetails(UserAuthDetails.builder().userDetails(userDetails).build()).build();
        CreateFeedbackResponse createFeedbackResponse = mock(CreateFeedbackResponse.class);
        when(brickbatFormService.createFeedbackAndAudit(any(), any())).thenReturn(createFeedbackResponse);
        GenericResponse<CreateFeedbackResponse> response = brickbatFormResourceV2.createFeedbackAndAudit(serviceUserPrincipal,
                brickbatSubmitFeedbackRequest, agentProfile);
        assertNotNull(response);
        assertTrue(response.isSuccess());
    }
}
