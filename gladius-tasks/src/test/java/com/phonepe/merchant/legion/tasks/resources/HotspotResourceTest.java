package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotAccessDetailsRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotCreateRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotSyncRequest;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotAccessDetailsResponse;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotDto;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.HotspotService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class HotspotResourceTest {

    @Mock
    private HotspotService hotspotService;

    @InjectMocks
    private HotspotResource hotspotResource;

    @Mock
    private ServiceUserPrincipal serviceUserPrincipal;

    @Mock
    private UserDetails userDetails;

    @Mock
    private AgentProfile agentProfile;

    private HotspotSyncRequest hotspotSyncRequest;


    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    public void testSyncHotspots_Success() {
        String actorId = "testActorId";
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        GenericResponse<Boolean> response = hotspotResource.syncHotspots(
               serviceUserPrincipal, agentProfile, hotspotSyncRequest);

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertTrue(response.isSuccess());
    }

    @Test
    public void testGetHotspots_Success() {
        String actorId = "testActorId";
        String sectorId = "testSectorId";
        String type = "testType";
        List<HotspotDto> hotspotDtoList = mock(List.class);
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        when(hotspotService.getStoredHotspots(actorId, sectorId, type)).thenReturn(hotspotDtoList);
        GenericResponse<List<HotspotDto>> response = hotspotResource.getHotspots(serviceUserPrincipal, agentProfile, sectorId, type);
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(hotspotDtoList, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotService, times(1)).getStoredHotspots(actorId, sectorId, type);
    }

    @Test
    public void testGetHotspotsWithoutActor_Success() {
        String actorId = "testActorId";
        String sectorId = "testSectorId";
        String type = "testType";
        List<HotspotDto> hotspotDtoList = mock(List.class);
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        when(hotspotService.getStoredHotspots(null, sectorId, type)).thenReturn(hotspotDtoList);
        GenericResponse<List<HotspotDto>> response = hotspotResource.getHotspotsWithoutActor(serviceUserPrincipal, agentProfile, sectorId, type);
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(hotspotDtoList, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotService, times(1)).getStoredHotspots(null, sectorId, type);
    }

    @Test
    public void testCreateHotspots_Success() {
        String actorId = "testActorId";
        String sectorId = "testSectorId";
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        when(hotspotService.createHotspots(anyString(), any(), anyBoolean())).thenReturn(List.of(HotspotDto.builder().build()));
        GenericResponse<List<HotspotDto>> response = hotspotResource.createHotspots(
                serviceUserPrincipal, agentProfile, sectorId, new HotspotCreateRequest());

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertTrue(response.isSuccess());
    }

    @Test
    public void testDeactivateHotspots_Success() {
        String actorId = "testActorId";
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);
        String hotspotId = "testHotspotId";
        String type = "testType";
        HotspotDto hotspotDto = HotspotDto.builder().build();
        when(hotspotService.deactivateHotspot(anyString(), anyString())).thenReturn(hotspotDto);

        GenericResponse<HotspotDto> response = hotspotResource.deactivateHotspot(
                serviceUserPrincipal, agentProfile, hotspotId);

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(hotspotDto, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotService, times(1)).deactivateHotspot(actorId, hotspotId);
    }

    @Test
    public void testGetHotspotAccessDetails_Success() {
        HotspotAccessDetailsResponse accessDetailsResponse = HotspotAccessDetailsResponse.builder().build();
        when(hotspotService.getHotspotAccessDetails(anyString(), any())).thenReturn(accessDetailsResponse);
        String actorId = "testActorId";
        when(AuthUserDetails.getLegionUserId(agentProfile, userDetails, serviceUserPrincipal)).thenReturn(actorId);

        GenericResponse<HotspotAccessDetailsResponse> response = hotspotResource.getHotspotAccessDetails(serviceUserPrincipal, agentProfile, new HotspotAccessDetailsRequest());

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(accessDetailsResponse, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotService, times(1)).getHotspotAccessDetails(anyString(), any());
    }


    @Test
    public void testGetHotspotsByLocation_Success() {
        double latitude =  12.0;
        double longitude = 12.0;
        List<HotspotDto> expected = new ArrayList<>();
        when(hotspotService.fetchHotspotsByLocation(latitude, longitude)).thenReturn(expected);
        GenericResponse<List<HotspotDto>> response = hotspotResource.fetchHotspotsByLocation(serviceUserPrincipal, latitude, longitude);
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(expected, response.getData());
        assertTrue(response.isSuccess());
        verify(hotspotService, times(1)).fetchHotspotsByLocation(latitude, longitude);
    }

}
