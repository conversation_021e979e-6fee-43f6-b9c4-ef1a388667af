package com.phonepe.merchant.legion.tasks.search.query.restrictions;

public class RestrictionsQueryBuilderConstants {
    public static final String EXPECTED_FOS_RESTRICTION_QUERY = """
        {
          "bool" : {
            "should" : [
              {
                "bool" : {
                  "must" : [
                    {
                      "bool" : {
                        "filter" : [
                          {
                            "terms" : {
                              "polygon_ids" : [
                                "S1",
                                "S2"
                              ],
                              "boost" : 1.0
                            }
                          }
                        ],
                        "adjust_pure_negative" : true,
                        "boost" : 1.0
                      }
                    },
                    {
                      "bool" : {
                        "filter" : [
                          {
                            "terms" : {
                              "task_definition_id" : [
                                "LEAD_TD_1",
                                "LEAD_TD_2"
                              ],
                              "boost" : 1.0
                            }
                          }
                        ],
                        "adjust_pure_negative" : true,
                        "boost" : 1.0
                      }
                    }
                  ],
                  "adjust_pure_negative" : true,
                  "boost" : 1.0
                }
              },
              {
                "bool" : {
                  "must" : [
                    {
                      "bool" : {
                        "must_not" : [
                          {
                            "terms" : {
                              "polygon_ids" : [
                                "S1",
                                "S2"
                              ],
                              "boost" : 1.0
                            }
                          }
                        ],
                        "adjust_pure_negative" : true,
                        "boost" : 1.0
                      }
                    }
                  ],
                  "adjust_pure_negative" : true,
                  "boost" : 1.0
                }
              }
            ],
            "adjust_pure_negative" : true,
            "boost" : 1.0
          }
        }""";

    public static final String EXPECTED_DFOS_RESTRICTION_QUERY = """
        {
          "bool" : {
            "must" : [
              {
                "bool" : {
                  "filter" : [
                    {
                      "terms" : {
                        "polygon_ids" : [
                          "S1",
                          "S2"
                        ],
                        "boost" : 1.0
                      }
                    }
                  ],
                  "adjust_pure_negative" : true,
                  "boost" : 1.0
                }
              },
              {
                "bool" : {
                  "filter" : [
                    {
                      "terms" : {
                        "task_definition_id" : [
                          "GTM_TD_1",
                          "GTM_TD_2"
                        ],
                        "boost" : 1.0
                      }
                    }
                  ],
                  "adjust_pure_negative" : true,
                  "boost" : 1.0
                }
              }
            ],
            "adjust_pure_negative" : true,
            "boost" : 1.0
          }
        }""";

    public static String EXPECTED_LENDING_RESTRICTION_QUERY_ON_LENDING_AGENT = """
            {
              "bool" : {
                "must" : [
                  {
                    "bool" : {
                      "filter" : [
                        {
                          "terms" : {
                            "action_id" : [
                              "PHONEPE_MERCHANT_LENDING"
                            ],
                            "boost" : 1.0
                          }
                        }
                      ],
                      "adjust_pure_negative" : true,
                      "boost" : 1.0
                    }
                  },
                  {
                    "bool" : {
                      "filter" : [
                        {
                          "terms" : {
                            "task_definition_id" : [
                              "PITCH_LOAN_HIGH_PRIORITY_PV"
                            ],
                            "boost" : 1.0
                          }
                        }
                      ],
                      "adjust_pure_negative" : true,
                      "boost" : 1.0
                    }
                  }
                ],
                "adjust_pure_negative" : true,
                "boost" : 1.0
              }
            }""";

    public static final String EXPECTED_LENDING_RESTRICTION_QUERY_ON_OTHER_AGENT = """
            {
              "bool" : {
                "should" : [
                  {
                    "bool" : {
                      "must" : [
                        {
                          "bool" : {
                            "filter" : [
                              {
                                "terms" : {
                                  "polygon_ids" : [
                                    "S1",
                                    "S2"
                                  ],
                                  "boost" : 1.0
                                }
                              }
                            ],
                            "adjust_pure_negative" : true,
                            "boost" : 1.0
                          }
                        },
                        {
                          "bool" : {
                            "must_not" : [
                              {
                                "terms" : {
                                  "action_id" : [
                                    "PHONEPE_MERCHANT_LENDING"
                                  ],
                                  "boost" : 1.0
                                }
                              }
                            ],
                            "adjust_pure_negative" : true,
                            "boost" : 1.0
                          }
                        },
                        {
                          "bool" : {
                            "must_not" : [
                              {
                                "terms" : {
                                  "task_definition_id" : [
                                    "PITCH_LOAN_HIGH_PRIORITY_PV"
                                  ],
                                  "boost" : 1.0
                                }
                              }
                            ],
                            "adjust_pure_negative" : true,
                            "boost" : 1.0
                          }
                        }
                      ],
                      "adjust_pure_negative" : true,
                      "boost" : 1.0
                    }
                  },
                  {
                    "bool" : {
                      "must" : [
                        {
                          "bool" : {
                            "must_not" : [
                              {
                                "terms" : {
                                  "polygon_ids" : [
                                    "S1",
                                    "S2"
                                  ],
                                  "boost" : 1.0
                                }
                              }
                            ],
                            "adjust_pure_negative" : true,
                            "boost" : 1.0
                          }
                        }
                      ],
                      "adjust_pure_negative" : true,
                      "boost" : 1.0
                    }
                  }
                ],
                "adjust_pure_negative" : true,
                "boost" : 1.0
              }
            }""";

    public static final String CHIMERA_RESTRICTIONS_CONFIG = """
        [
            {
              "treatmentGroup":  "default",
              "restrictionsEnabled": true,
              "restrictions": [
                [{
                  "conditionKey": "gladius_condition_fos",
                  "restrictionKey": "gladius_restriction_fos"
                }],
                [{
                  "conditionKey": "gladius_condition_dfos",
                  "restrictionKey": "gladius_restriction_dfos"
                }],
                [{
                  "conditionKey": "gladius_lending_condition_on_lending_agents",
                  "restrictionKey": "gladius_lending_restriction_on_lending_agents"
                }],
                [{
                  "conditionKey": "gladius_lending_condition_on_other_agents",
                  "restrictionKey": "gladius_lending_restriction_on_other_agents"
                }]
              ]
            }
        ]
        """;

    public static final String GLADIUS_CONDITION_FOS = """
        {
          "orConditions": [
            [
              {
                "type":  "NOT_IN",
                "field": "requestingAgentRole",
                "values": [
                  "DDP_FOS"
                ]
              }
            ]
          ]
        }
        """;

    public static final String GLADIUS_RESTRICTION_FOS = """
        {
          "orConditions": [
            [
              {
                "type":  "IN",
                "field": "polygon_ids",
                "values": {{$['ddpSectorIds']}}
              },
              {
                "type":  "IN",
                "field": "task_definition_id",
                "values": [
                  "LEAD_TD_1",
                  "LEAD_TD_2"
                ]
              }
            ],
            [
              {
                "type":  "NOT_IN",
                "field": "polygon_ids",
                "values": {{$['ddpSectorIds']}}
              }
            ]
          ]
        }
        """;

    public static final String GLADIUS_CONDITION_DFOS = """
        {
          "orConditions": [
            [
              {
                "type":  "IN",
                "field": "requestingAgentRole",
                "values": [
                  "DDP_FOS"
                ]
              }
            ]
          ]
        }
        """;
    public static final String GLADIUS_RESTRICTION_DFOS = """
        {
          "orConditions": [
            [
              {
                "type":  "IN",
                "field": "polygon_ids",
                "values": {{$['ddpSectorIds']}}
              },
              {
                "type":  "IN",
                "field": "task_definition_id",
                "values": [
                  "GTM_TD_1",
                  "GTM_TD_2"
                ]
              }
            ]
          ]
        }
        """;
    public static final String GLADIUS_LENDING_CONDITION_ON_LENDING_AGENTS = """
        {
          "orConditions": [
            [
              {
                "type":  "CONTAINS_ANY",
                "field": "agentTags",
                "values": [
                  "EXCLUSIVE_LENDING"
                ]
              }
            ]
          ]
        }
        """;
    public static final String GLADIUS_LENDING_RESTRICTION_ON_LENDING_AGENTS = """
        {
          "orConditions": [
            [
              {
                "type": "IN",
                "field": "action_id",
                "values": [
                  "PHONEPE_MERCHANT_LENDING"
                ]
              },
              {
                "type": "IN",
                "field": "task_definition_id",
                "values": [
                  "PITCH_LOAN_HIGH_PRIORITY_PV"
                ]
              }
            ]
          ]
        }
        """;
    public static final String GLADIUS_LENDING_CONDITION_ON_OTHER_AGENTS = """
        {
          "orConditions": [
            [
              {
                "type":  "NOT_CONTAINS",
                "field": "agentTags",
                "values": [
                  "EXCLUSIVE_LENDING"
                ]
              },
              {
                "type":  "IN",
                "field": "requestingAgentRole",
                "values": [
                  "BDA",
                  "BDE",
                  "TL",
                  "TSE"
                ]
              }
            ]
          ]
        }
        """;
    public static final String GLADIUS_LENDING_RESTRICTION_ON_OTHER_AGENTS = """
            {
              "orConditions": [
                [
                  {
                    "type":  "IN",
                    "field": "polygon_ids",
                    "values": {{$['exclusiveLendingSectorIds']}}
                  },
                  {
                    "type": "NOT_IN",
                    "field": "action_id",
                    "values": [
                      "PHONEPE_MERCHANT_LENDING"
                    ]
                  },
                  {
                    "type": "NOT_IN",
                    "field": "task_definition_id",
                    "values": [
                      "PITCH_LOAN_HIGH_PRIORITY_PV"
                    ]
                  }
                ],
                [
                  {
                    "type":  "NOT_IN",
                    "field": "polygon_ids",
                    "values": {{$['exclusiveLendingSectorIds']}}
                  }
                ]
              ]
            }
        """;
    public static final String CHIMERA_RESTRICTIONS_CONFIG_DISABLED = """
        [
            {
              "treatmentGroup":  "default",
              "restrictionsEnabled": false,
              "restrictions": [
                [{
                  "conditionKey": "gladius_condition_fos",
                  "restrictionKey": "gladius_restriction_fos"
                }],
                [{
                  "conditionKey": "gladius_condition_dfos",
                  "restrictionKey": "gladius_restriction_dfos"
                }]
              ]
            }
        ]
        """;
    public static final String CHIMERA_RESTRICTIONS_CONFIG_EMPTY = """
        [
        {
          "treatmentGroup":  "default",
          "restrictionsEnabled": true,
          "restrictions": [
          ]
        }
        ]
        """;
    public static final String CHIMERA_RESTRICTIONS_CONFIG_NULL = """
        [
        {
          "treatmentGroup":  "default",
          "restrictionsEnabled": true
        }
        ]
        """;
}
