package com.phonepe.merchant.legion.tasks.search.query.search;

import com.collections.CollectionUtils;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.search.query.BaseDiscoverySearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.ArrayList;
import java.util.List;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POLYGON_IDS;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class DiscoveryViewTaskSearchRequestQueryBuilder
        extends BaseDiscoverySearchRequestQueryBuilder<DiscoveryViewTaskSearchRequest>
        implements TaskSearchRequestQueryBuilder<DiscoveryViewTaskSearchRequest> {

    private final Miscellaneous miscellaneous;

    @Inject
    public DiscoveryViewTaskSearchRequestQueryBuilder(LegionService legionService,
                                                      Miscellaneous miscellaneous,
                                                      ViewKillSwitchExecutor viewKillSwitchExecutor,
                                                      RestrictionQueryBuilder restrictionQueryBuilder) {
        super(legionService, viewKillSwitchExecutor, restrictionQueryBuilder);
        this.miscellaneous = miscellaneous;
    }

    @Override
    public BoolQueryBuilder buildQuery(String agentId, DiscoveryViewTaskSearchRequest request) {
        return super.getQuery(request, ObjectUtils.firstNonNull(agentId, request.getAgentId()));
    }

    @Override
    protected BoolQueryBuilder getFilterBasedQuery(DiscoveryViewTaskSearchRequest request, AgentProfile agentProfile) {
        if (CollectionUtils.isNotEmpty(agentProfile.getSectors())) {
            request.getFilters().add(new InFilter(POLYGON_IDS, agentProfile.getSectors()));
            return TaskEsUtils.getBoolQuery(request.getFilters());
        }
        BoolQueryBuilder query = TaskEsUtils.getBoolQuery(request.getFilters());
        TaskEsUtils.addGeoDistanceFilter(query, request.getLocation(), miscellaneous.getMaxGeoSortDistance());
        return query;
    }

    @Override
    protected List<String> getSectorIdsFromRequest(DiscoveryViewTaskSearchRequest request, AgentProfile agentProfile) {
        List<String> sectorIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(agentProfile.getSectors())) {
            sectorIds.addAll(agentProfile.getSectors());
        }
        return sectorIds;
    }
}
