package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EventBasedTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;

public interface ClientTaskService {

    StoredTaskInstance createAndAssignQCTask(String taskInstanceId);

    StoredTaskInstance createTaskFromEvent(EventBasedTaskCreationRequest request);

    StoredTaskInstance createAndAssignClientTask(ClientTaskCreateAndAssignRequest request);

    StoredTaskInstance deleteClientTask(ClientTaskDeleteRequest request);

    StoredTaskInstance verifyClientTask(TaskManualVerificationRequest request);

    void tagMxnToExternalEntity(String merchantId, String storeId, String taskInstanceId);
}
