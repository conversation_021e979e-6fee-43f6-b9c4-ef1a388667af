package com.phonepe.merchant.legion.tasks.auth.resolver;

import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.DefinitionIdIdentifier;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsByTypeCache;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.olympus.im.client.OlympusIMClient;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.function.Supplier;

@Singleton
public class TaskDeletionRequestResolver extends AccessResolverForClients {

    @Inject
    public TaskDeletionRequestResolver(final Supplier<OlympusIMClient> olympusIMClientSupplier,
                                       final TaskDefinitionCache taskDefinitionCache,
                                       final TaskInstanceManagementServiceImpl taskInstanceManagementService,
                                       final FoxtrotEventIngestionService foxtrotEventIngestionService,
                                       final TaskDefinitionIdsByTypeCache taskDefinitionIdsByTypeCache) {
        super(olympusIMClientSupplier,taskDefinitionCache, taskInstanceManagementService, foxtrotEventIngestionService, taskDefinitionIdsByTypeCache);
    }

    public Class<? extends DefinitionIdIdentifier> getRequestClass() {
        return ClientTaskDeleteRequest.class;
    }
}
