package com.phonepe.merchant.legion.tasks.entitystore;

import com.google.inject.Inject;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.merchant.gladius.models.entitystore.AgentEntity;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntity;
import com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity;
import com.phonepe.merchant.gladius.models.entitystore.SectorEntity;
import com.phonepe.merchant.gladius.models.entitystore.TaskEntity;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.models.merchants.tasks.TaskExternalMetaRequest;
import com.phonepe.models.merchants.tasks.TaskMerchantMeta;
import com.phonepe.models.merchants.tasks.TaskStoreMeta;
import com.phonepe.models.merchants.tasks.TaskVpaMeta;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MAX_PAGE_SIZE;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

public class EntityMetaListFetchVisitor implements EntityType.EntityTypeVisitor<List<EntityMeta>, Set<String>> {

    private final MerchantService merchantService;
    private final IntelService intelService;
    private final ESRepository esRepository;
    private final ExternalEntityService externalEntityService;
    private final LegionService profileCRUDService;

    @Inject
    public EntityMetaListFetchVisitor(MerchantService merchantService,
                                      IntelService intelService, ESRepository esRepository,
                                      ExternalEntityService externalEntityService,
                                      LegionService profileCRUDService) {
        this.merchantService = merchantService;
        this.intelService = intelService;
        this.esRepository = esRepository;
        this.externalEntityService = externalEntityService;
        this.profileCRUDService = profileCRUDService;
    }

    @Override
    public List<EntityMeta> visitUser(Set<String> data) {
        return Collections.emptyList();
    }

    @Override
    public List<EntityMeta> visitSector(Set<String> data) {
        return data.stream().map(entityId -> {
                SectorEntity sectorEntity = SectorEntity.builder()
                        .sectorId(entityId)
                        .build();
                return EntityTransformationUtils.entityToEntityMeta(sectorEntity);
        }).toList();
    }

    @Override
    public List<EntityMeta> visitMerchant(Set<String> data) {
        List<TaskMerchantMeta> merchantMetas = merchantService.getListOfMerchants(data);
        List<Entity> entities = merchantMetas.stream()
                .map(EntityTransformationUtils::taskMerchantMetaToMerchantEntity)
                .collect(Collectors.toList());
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }

    @Override
    public List<EntityMeta> visitNone(Set<String> data) {
        return Collections.emptyList();
    }

    @Override
    public List<EntityMeta> visitStore(Set<String> data) {
        List<TaskStoreMeta> stores = merchantService.getListOfStores(data);
        List<Entity> entities = stores.stream()
                .map(EntityTransformationUtils::taskStoreMetaToStoreEntity)
                .collect(Collectors.toList());
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }

    @Override
    public List<EntityMeta> visitTask(Set<String> data) {
        InFilter inFilter = new InFilter(TASK_INSTANCE_ID,new ArrayList(data));
        List<Filter> filters = new ArrayList<>();
        filters.add(inFilter);
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        List<DiscoveryTaskInstance> tasks = esRepository.search(TASK_INDEX, boolQueryBuilder,
                0,MAX_PAGE_SIZE,DiscoveryTaskInstance.class);
        List<TaskEntity> entities = tasks.stream()
                .map(EntityTransformationUtils::discoveryTaskInstanceToTaskEntity)
                .toList();
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }

    @Override
    public List<EntityMeta> visitVpa(Set<String> data) {
        Set<TaskVpaMeta> vpas = intelService.getListOfVpas(data);
        List<Entity> entities = vpas.stream()
                .map(EntityTransformationUtils::taskVpaMetaToVpaEntity)
                .collect(Collectors.toList());
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }

    @Override
    public List<EntityMeta> visitExternal(Set<String> data) {
        TaskExternalMetaRequest request = TaskExternalMetaRequest.builder()
                .externalEntityIds(new ArrayList<>(data))
                .build();
        List<ExternalEntity> entities = externalEntityService.getExternalEntityMetas(request);
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }

    @Override
    public List<EntityMeta> visitAgent(Set<String> data) {
        List<AgentProfile> agents = profileCRUDService.getAgentsMeta(data);
        List<AgentEntity> entities = agents.stream()
                .map(EntityTransformationUtils::agentToAgentEntity)
                .toList();
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }

    @Override
    public List<EntityMeta> visitPhoneNumber(Set<String> data) {
        InFilter inFilter = new InFilter(TASK_INSTANCE_ID,new ArrayList(data));
        EqualsFilter equalsFilter = new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE,
                EntityType.PHONE_NUMBER.name());
        List<Filter> filters = new ArrayList<>();
        filters.add(inFilter);
        filters.add(equalsFilter);
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        List<DiscoveryTaskInstance> tasks = esRepository.search(TASK_INDEX, boolQueryBuilder,0, MAX_PAGE_SIZE,DiscoveryTaskInstance.class);
        Set<PhoneNumberEntity> entities = tasks.stream()
                .map(EntityTransformationUtils::phoneNumberTaskToPhoneNumberEntity)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return entities.stream()
                .map(EntityTransformationUtils::entityToEntityMeta)
                .toList();
    }
}
