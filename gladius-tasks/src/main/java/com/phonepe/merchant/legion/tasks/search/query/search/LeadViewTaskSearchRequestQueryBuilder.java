package com.phonepe.merchant.legion.tasks.search.query.search;

import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.predicate.ORFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.search.LeadViewTaskSearchRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;

import static com.phonepe.merchant.legion.tasks.utils.CommonUtils.enrichRequestWithNeedScheduledTaskFilters;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class LeadViewTaskSearchRequestQueryBuilder
        implements TaskSearchRequestQueryBuilder<LeadViewTaskSearchRequest>{

    @Override
    public BoolQueryBuilder buildQuery(String agentId, LeadViewTaskSearchRequest request) {
        enrichLeadRequest(agentId, request.getFilters(), request.getAssignedTo());
        request.getFilters().add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        request.getFilters().add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, EntityType.PHONE_NUMBER.name()));

        if (request.isNeedScheduledTasks()) {
            enrichRequestWithNeedScheduledTaskFilters(request.getFilters(), request.getStartDate(), request.getEndDate());
        }
        return getBoolQuery(request.getFilters());
    }

    private void enrichLeadRequest(String actor, List<Filter> filters, String assignedTo) {
        //set value of actor in request
        actor = ObjectUtils.firstNonNull(assignedTo, actor);
        Preconditions.checkNotNull(actor, "assignedTo cannot be null in lead view");
        ORFilter orFilter = new ORFilter(List.of(
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, actor),
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.COMPLETED_BY, actor)
        ));
        filters.add(orFilter);
    }
}
