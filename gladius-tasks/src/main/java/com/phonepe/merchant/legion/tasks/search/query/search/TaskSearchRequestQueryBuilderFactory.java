package com.phonepe.merchant.legion.tasks.search.query.search;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.HotspotAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.HotspotDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.LeadViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.legion.tasks.bindings.search.AssignedViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.DiscoveryViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.EscalatedViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.LeadViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.SectorDiscoveryViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilder;

@Slf4j
@Singleton
public class TaskSearchRequestQueryBuilderFactory {
    private final TaskSearchRequestQueryBuilder<AssignedViewTaskFetchRequest> assignedViewQueryBuilder;
    private final TaskSearchRequestQueryBuilder<DiscoveryViewTaskSearchRequest> discoveryViewTaskSearchRequestQueryBuilder;
    private final TaskSearchRequestQueryBuilder<SectorDiscoveryViewTaskSearchRequest> sectorDiscoveryViewTaskSearchRequestQueryBuilder;
    private final TaskSearchRequestQueryBuilder<EscalatedViewTaskSearchRequest> escalatedViewTaskSearchRequestTaskSearchRequestQueryBuilder;
    private final HotspotRequestQueryBuilder hotspotRequestQueryBuilder;

    private final TaskSearchRequestQueryBuilder<LeadViewTaskSearchRequest> leadViewQueryBuilder;

    @Inject
    public TaskSearchRequestQueryBuilderFactory(
            @AssignedViewTaskSearchRequestQueryBuilderProvider
            TaskSearchRequestQueryBuilder<AssignedViewTaskFetchRequest> assignedViewQueryBuilder,
            @DiscoveryViewTaskSearchRequestQueryBuilderProvider
            TaskSearchRequestQueryBuilder<DiscoveryViewTaskSearchRequest> discoveryViewTaskSearchRequestQueryBuilder,
            @SectorDiscoveryViewTaskSearchRequestQueryBuilderProvider
            TaskSearchRequestQueryBuilder<SectorDiscoveryViewTaskSearchRequest> sectorDiscoveryViewTaskSearchRequestQueryBuilder,
            @EscalatedViewTaskSearchRequestQueryBuilderProvider
            TaskSearchRequestQueryBuilder<EscalatedViewTaskSearchRequest> escalatedViewTaskSearchRequestTaskSearchRequestQueryBuilder,
            HotspotRequestQueryBuilder hotspotRequestQueryBuilder,
            @LeadViewTaskSearchRequestQueryBuilderProvider
            TaskSearchRequestQueryBuilder<LeadViewTaskSearchRequest> leadViewQueryBuilder) {
        this.assignedViewQueryBuilder = assignedViewQueryBuilder;
        this.discoveryViewTaskSearchRequestQueryBuilder = discoveryViewTaskSearchRequestQueryBuilder;
        this.sectorDiscoveryViewTaskSearchRequestQueryBuilder = sectorDiscoveryViewTaskSearchRequestQueryBuilder;
        this.escalatedViewTaskSearchRequestTaskSearchRequestQueryBuilder = escalatedViewTaskSearchRequestTaskSearchRequestQueryBuilder;
        this.hotspotRequestQueryBuilder = hotspotRequestQueryBuilder;
        this.leadViewQueryBuilder = leadViewQueryBuilder;
    }

    @MonitoredFunction
    public QueryBuilder queryBuilder(String actor, TaskSearchRequest request) {
        return request.getTaskSearchRequestType().accept(new TaskSearchRequestType.TaskSearchRequestTypeVisitor<>() {
            @Override
            public QueryBuilder visitAssignedRequest(TaskSearchRequest payload) {
                return assignedViewQueryBuilder.buildQuery(actor, (AssignedViewTaskFetchRequest) request);
            }

            @Override
            public QueryBuilder visitDiscoveryRequest(TaskSearchRequest payload) {
                return discoveryViewTaskSearchRequestQueryBuilder.buildQuery(actor,
                        (DiscoveryViewTaskSearchRequest) request);
            }

            @Override
            public QueryBuilder visitSectorAssignedRequest(TaskSearchRequest payload) {
                return assignedViewQueryBuilder.buildQuery(actor, (SectorAssignedViewTaskSearchRequest) request);
            }

            @Override
            @MonitoredFunction(method = "sectorDiscoveryView")
            public QueryBuilder visitSectorDiscoveryRequest(TaskSearchRequest payload) {
                return sectorDiscoveryViewTaskSearchRequestQueryBuilder.buildQuery(actor,
                        (SectorDiscoveryViewTaskSearchRequest) request);
            }

            @Override
            @MonitoredFunction(method = "escalationView")
            public QueryBuilder viewEscalatedView(TaskSearchRequest payload) {
                return escalatedViewTaskSearchRequestTaskSearchRequestQueryBuilder.buildQuery(actor, (EscalatedViewTaskSearchRequest) request);
            }

            @Override
            public QueryBuilder viewHotspotAssignedView(TaskSearchRequest payload) {
                return hotspotRequestQueryBuilder.buildQuery(actor, ((HotspotAssignedViewTaskSearchRequest)payload).getHotspotId()).must(
                        visitSectorAssignedRequest(payload));
            }

            @Override
            public QueryBuilder viewHotspotDiscoveryView(TaskSearchRequest payload) {
                return hotspotRequestQueryBuilder.buildQuery(actor, ((HotspotDiscoveryViewTaskSearchRequest)payload).getHotspotId()).must(
                        visitSectorDiscoveryRequest(payload));
            }

            @Override
            @MonitoredFunction(method = "leadView")
            public QueryBuilder visitLeadRequest(TaskSearchRequest payload) {
                return leadViewQueryBuilder.buildQuery(actor, (LeadViewTaskSearchRequest) request);
            }
        }, request);
    }
}
