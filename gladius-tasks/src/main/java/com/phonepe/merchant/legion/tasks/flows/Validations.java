package com.phonepe.merchant.legion.tasks.flows;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.flows.models.ActionsEligibleForSelfAssignTransition;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY;
import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.BAD_REQUEST;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.BUSINESS_UNIT_MISMATCH;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.TASK_SECTOR_NOT_ASSIGNED;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class Validations {

    private final LegionService legionService;
    private final AtlasService atlasService;
    private final ChimeraRepositoryImpl chimeraLiteRepository;


    public void validateTaskAssignee(AgentProfile agentProfile) {
        if (agentProfile == null || !agentProfile.isActive()) {
            throw LegionException.error(BAD_REQUEST,
                    Map.of("message", "Task cannot be assigned to invalid agent id"));
        }

    }

    @MonitoredFunction
    public void validateTaskSectorWithUser(String agentId, List<String> taskSectors) {
        AgentProfile agentProfile = legionService.getAgentProfile(agentId);

        validateTaskAssignee(agentProfile);

        if (!isNullOrEmpty(taskSectors) && !isAssignable(agentId, taskSectors)) {
            throw LegionException.error(TASK_SECTOR_NOT_ASSIGNED);
        }
    }

    @MonitoredFunction
    public void validateTaskSectorAndBuWithUser(String agentId, EsLocationRequest taskLocation, Set<String> tags) {
        AgentProfile agentProfile = legionService.getAgentProfile(agentId);

        if (!isNullOrEmpty(tags) && !(tags.contains(agentProfile.getBusinessUnit().name()) || tags.contains(agentProfile.getAgentType().name()))) {
            throw LegionException.error(BUSINESS_UNIT_MISMATCH);
        }

        validateTaskAssignee(agentProfile);
        List<String> taskSectors = atlasService.getSectorIds(taskLocation);
        if (!isNullOrEmpty(taskSectors) && !isAssignable(agentId, taskSectors)) {
            throw LegionException.error(TASK_SECTOR_NOT_ASSIGNED);
        }
    }

    public boolean isActionEligibleForSelfAssignment(LegionTaskStateMachineState state, String actionId) {
        ActionsEligibleForSelfAssignTransition actionsEligibleForSelfAssignTransition = chimeraLiteRepository.getChimeraConfig(
                IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY, ActionsEligibleForSelfAssignTransition.class
        );
        return Optional.ofNullable(actionsEligibleForSelfAssignTransition)
                .map(ActionsEligibleForSelfAssignTransition::getEligibleActionsForSelfAssignment)
                .map(actionsMap -> actionsMap.get(actionId))
                .map(rules -> rules.accept(state)
                ).orElse(false);
    }

    public boolean isAssignable(String agentId, List<String> taskSectors) {
        for (String sector : taskSectors) {
            if (legionService.isSectorAccessibleBoolean(sector, agentId)) {
                return true;
            }
        }
        return false;
    }
}
