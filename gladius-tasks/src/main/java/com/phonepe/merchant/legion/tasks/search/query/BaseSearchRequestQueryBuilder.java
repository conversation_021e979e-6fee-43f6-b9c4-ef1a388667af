package com.phonepe.merchant.legion.tasks.search.query;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.UserRestrictionResponse;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.models.DiscoveryRestrictionContext;
import com.phonepe.merchant.legion.tasks.search.query.search.ViewKillSwitchExecutor;
import com.phonepe.merchant.legion.tasks.utils.CommonUtils;
import com.phonepe.merchant.legion.tasks.utils.ProfileUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.DEFAULT_TREATMENT_GROUP;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.EXCLUSIVE_LENDING_TAG;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getAllowedActionsFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getDisabledAttributeFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTagFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskStatusFilter;
import static org.elasticsearch.index.query.QueryBuilders.matchQuery;

@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseSearchRequestQueryBuilder<T> {

    private List<LegionTaskStateMachineState> states;
    protected LegionService legionService;
    private ViewKillSwitchExecutor viewKillSwitchExecutor;
    private RestrictionQueryBuilder restrictionQueryBuilder;

    public BoolQueryBuilder getQuery(T request, String actor) {

        AgentProfile userProfile = legionService.getAgentProfile(actor);
        UserRestrictionResponse userRestrictionResponse = legionService.fetchUserRestrictions(actor);

        BoolQueryBuilder query = getBaseQuery(states);
        query.must(getFilterBasedQuery(request, userProfile));

        if (!isNullOrEmpty(userRestrictionResponse.getAllowedActions())) {
            query.must(getAllowedActionsFilter(userRestrictionResponse.getAllowedActions()));
        }

        if (viewKillSwitchExecutor.isViewKsEnabled()) {
            query.mustNot(viewKillSwitchExecutor.buildKsQuery());
        }

        if (!isNullOrEmpty(userRestrictionResponse.getEnabledAttributes())) {
            userRestrictionResponse.getEnabledAttributes().forEach(value -> query.must(getDisabledAttributeFilter(value)));
        }

        // Adding checks for tags associated with agent's profile
        query.must(getTagFilter(ProfileUtils.tagEnricher(userProfile)));
        query.must(TaskEsUtils.getRoleFilter(userProfile.getAgentType().toString()));
        restrictionQueryBuilder.getRestrictionQueries(buildDiscoveryRestrictionContext(request, userProfile), DEFAULT_TREATMENT_GROUP).forEach(query::must);
        return query;
    }

    public static BoolQueryBuilder getBaseQuery(List<LegionTaskStateMachineState> states) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(matchQuery(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        query.must(TaskEsUtils.getBoolQuery(List.of(getTaskStatusFilter(states))));
        return query;
    }

    protected DiscoveryRestrictionContext buildDiscoveryRestrictionContext(T request, AgentProfile agentProfile) {
        List<String> sectorIds = getSectorIdsFromRequest(request, agentProfile);
        // Fetch All Sector Ids assigned to Agent in case we can not derive the sector Ids from request
        if (isNullOrEmpty(sectorIds)) {
            sectorIds = legionService.getAllAccessibleSectorsOfAgent(agentProfile.getAgentId());
        }
        if (isNullOrEmpty(sectorIds)) {
            return DiscoveryRestrictionContext.builder()
                    .ddpSectorIds(Collections.emptyList())
                    .requestingAgentRole(agentProfile.getAgentType())
                    .build();
        }
        AgentProfilesInSectorResponse agentsInSectors = legionService.getAgentProfilesInSector(sectorIds);
        List<String> ddpSectorIds = new ArrayList<>();
        Set<String> exclusiveLendingSectorIds = new HashSet<>();
        Map<String, Set<String>> tagToSectorIds = new HashMap<>();
        restrictionQueryBuilder.getSectorProfileTags().forEach(tag -> tagToSectorIds.put(tag, new HashSet<>()));
        agentsInSectors.getProfilesPerSector().forEach((k, v) ->
                v.forEach(p -> {
                    if (CommonUtils.DDP_SECTOR_AGENT_ROLES.contains(p.getAgentType())) {
                        ddpSectorIds.add(k);
                    }
                    Set<String> agentTags = ProfileUtils.tagEnricher(p);
                    if (agentTags.contains(EXCLUSIVE_LENDING_TAG)) {
                        exclusiveLendingSectorIds.add(k);
                    }

                    restrictionQueryBuilder.getSectorProfileTags().forEach(tag -> {
                        if (agentTags.contains(tag)) {
                            tagToSectorIds.get(tag).add(k);
                        }
                    });
                })
        );

        return DiscoveryRestrictionContext.builder()
                .ddpSectorIds(Set.copyOf(ddpSectorIds).stream().toList())
                .exclusiveLendingSectorIds(exclusiveLendingSectorIds.stream().toList())
                .requestingAgentRole(agentProfile.getAgentType())
                .agentTags(ProfileUtils.tagEnricher(agentProfile).stream().toList())
                .tagToSectorIds(tagToSectorIds)
                .build();
    }

    protected abstract BoolQueryBuilder getFilterBasedQuery(T request, AgentProfile agentProfile);

    protected abstract List<String> getSectorIdsFromRequest(T request, AgentProfile agentProfile);
}