package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.survey.CampaignQuestionResponse;
import com.phonepe.merchant.gladius.models.survey.FeedbackPayload;
import com.phonepe.merchant.gladius.models.survey.HtmContentResponse;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EventBasedTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.BrickbatService;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.core.utils.LegionCoreConstants;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskDeleteMessage;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskVerificationMessage;
import com.phonepe.merchant.legion.tasks.actor.message.EventBasedTaskCreationMessage;
import com.phonepe.merchant.legion.tasks.actor.message.QcTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskCreationRequestResolver;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskDeletionRequestResolver;
import com.phonepe.merchant.legion.tasks.cache.CampaignQuestionsCache;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.resolver.ConstantTenantTypeResolver;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.brickbat.models.feedback.StoredFeedback;
import com.phonepe.platform.brickbat.models.feedback.StoredSurvey;
import com.phonepe.platform.brickbat.models.question.FeedbackReasonQuestion;
import com.phonepe.platform.brickbat.models.question.MCQQuestion;
import com.phonepe.platform.brickbat.models.question.Question;
import com.phonepe.platform.brickbat.models.question.TextQuestion;
import com.phonepe.platform.brickbat.models.question.options.Option;
import com.phonepe.platform.brickbat.models.question.questionresponse.DateQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.EntityRatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.FeedbackReasonQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQGridQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.QuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.QuestionResponseVisitor;
import com.phonepe.platform.brickbat.models.question.questionresponse.RatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.TextQuestionResponse;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import killswitch.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ANCHOR_END;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ANCHOR_START;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.PHONE_NUMBER_REGEX;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.TR_CLOSE;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.TR_OPEN;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

/**
 * <AUTHOR> puri
 */

@Slf4j
@Singleton
@Path("/v1/task")
@Tag(name = "Misc resources")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class MiscResource {

    private final BrickbatService brickbatService;
    private final ClientTaskService clientTaskService;
    private final CampaignQuestionsCache campaignQuestionsCache;
    private final FoxtrotEventExecutor eventExecutor;
    private final TaskManagementService taskManagementService;
    private final TaskDiscoveryService taskDiscoveryService;

    @Path("/survey/feedback")
    @POST
    @Timed
    @ExceptionMetered
    @RolesAllowed(value = "all")
    @Operation(summary = "get feedbackResult")
    @ApiKillerMeta(tags = {OperationType.WRITE})
    public GenericResponse<HtmContentResponse> getFeedbackResult(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid FeedbackPayload feedbackPayload) {

        String feedbackId = feedbackPayload.toString();
        GenericResponse<StoredFeedback> feedbackGenericResponse = brickbatService.getSurveyResult(feedbackId);
        StoredSurvey storedSurvey = (StoredSurvey) feedbackGenericResponse.getData();
        Map<String, QuestionResponse> map = storedSurvey.getUserResponses();
        StringBuilder finalHtml = new StringBuilder(LegionCoreConstants.HTML_START);/** will change to free marker*/
        Map<String, Question> campaignQuestionMap = campaignQuestionsCache.get(feedbackPayload.getBrickbatCampaignId());
        for (Map.Entry<String, QuestionResponse> responseEntry : map.entrySet()) {
            Question question = campaignQuestionMap.get(responseEntry.getKey());
            CampaignQuestionResponse questionResponse = responseEntry.getValue().accept(new QuestionResponseVisitor<CampaignQuestionResponse>() {
                @Override
                public CampaignQuestionResponse visit(MCQQuestionResponse mcqQuestionResponse) {
                    String mcqResponseOption = mcqQuestionResponse.getAnswers().get(0).getKey();
                    MCQQuestion mcqQuestion = (MCQQuestion) question;
                    Optional<Option> mcqQuestionOption = mcqQuestion.getOptions().values().stream().filter(mcqOption -> mcqOption.getKey().equals(mcqResponseOption)).findFirst();

                    return CampaignQuestionResponse.builder()
                            .questionDisplayText(mcqQuestion.getTitle())
                            .questionResponse(mcqQuestionOption.isPresent() ? mcqQuestionOption.get().getDisplayText() : "")
                            .build();
                }

                @Override
                public CampaignQuestionResponse visit(TextQuestionResponse textQuestionResponse) {
                    Matcher m = (Pattern.compile(PHONE_NUMBER_REGEX)).matcher(textQuestionResponse.getText());
                    String questionResponse;
                    TextQuestion textQuestion = (TextQuestion) question;
                    if (m.matches()) {
                        questionResponse = ANCHOR_START + textQuestionResponse.getText() + "\'>" + textQuestionResponse.getText() + ANCHOR_END;
                    } else {
                        questionResponse = textQuestionResponse.getText();
                    }
                    return CampaignQuestionResponse.builder()
                            .questionResponse(questionResponse)
                            .questionDisplayText(textQuestion.getTitle()).build();
                }

                @Override
                public CampaignQuestionResponse visit(FeedbackReasonQuestionResponse feedbackReasonQuestionResponse) {
                    return CampaignQuestionResponse.builder()
                            .questionResponse(feedbackReasonQuestionResponse.getFeedbackReasons())
                            .questionDisplayText(((FeedbackReasonQuestion) question).getDefaultTitle()).build();
                }

                @Override
                public CampaignQuestionResponse visit(EntityRatingQuestionResponse entityRatingQuestionResponse) {
                    return CampaignQuestionResponse.builder()
                            .questionResponse(entityRatingQuestionResponse.getRating())
                            .questionDisplayText(question.getTitle()).build();
                }

                @Override
                public CampaignQuestionResponse visit(RatingQuestionResponse ratingQuestionResponse) {
                    return CampaignQuestionResponse.builder()
                            .questionResponse(ratingQuestionResponse.getRating())
                            .questionDisplayText(question.getTitle()).build();
                }

                @Override
                public CampaignQuestionResponse visit(MCQGridQuestionResponse mcqGridQuestionResponse) {
                    return CampaignQuestionResponse.builder()
                            .questionResponse(mcqGridQuestionResponse.getAnswers())
                            .questionDisplayText(question.getTitle()).build();
                }

                @Override
                public CampaignQuestionResponse visit(DateQuestionResponse dateQuestionResponse) {
                    return CampaignQuestionResponse.builder()
                            .questionResponse(new Date(dateQuestionResponse.getEpoch()))
                            .questionDisplayText(question.getTitle()).build();
                }
            });
            finalHtml.append(TR_OPEN);
            finalHtml.append(LegionCoreConstants.TD_OPEN).append(questionResponse.getQuestionDisplayText()).append(LegionCoreConstants.TD_CLOSE);
            finalHtml.append(LegionCoreConstants.TD_OPEN).append(questionResponse.getQuestionResponse()).append(LegionCoreConstants.TD_CLOSE);
            finalHtml.append(TR_CLOSE);
        }
        return GenericResponse.<HtmContentResponse>builder()
                .data(HtmContentResponse.builder().htmlContent(finalHtml.append(LegionCoreConstants.HTML_END).toString()).build())
                .success(true)
                .build();
    }

    @Path("/create/qc/task/{taskInstanceId}")
    @POST
    @Timed
    @ExceptionMetered
    @Operation(summary = "create and assign QC task")
    @RolesAllowed( value = "create_qc_task")
    @ApiKillerMeta(tags = {OperationType.QUEUE, OperationType.WRITE})
    public GenericResponse<Boolean> createAndAssignTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("taskInstanceId") String taskInstanceId) {
        return GenericResponse.<Boolean>builder()
                .success(true)
                .data(TaskActionMessagePublisher.createAndAssignQcTask(
                        QcTaskCreateAndAssignMessage.builder()
                                .taskInstanceId(taskInstanceId)
                                .build())
                ).build();
    }

    @Path("/create/task")
    @POST
    @Timed
    @ExceptionMetered
    @Operation(summary = "create task based on event")
    @ApiKillerMeta(tags = {OperationType.QUEUE, OperationType.WRITE})
    @RolesAllowed(value = "crete_task_event")
    public Response createTaskFromEvent(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid EventBasedTaskCreationRequest request) {
        TaskActionMessagePublisher.createTaskFromEvent(
                EventBasedTaskCreationMessage.builder()
                        .taskCreationRequest(request)
                        .build());
        return Response.accepted()
                .build();
    }

    @Path("create/client/task")
    @POST
    @Timed
    @ExceptionMetered
    @AccessAllowed(
            permissions = {"clientTaskCreate"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskCreationRequestResolver.class)
    @Operation(summary = "Create task for client")
    @ApiKillerMeta(tags = {OperationType.QUEUE, OperationType.WRITE})
    public GenericResponse<Boolean> createAndAssignClientTask(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                              @NotNull @Valid ClientTaskCreateAndAssignRequest request) {
        String actor = AuthUserDetails.getLegionUserId(null, null, userPrincipal);
        log.info("Creating task for client with request: {}", request);
        eventExecutor.ingest(TaskFoxtrotEventUtils.toClientTaskRequestedEvent(request));
        request.setCreatedBy(actor);
        try {
            TaskActionMessagePublisher.createAndAssignTaskForClient(
                            ClientTaskCreateAndAssignMessage.builder()
                                    .request(request)
                                    .build());
            eventExecutor.ingest(TaskFoxtrotEventUtils.toQueuePushSuccessEvent(request));
            return GenericResponse.<Boolean>builder()
                    .success(true)
                    .data(true)
                    .build();

        } catch (Exception e) {
            eventExecutor.ingest(TaskFoxtrotEventUtils.toQueuePushFailedEvent(request, e));
            throw LegionException.propagate(LegionTaskErrorCode.CLIENT_TASK_QUEUE_PUSH_FAILED, e);
        }
    }

    @Path("create/client/task/sync")
    @POST
    @Timed
    @ExceptionMetered
    @AccessAllowed(
            permissions = {"clientTaskCreate"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskCreationRequestResolver.class)
    @Operation(summary = "Create task for client")
    @RolesAllowed(value = "clientTaskCreate") // check with sid
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
    public GenericResponse<TaskInstance> createAndAssignClientTaskSync(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                       @NotNull  @Valid ClientTaskCreateAndAssignRequest request,
                                                                       @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, null, userPrincipal);
        request.setCreatedBy(actor);
        return GenericResponse.<TaskInstance>builder()
                .success(true)
                .data(TaskInstanceTransformationUtils.toTaskInstance(clientTaskService.createAndAssignClientTask(request)))
                .build();
    }

    @Path("delete/client/task")
    @POST
    @Timed
    @ExceptionMetered
    @AccessAllowed(
            permissions = {"clientTaskDelete"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskDeletionRequestResolver.class)
    @Operation(summary = "Delete task for client")
    @ApiKillerMeta(tags = {OperationType.QUEUE, OperationType.WRITE})
    public GenericResponse<Boolean> deleteClientTask(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                     @Valid ClientTaskDeleteRequest request) {
        String actor = AuthUserDetails.getLegionUserId(null, null, userPrincipal);
        request.setDeletedBy(actor);
        return GenericResponse.<Boolean>builder()
                .success(true)
                .data(TaskActionMessagePublisher.deleteTask(ClientTaskDeleteMessage.builder().request(request).build()))
                .build();
    }

    @POST
    @Path("/client/task/delete")
    @Timed
    @AccessAllowed(
            permissions = {"clientTaskDelete"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskDeletionRequestResolver.class)
    @Operation(summary = "Move task to deleted state sync")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    public  GenericResponse<TaskInstance> deleteTask(@NotNull @Valid ClientTaskDeleteRequest request,
                                                     @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal) {
        request.setDeletedBy(AuthUserDetails.getLegionUserId(null, null, userPrincipal));
        StoredTaskInstance storedTaskInstance = taskManagementService.deleteTask(request);
        return GenericResponse.<TaskInstance>builder()
                .success(storedTaskInstance != null)
                .data(TaskInstanceTransformationUtils.toTaskInstance(storedTaskInstance))
                .build();
    }


    @Path("verify/client/task")
    @POST
    @Timed
    @ExceptionMetered
    @RolesAllowed("clientTaskVerify")
    @Operation(summary = "Verify task for client")
    @ApiKillerMeta(tags = {OperationType.QUEUE, OperationType.WRITE})
    @LegionGateKeeper
    public GenericResponse<Void> verifyClientTask(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                  TaskManualVerificationRequest request,
                                                  @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, null, userPrincipal);
        request.setVerifiedBy(actor);
        return GenericResponse.<Void>builder()
                .success(TaskActionMessagePublisher.verifyTask(ClientTaskVerificationMessage.builder().request(request).build()))
                .build();
    }

    @GET
    @Path("/leads/config")
    @Timed
    @Authorize(value = "getLeadConfig")
    @RolesAllowed(value = "getLeadConfig")
    @Operation(summary = "Fetch lead management related config")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<LeadConfig> getConfig(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal) {
        return GenericResponse.<LeadConfig>builder()
                .success(true)
                .data(TaskInstanceTransformationUtils.toLeadConfig())
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Get Task Listing from ES",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/taskListing")
    @RolesAllowed(value = "all")
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<TaskListResponse> getTaskListing(@Valid @NotNull TaskListRequest taskListRequest,
                                                            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal) {
        return GenericResponse.<TaskListResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getTaskListing(taskListRequest))
                .build();
    }

}
