package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.core.FoxtrotRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotAccessDetailsRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotSyncRequest;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotAccessDetailsResponse;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotDto;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TasksInPolygonRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.TaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.geopolygon.GeoFenceRemappingSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.EsDocScrollResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.FoxtrotService;
import com.phonepe.merchant.legion.core.services.HermodService;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskEsChangeEventPublishMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskEsDirectUpdateMessage;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.services.HotspotService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskFilterService;
import com.phonepe.merchant.legion.tasks.services.TaskHousekeepingService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.atlas.model.fence.Shape;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import killswitch.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher.taskEsChangeEventPublishMessage;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/housekeeping")
@Tag(name = "Housekeeping APIs")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
public class TaskHouseKeepingResource {

    private final TaskDiscoveryService taskDiscoveryService;
    private final FoxtrotService foxtrotService;
    private final TaskHousekeepingService taskHousekeepingService;
    private final TaskDefinitionService taskDefinitionService;
    private final TaskFilterService taskFilterService;
    private final HermodService hermodService;
    private final TaskInstanceManagementService taskInstanceManagementService;
    private final IntelService intelService;
    private final AtlasService atlasService;
    private final HotspotService hotspotService;

    @PUT
    @Path("/sync")
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "update Task Instance")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Void> syncWithDb(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @QueryParam("taskInstanceId") String taskInstanceId) {
        taskHousekeepingService.refreshTaskInstance(taskInstanceId);
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @PUT
    @Path("/update")
    @Timed
    @Operation(summary = "update Task Instance")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<String> update(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @QueryParam("taskInstanceId") String taskInstanceId, Object discoveryTaskInstance) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        String data = objectMapper.writeValueAsString(discoveryTaskInstance);
        TaskActionMessagePublisher.taskEsUpdateRetry(new TaskEsDirectUpdateMessage(taskInstanceId, data));
        return GenericResponse.<String>builder()
                .success(true)
                .data(data)
                .build();
    }

    @POST
    @Path("syncDueDate/{taskInstanceId}")
    @Timed
    @Operation(summary = "sync due date in db")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Void> syncDueDate(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("taskInstanceId") @NotNull @NotEmpty String taskInstanceId) {
        taskHousekeepingService.syncDueDateInDb(taskInstanceId);
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @POST
    @Path("/ingest/{taskInstanceId}")
    @Timed
    @Operation(summary = "Ingest Task Instance")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Void> ingest(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("taskInstanceId") String taskInstanceId) {
        taskEsChangeEventPublishMessage(TaskEsChangeEventPublishMessage.builder()
                .taskInstanceId(taskInstanceId)
                .build());
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @GET
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Get task instance",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/get/{taskInstanceId}")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<DiscoveryTaskInstance> getTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("taskInstanceId") @NotNull @NotEmpty String taskInstanceId) {
        TaskByIdRequest taskByIdRequest = TaskByIdRequest.builder()
                .taskInstanceId(taskInstanceId)
                .build();
        return GenericResponse.<DiscoveryTaskInstance>builder()
                .success(true)
                .data(taskDiscoveryService.getById(taskByIdRequest))
                .build();
    }

    @GET
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Get task instance",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/vpa/{vpaId}")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<CompetitionQrResponse> getVpa(@PathParam("vpaId") @NotNull @NotEmpty String vpaId) {
        return GenericResponse.<CompetitionQrResponse>builder()
                .success(true)
                .data(intelService.getVpaDetails(vpaId))
                .build();
    }

    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Get Task Listing",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/get/listing")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskListResponse> getTaskListing(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid @NotNull TaskListRequest taskListRequest) {
        return GenericResponse.<TaskListResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getTaskListing(taskListRequest))
                .build();
    }

    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Get Foxtrot Events",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/get/events")
    @ApiKillerMeta(tags = {OperationType.READ})
	public long getFoxtrotEvents(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, FoxtrotRequest request) {
        return foxtrotService.getFoxtrotEventCount(request);
    }


    @Path("/v1/log")
    @POST
    @ExceptionMetered
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "API that will be used to test out all the dags")
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse logMessage(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull Object message) {
        log.info("Received the message :- {} ", message);
        return GenericResponse.builder().success(true).build();
    }

    @GET
    @Path("/fetchbyAction/{actionId}")
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Fetch Task Definitions for an Action")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<List<TaskDefinitionInstance>> fetchByActionId(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("actionId") @NotNull final String actionId) {
        List<TaskDefinitionInstance> response = taskDefinitionService.fetchByActionId(actionId);
        return GenericResponse.<List<TaskDefinitionInstance>>builder()
                .success(response != null)
                .data(response)
                .build();
    }

    @PUT
    @Path("/bulk/sector")
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Bulk update sector on sector id bases")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Boolean> bulkSectorUpdate(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @QueryParam("sectorId") @NotNull final String sectorId,
                                                     @QueryParam("createdAt") @NotNull final long createdAt) {
        boolean response = taskHousekeepingService.updateSectorAgainstSectorId(sectorId, createdAt);
        return GenericResponse.<Boolean>builder()
                .success(response)
                .build();
    }

    @PUT
    @Path("/sector/{taskInstanceId}")
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Bulk update sector on sector id bases")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<DiscoveryTaskInstance> updateSectorForTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("taskInstanceId") @NotNull final String taskInstanceId) {
        boolean response = taskHousekeepingService.updateSectorForTask(taskInstanceId);
        return GenericResponse.<DiscoveryTaskInstance>builder()
                .success(response)
                .build();
    }

    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "GET filter",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/filters")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<ViewWiseFilters> getFiltersOnTasks(
            @Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
            @Valid @NotNull TaskFilterRequest taskFilterRequest,
                                                              @QueryParam("actor") @NotNull @NotEmpty String actor) {
        return GenericResponse.<ViewWiseFilters>builder()
                .success(true)
                .data(taskFilterService.getTaskFilterOptions(actor, taskFilterRequest))
                .build();
    }

    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "GET filter",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/filters/v2")
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<ViewWiseFiltersV2> getFiltersOnTasksV2(
            @Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
            @Valid @NotNull TaskFilterRequest taskFilterRequest,
            @QueryParam("actor") @NotNull @NotEmpty String actor) {
        return GenericResponse.<ViewWiseFiltersV2>builder()
                .success(true)
                .data(taskFilterService.getTaskFilterOptionsV2(actor, taskFilterRequest))
                .build();
    }

    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Search tasks",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/search")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskSearchResponse> searchTasks(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @QueryParam("actor") @NotNull @NotEmpty String actor,
                                                           @Valid @NotNull TaskSearchRequest taskSearchRequest) {

        return GenericResponse.<TaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.search(actor, taskSearchRequest))
                .build();
    }

    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Sync Hotspot",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/hotspots/{sectorId}/sync")
    @ApiKillerMeta(tags = {killswitch.enums.OperationType.READ})
    public GenericResponse<List<HotspotDto>> syncHotspots(
            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile,
            @Valid @BeanParam HotspotSyncRequest hotspotSyncRequest) {
        String actorId = AuthUserDetails.getLegionUserId(agentProfile, null, userPrincipal);
        return GenericResponse.<List<HotspotDto>>builder()
                .success(true)
                .data(hotspotService.syncHotspots(actorId, hotspotSyncRequest))
                .build();
    }

    @POST
    @Path("/hotspots/accessDetails")
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Get Hotspot Access Details",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @ApiKillerMeta(tags = {killswitch.enums.OperationType.READ})
    public GenericResponse<HotspotAccessDetailsResponse> getHotspotAccessDetails(
            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile,
            @QueryParam("actorId")  @NotBlank String actorId,
            HotspotAccessDetailsRequest hotspotAccessDetailsRequest) {
        return GenericResponse.<HotspotAccessDetailsResponse>builder()
                .success(true)
                .data(hotspotService.getHotspotAccessDetails(actorId, hotspotAccessDetailsRequest))
                .build();
    }

    @PUT
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Deactivate Hotspot",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/hotspots/{hotspotId}/deactivate")
    @ApiKillerMeta(tags = {killswitch.enums.OperationType.READ})
    public GenericResponse<HotspotDto> deactivateHotspots(
            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile,
            @PathParam("hotspotId") @NotNull String hotspotId) {
        String actorId = AuthUserDetails.getLegionUserId(agentProfile, null, userPrincipal);
        return GenericResponse.<HotspotDto>builder()
                .success(true)
                .data(hotspotService.deactivateHotspot(actorId, hotspotId))
                .build();
    }

    @GET
    @Timed
    @Operation(summary = "Get Hotspot",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/hotspots/{sectorId}")
    @ApiKillerMeta(tags = {killswitch.enums.OperationType.READ})
    public GenericResponse<List<HotspotDto>> getStoredHotspots(
            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile,
            @PathParam("sectorId") @NotBlank String sectorId,
            @QueryParam("hotspotType") @NotBlank String hotspotType,
            @QueryParam("actorId") String actorId) {
        return GenericResponse.<List<HotspotDto>>builder()
                .success(true)
                .data(hotspotService.getStoredHotspots(actorId, sectorId, hotspotType))
                .build();
    }



    @POST
    @Timed
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    @Operation(summary = "Get task present in a GeoJson Polygon of a sector",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/sector/tasks")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskListResponse> getTaskInSectorGeoJson(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid TasksInPolygonRequest taskSearchRequest) {

        return GenericResponse.<TaskListResponse>builder()
                .success(true)
                .data(taskHousekeepingService.getTaskInGeoFenceOfSector(taskSearchRequest))
                .build();
    }

    @GET
    @Timed
    @RolesAllowed(value = "housekeeping")
    @Operation(summary = "Get Lending Task Verification Status",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/lending/status/{entityId}/{workflowId}")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<Boolean> getLendingTaskVerificationStatus(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("entityId") @NotNull @NotEmpty String entityId,
                                                                     @PathParam("workflowId") @NotNull @NotEmpty String workflowId) {

        return GenericResponse.<Boolean>builder()
                .success(true)
                .data(hermodService.getLendingTaskVerificationStatus(entityId, workflowId))
                .build();
    }


    @Path("create/client/task")
    @POST
    @Timed
    @ExceptionMetered
    @Authorize(value = "clientTaskCreate")
    @RolesAllowed(value = "clientTaskCreate")
    @Operation(summary = "Create task for client without KS")
    @LegionGateKeeper
    public GenericResponse<Boolean> createAndAssignClientTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                              @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                              @Valid ClientTaskCreateAndAssignRequest request,
                                                              @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        log.info("Creating task for client with request: {}", request);
        request.setCreatedBy(actor);
        try {
            return GenericResponse.<Boolean>builder()
                    .success(true)
                    .data(TaskActionMessagePublisher.createAndAssignTaskForClient(
                            ClientTaskCreateAndAssignMessage.builder()
                                    .request(request)
                                    .build()))
                    .build();
        } catch (Exception e) {
            throw LegionException.propagate(LegionTaskErrorCode.CLIENT_TASK_QUEUE_PUSH_FAILED, e);
        }
    }

    @GET
    @Path("/{taskInstanceId}")
    @Timed
    @Operation(summary = "Fetch Task Instance without KS")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    public GenericResponse<TaskInstance> get(@NotNull @PathParam("taskInstanceId") String taskInstanceId) {
        TaskInstance response = taskInstanceManagementService.getById(
                TaskByIdRequest.builder()
                        .taskInstanceId(taskInstanceId)
                        .build()
        );
        return GenericResponse.<TaskInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }


    @GET
    @Path("/atlas/sector")
    @Timed
    @Operation(summary = "Fetch sector from latitude and longitude")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    public GenericResponse<List<String>> getSector(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                             @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                             @NotNull @QueryParam("latitude") double latitude,
                                             @NotNull @QueryParam("longitude") double longitude) {

        return GenericResponse.<List<String>>builder()
                .success(true)
                .data(atlasService.getSectorIdByLatLong(latitude, longitude))
                .build();
    }

    @GET
    @Path("/atlas/sector/{sectorId}")
    @Timed
    @Operation(summary = "Fetch sector detaiils from sectorId")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    public GenericResponse<Shape> getSectorDetailsFromSectorId(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                               @NotNull @PathParam("sectorId") String sectorId) {

        return GenericResponse.<Shape>builder()
                .success(true)
                .data(atlasService.getSectorCoordinates(sectorId))
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Get task/docs within a geo-fence")
    @Path("/getEsDocsWithinGeoFence")
    @RolesAllowed(value = "housekeeping")
    @ExceptionMetered
    public GenericResponse<EsDocScrollResponse> getEsDocsWithinGeoFence(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                                        @Valid GeoFenceRemappingSearchRequest request) {
        return GenericResponse.<EsDocScrollResponse>builder()
                .success(true)
                .data(taskHousekeepingService.getEsDocsWithinGeoFence(request))
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Search tasks",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/search/sector")
    @RolesAllowed(value = "housekeeping")
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<TaskSearchResponse> searchTasks(@QueryParam("actor") String actor,
                                                           @Valid @NotNull TaskSearchRequest taskSearchRequest) {
        return GenericResponse.<TaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.search(actor, taskSearchRequest))
                .build();
    }

}
