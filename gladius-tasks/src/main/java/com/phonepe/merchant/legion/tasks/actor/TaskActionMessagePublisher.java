package com.phonepe.merchant.legion.tasks.actor;

import com.phonepe.merchant.gladius.models.entitystore.ActorMessageType;
import com.phonepe.merchant.legion.core.actor.ActorMessage;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.rabbitmq.ActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskDeleteMessage;
import com.phonepe.merchant.legion.tasks.actor.message.ClientTaskVerificationMessage;
import com.phonepe.merchant.legion.tasks.actor.message.EventBasedTaskCreationMessage;
import com.phonepe.merchant.legion.tasks.actor.message.HotspotSyncMessage;
import com.phonepe.merchant.legion.tasks.actor.message.QcTaskCreateAndAssignMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskEsChangeEventPublishMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskEsUpdateRetryMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskRecreationScheduleMessage;
import com.phonepe.merchant.legion.tasks.actor.message.TaskVerifyScheduleMessage;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class TaskActionMessagePublisher extends ActionMessagePublisher {

    public static Boolean taskEsUpdateRetry(TaskEsUpdateRetryMessage message) {
        try {
            actors.get(ActorMessageType.TASK_ES_UPDATE_RETRY).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.TASK_ES_UPDATE_RETRY, e);
            return false;
        }
    }

    public static Boolean taskEsChangeEventPublishMessage(TaskEsChangeEventPublishMessage message) {
        try {
            actors.get(ActorMessageType.TASK_ES_CHANGE_EVENT).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.TASK_ES_CHANGE_EVENT, e);
            return false;
        }
    }

    public static Boolean taskScheduleVerificationRequest(TaskVerifyScheduleMessage message) {
        try {
            actors.get(ActorMessageType.TASK_SCHEDULE_VERIFICATION).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.TASK_SCHEDULE_VERIFICATION, e);
            return false;
        }
    }

    public static Boolean createAndAssignQcTask(QcTaskCreateAndAssignMessage message) {
        try {
            actors.get(ActorMessageType.QC_TASK_CREATE_EVENT).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.QC_TASK_CREATE_EVENT, e);
            return false;
        }
    }

    public static Boolean createTaskFromEvent(EventBasedTaskCreationMessage message) {
        try {
            actors.get(ActorMessageType.TASK_CREATION_FROM_EVENT).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.TASK_CREATION_FROM_EVENT, e);
            return false;
        }
    }

    public static <T extends ActorMessage & AgentActionVerifier> Boolean taskVerificationRequest(T message) {
        try {
            actors.get(message.getActorMessageType()).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, message.getActorMessageType(), e);
            return false;
        }
    }

    public static Boolean recreateTask(TaskRecreationScheduleMessage message) {
        try {
            actors.get(ActorMessageType.TASK_RECREATION).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.TASK_RECREATION);
            return false;
        }
    }

    public static Boolean deleteTask(ClientTaskDeleteMessage message) {
        try {
            actors.get(ActorMessageType.CLIENT_TASK_DELETE).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.CLIENT_TASK_DELETE);
            return false;
        }
    }

    public static Boolean verifyTask(ClientTaskVerificationMessage message) {
        try {
            actors.get(ActorMessageType.CLIENT_TASK_MANUAL_VERIFICATION).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.CLIENT_TASK_MANUAL_VERIFICATION);
            return false;
        }
    }

    public static Boolean createAndAssignTaskForClient(ClientTaskCreateAndAssignMessage message) {
        try {
            log.info("Pushing msg in the task creation queue {}", message);
            actors.get(ActorMessageType.CLIENT_TASK_CREATE_AND_ASSIGN).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.CLIENT_TASK_CREATE_AND_ASSIGN);
            throw LegionException.propagate(LegionTaskErrorCode.CLIENT_TASK_QUEUE_PUSH_FAILED, e);
        }
    }

    public static Boolean sectorHotspotSync(HotspotSyncMessage message) {
        try {
            log.info("Pushing msg in the sector hotspot sync queue {}", message);
            actors.get(ActorMessageType.HOTSPOT_SYNC).publish(message);
            return true;
        } catch (Exception e) {
            log.error(errorQueueMessage, ActorMessageType.HOTSPOT_SYNC);
           return false;
        }
    }
}
