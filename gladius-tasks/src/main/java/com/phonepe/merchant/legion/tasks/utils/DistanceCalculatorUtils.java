package com.phonepe.merchant.legion.tasks.utils;

import com.phonepe.models.common.Location;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.val;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DistanceCalculatorUtils {
    private static final double EARTH_RADIUS = 6371 * 1000.0; // Radius of the earth in meters

    /**
     * Haversine
     * formula:	a = sin²(Δφ/2) + cos φ1 ⋅ cos φ2 ⋅ sin²(Δλ/2)
     * c = 2 ⋅ atan2( √a, √(1−a) )
     * d = R ⋅ c
     * where	φ is latitude, λ is longitude, R is earth’s radius (mean radius = 6,371km);
     * note that angles need to be in radians to pass to trig functions!
     * @param lat1
     * @param lng1
     * @param lat2
     * @param lng2
     * @return
     */

    public static double getDistanceFromLatLonInMeters(double lat1, double lng1, double lat2, double lng2) {
        val dLat = deg2rad(lat2 - lat1);  // deg2rad below
        val dLon = deg2rad(lng2 - lng1);
        val a =
                Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                        Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
                                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c; // Distance in meters
    }

    public static double getDistanceFromLocationInMeters(Location loc1, Location loc2) {
        return getDistanceFromLatLonInMeters(loc1.getLatitude(), loc1.getLongitude(), loc2.getLatitude(), loc2.getLongitude());
    }

    public static double getDistanceFromLocationInKiloMeters(double lat1, double lng1, double lat2, double lng2) {
        return getDistanceFromLatLonInMeters(lat1, lng1, lat2, lng2) / 1000.0;
    }

    private static double deg2rad(double deg) {
        return deg * (Math.PI / 180);
    }
}
