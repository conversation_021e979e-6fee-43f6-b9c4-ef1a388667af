package com.phonepe.merchant.legion.tasks.search.query;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.RestrictionQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.ViewKillSwitchExecutor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public abstract class BaseDiscoverySearchRequestQueryBuilder<T> extends BaseSearchRequestQueryBuilder<T> {

    private static final List<LegionTaskStateMachineState> discoverableStates = LegionTaskStateMachineState.getDiscoverableStates();

    protected BaseDiscoverySearchRequestQueryBuilder(LegionService legionService,
                                                     ViewKillSwitchExecutor viewKillSwitchExecutor,
                                                     RestrictionQueryBuilder restrictionQueryBuilder) {
        super(discoverableStates, legionService, viewKillSwitchExecutor, restrictionQueryBuilder);
    }

}
