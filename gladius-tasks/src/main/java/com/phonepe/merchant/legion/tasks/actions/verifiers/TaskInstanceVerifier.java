package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.models.merchants.tasks.EntityType;
import com.utils.StringUtils;

import java.util.Map;
import java.util.Optional;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_INSTANCE_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;

@ActionVerifierMarker(name = TASK_INSTANCE_VERIFIER)
public class TaskInstanceVerifier extends ActionVerifier {

    private final TaskInstanceRepository taskInstanceRepository;

    @Inject
    public TaskInstanceVerifier(TaskInstanceRepository taskInstanceRepository) {
        this.taskInstanceRepository = taskInstanceRepository;
    }

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.PHONE_NUMBER) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }


    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig,
                                   Map<String, Object> context) {
        return VerifierResponse.builder()
                .verified(true)
                .context(context)
                .build();
    }

    @Override
    public boolean validateTaskCreation(CreateTaskInstanceRequest instanceRequest,
                                        TaskActionInstance actionInstance) {
        String taskInstanceId = Optional.ofNullable(instanceRequest.getTaskInstanceMeta())
                .map(TaskInstanceMeta::getTaskMetaList)
                .flatMap(list -> list.stream()
                        .filter(meta -> TaskMetaType.TASK_INSTANCE_ID.equals(meta.getType()))
                        .map(meta -> (String) meta.getValue())
                        .findFirst())
                .orElse(null);

        if (StringUtils.isBlank(taskInstanceId)) {
            return false;
        }
        if (taskInstanceRepository.get(taskInstanceId).isEmpty()) {
            throw LegionException.error(INVALID_TASK_INSTANCE_ID,
                    Map.of(MESSAGE, "Invalid task linked, Lead cannot be created."));
        }
        return true;
    }
}
