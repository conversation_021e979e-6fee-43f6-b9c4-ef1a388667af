package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.Operator;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.filter.TaskFilterRequest;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import com.phonepe.merchant.legion.tasks.search.query.filter.TaskFilterRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.response.filter.RequestViewWiseFilterFactory;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskFilterService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ATTRIBUTES;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils.getParsedLongTermsFromEsSearchResponse;
import static com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils.getParsedStringTermsFromEsSearchResponse;
import static com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils.toTitleCase;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT_AGGREGATION;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT_FIELD;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.OBJECTIVES_KEYWORD;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getActionFilterFromMultiAgg;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getFilterValuesFromAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getLeadFilterFromAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getLeadIntentAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getPointAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getPointFilterFromMultiAgg;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskActionAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskObjectiveAggregation;

@Slf4j
@Singleton
@AllArgsConstructor
public class TaskFilterServiceImpl implements TaskFilterService {

    private final TaskActionRepository taskActionRepository;
    private final ESRepository esRepository;
    private final TaskFilters taskFilters;
    private final TaskFiltersV2 taskFiltersV2;
    private final TaskFilterRequestQueryBuilderFactory requestFilterGenerator;
    private final RequestViewWiseFilterFactory requestViewWiseFilterFactory;
    private final TaskAttributeService taskAttributeService;
    private static final String ACTION_MISSING_TEXT = "This action is missing";


    @Inject
    public TaskFilterServiceImpl(TaskActionRepository taskActionRepository,
                                 ESRepository esRepository,
                                 TaskFilterRequestQueryBuilderFactory requestFilterGenerator,
                                 RequestViewWiseFilterFactory requestViewWiseFilterFactory,
                                 TaskAttributeService taskAttributeService,
                                 ChimeraLiteRepository chimeraLiteRepository) {
        this.taskActionRepository = taskActionRepository;
        this.esRepository = esRepository;
        this.taskFilters = chimeraLiteRepository.getStaticFilters();
        this.taskFiltersV2 = chimeraLiteRepository.getStaticFiltersV2();
        this.requestFilterGenerator = requestFilterGenerator;
        this.requestViewWiseFilterFactory = requestViewWiseFilterFactory;
        this.taskAttributeService = taskAttributeService;
    }

    private SearchResponse getElasticSearchAggregationResponse(String actor, TaskFilterRequest taskFilterRequest) {
        QueryBuilder boolQueryBuilder = requestFilterGenerator.enrichFilter(actor, taskFilterRequest);
        AggregationBuilder pointAggregationBuilder = getPointAggregation();
        AggregationBuilder actionAggregationBuilder = getTaskActionAggregation();
        AggregationBuilder objectiveAggregationBuilder = getTaskObjectiveAggregation();
        AggregationBuilder leadIntentAggregationBuilder = getLeadIntentAggregation();

        return esRepository.getAggregationResult(TASK_INDEX, boolQueryBuilder,
                Arrays.asList(leadIntentAggregationBuilder, pointAggregationBuilder, actionAggregationBuilder, objectiveAggregationBuilder));
    }

    private Map<String, List<FilterOptions>> getFilterOptions(SearchResponse searchResponse) {
        if (searchResponse != null) {
            List<Long> pointFilterList = getPointFilterFromMultiAgg(getParsedLongTermsFromEsSearchResponse(searchResponse, POINTS));
            List<String> actionIdList = getActionFilterFromMultiAgg(getParsedStringTermsFromEsSearchResponse(searchResponse, ACTION_ID));
            List<String> objectiveList = getFilterValuesFromAggregation(getParsedStringTermsFromEsSearchResponse(searchResponse, ATTRIBUTES));

            Map<String, List<FilterOptions>> taskTypeFilters = new HashMap<>();
            taskTypeFilters.put(ACTION_ID, getActionIdsToTaskFilters(actionIdList));
            taskTypeFilters.put(POINTS, getPointsToTaskFilters(pointFilterList));
            taskTypeFilters.put(OBJECTIVES, getObjectivesToTaskFilters(objectiveList));
            return taskTypeFilters;
        }
        return Collections.emptyMap();
    }

    private Map<String, List<FilterOptionsV2>> getFilterOptionsV2(SearchResponse searchResponse) {
        if(searchResponse != null) {
            List<Long> pointFilterList = getPointFilterFromMultiAgg(getParsedLongTermsFromEsSearchResponse(searchResponse, POINTS));
            List<String> actionIdList = getActionFilterFromMultiAgg(getParsedStringTermsFromEsSearchResponse(searchResponse, ACTION_ID));
            List<String> objectiveList = getFilterValuesFromAggregation(getParsedStringTermsFromEsSearchResponse(searchResponse, ATTRIBUTES));
            List<String> leadList = getLeadFilterFromAggregation(getParsedStringTermsFromEsSearchResponse(searchResponse, LEAD_INTENT_AGGREGATION));
            Map<String, List<FilterOptionsV2>> taskTypeFilters = new HashMap<>();
            taskTypeFilters.put(ACTION_ID, getActionIdsToTaskFiltersV2(actionIdList));
            taskTypeFilters.put(POINTS, getPointsToTaskFiltersV2(pointFilterList));
            taskTypeFilters.put(OBJECTIVES, getObjectivesToTaskFiltersV2(objectiveList));
            taskTypeFilters.put(LEAD_INTENT, getLeadToTaskFiltersV2(leadList));
            return taskTypeFilters;
        }
        return Collections.emptyMap();
    }

    private List<FilterOptions> getActionIdsToTaskFilters(List<String> actionIds) {
        return actionIds.stream().map(actionId -> {
            String taskDescription = taskActionRepository.get(actionId)
                    .orElseThrow(() -> LegionException.error(CoreErrorCode.NOT_FOUND,
                            Collections.singletonMap(MESSAGE, ACTION_MISSING_TEXT + actionId))).getDescription();
            return FilterOptions.builder().key(actionId).fieldName("action_id").displayText(taskDescription).build();
        }).sorted(Comparator.comparing(FilterOptions::getDisplayText)).toList();
    }

    private List<FilterOptionsV2> getActionIdsToTaskFiltersV2(List<String> actionIds) {
        return actionIds.stream().map(actionId -> {
            String taskDescription = taskActionRepository.get(actionId)
                    .orElseThrow(() -> LegionException.error(CoreErrorCode.NOT_FOUND,
                            Collections.singletonMap(MESSAGE, ACTION_MISSING_TEXT + actionId))).getDescription();
            return FilterOptionsV2.builder().key(actionId).operator(Operator.EQUALS).field(ACTION_ID).displayText(taskDescription).build();
        }).sorted(Comparator.comparing(FilterOptionsV2::getDisplayText)).toList();
    }

    private List<FilterOptions> getPointsToTaskFilters(List<Long> points) {
        return points.stream().map(point ->
                FilterOptions.builder().key(String.valueOf(point))
                        .fieldName("points").displayText(String.valueOf(point)).build()
        ).sorted(Comparator.comparingInt(o -> Integer.parseInt(o.getDisplayText()))).toList();
    }

    private List<FilterOptionsV2> getPointsToTaskFiltersV2(List<Long> points) {
        return points.stream().map(point ->
                FilterOptionsV2.builder().key(String.valueOf(point))
                        .field(POINTS)
                        .operator(Operator.EQUALS)
                        .displayText(String.valueOf(point)).build()
        ).sorted(Comparator.comparingInt(o -> Integer.parseInt(o.getDisplayText()))).toList();
    }

    private List<FilterOptionsV2> getLeadToTaskFiltersV2(List<String> intents) {
        return intents.stream().map(intent ->
                FilterOptionsV2.builder().key(intent)
                        .field(LEAD_INTENT_FIELD)
                        .operator(Operator.EQUALS)
                        .displayText(toTitleCase(intent)).build()
        ).sorted(Comparator.comparing(FilterOptionsV2::getDisplayText)).toList();
    }

    private List<FilterOptions> getObjectivesToTaskFilters(List<String> objectiveList) {
        return objectiveList.stream().map(objective -> {
            String objectiveName = taskAttributeService.getFromCache(objective).getName();
            return FilterOptions.builder()
                    .key(objective)
                    .fieldName(OBJECTIVES)
                    .displayText(objectiveName)
                    .build();
        }).sorted(Comparator.comparing(FilterOptions::getDisplayText)).toList();
    }

    private List<FilterOptionsV2> getObjectivesToTaskFiltersV2(List<String> objectiveList) {
        return objectiveList.stream().map(objective -> {
            String objectiveName = taskAttributeService.getFromCache(objective).getName();
            return FilterOptionsV2.builder()
                    .key(objective)
                    .field(OBJECTIVES_KEYWORD)
                    .operator(Operator.EQUALS)
                    .displayText(objectiveName)
                    .build();
        }).sorted(Comparator.comparing(FilterOptionsV2::getDisplayText)).toList();
    }

    @Override
    public ViewWiseFilters getTaskFilterOptions(String actor, TaskFilterRequest taskFilterRequest) {
        SearchResponse searchResponse = getElasticSearchAggregationResponse(actor, taskFilterRequest);
        Map<String, List<FilterOptions>> filterOptions = getFilterOptions(searchResponse);

        return requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions,
                taskFilterRequest.getTaskSearchRequestType());
    }

    @Override
    public ViewWiseFiltersV2 getTaskFilterOptionsV2(String actor, TaskFilterRequest taskFilterRequest) {
        SearchResponse searchResponse = getElasticSearchAggregationResponse(actor, taskFilterRequest);
        Map<String, List<FilterOptionsV2>> filterOptions = getFilterOptionsV2(searchResponse);

        return requestViewWiseFilterFactory.generateViewWiseFilterV2(taskFiltersV2, filterOptions,
                taskFilterRequest.getTaskSearchRequestType());
    }

}
