package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.models.merchants.tasks.EntityType;

import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.PHONE_NUMBER_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_ENTITY_ID;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;

@ActionVerifierMarker(name = PHONE_NUMBER_VERIFIER)
public class PhoneNumberVerifier extends ActionVerifier {

    private static final String PHONE_NUMBER_REGEX = "\\d{10}";

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.PHONE_NUMBER) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        return VerifierResponse.builder()
                .verified(true)
                .context(context)
                .build();
    }

    @Override
    public boolean validateTaskCreation(CreateTaskInstanceRequest instanceRequest, TaskActionInstance actionInstance) {
        String entityId = instanceRequest.getEntityId();
        if (entityId == null) {
            throw LegionException.error(
                    INVALID_ENTITY_ID,
                    Map.of(MESSAGE, "Entity ID cannot be null.")
            );
        }

        if (!entityId.matches(PHONE_NUMBER_REGEX)) {
            throw LegionException.error(
                    INVALID_ENTITY_ID,
                    Map.of(MESSAGE, "Entity ID " + entityId + " is not a valid 10-digit phone number.")
            );
        }
        return true;
    }
}
