package com.phonepe.merchant.legion.tasks.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.phonepe.merchant.gladius.models.survey.request.FeedbackRequest;
import com.phonepe.merchant.gladius.models.survey.request.FeedbackRequestFailureEvent;
import com.phonepe.merchant.gladius.models.survey.request.FeedbackRequestFoxtrotEvent;
import com.phonepe.merchant.gladius.models.survey.request.FormConfigRequest;
import com.phonepe.merchant.gladius.models.survey.request.FormConfigRequestFailureEvent;
import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EventBasedTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskCreatedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskCreationFailedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskDeletionEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskDeletionFailedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskRequestedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskVerificationFailedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.ClientTaskVerifiedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.CommentOnTaskEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.EventBasedTaskCreationFailed;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.EventBasedTaskCreationSuccess;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.QueuePushFailedEvent;
import com.phonepe.merchant.legion.core.models.BrickbatFlatFeedbackRequest;
import com.phonepe.merchant.legion.core.models.BrickbatFlatFeedbackRequestFailureEvent;
import com.phonepe.merchant.legion.core.models.BrickbatFormsRequestV2;
import com.phonepe.merchant.legion.core.models.BrickbatFormsRequestV2FailureEvent;
import com.phonepe.merchant.legion.core.models.BrickbatSubmitFeedbackRequest;
import com.phonepe.merchant.legion.core.models.BrickbatSubmitFeedbackRequestFailureEvent;
import com.phonepe.merchant.legion.core.utils.CommonUtils;
import com.phonepe.merchant.legion.core.utils.CoreFoxtrotEventUtils;
import com.phonepe.merchant.legion.core.utils.EventConstants;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.eventingestion.models.BulkSectorFetchErrorEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.BulkSectorUpdateFailedEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.FailedTaskRecreatedEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.ForceTaskCompletionEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.TaskDefinitionEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.TaskEsChangeEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.TaskEsChangeEventV2;
import com.phonepe.merchant.legion.tasks.eventingestion.models.TaskFoxtrotEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.TaskMetaUpdateEvent;
import com.phonepe.merchant.legion.tasks.eventingestion.models.ToggleTagEvent;
import com.phonepe.platform.eventingestion.model.Event;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.phonepe.merchant.gladius.models.utils.GenericUtil.getMidAndSidFromEid;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.CREATE_FEEDBACK_AND_AUDIT_FAILURE_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.FEEDBACK_CREATION_OR_UPDATION_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.FEEDBACK_CREATION_OR_UPDATION_FAILURE_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.FORM_CONFIG_CREATION_OR_UPDATION_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.FORM_CONFIG_CREATION_OR_UPDATION_FAILURE_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.GET_BRICKBAT_FORM_AND_FEEDBACK_FAILURE_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.FormEvents.GET_FLAT_MERCHANT_STORE_FEEDBACK_FAILURE_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.SurveyEvents.EVENT_BASED_TASK_CREATION_FAILURE;
import static com.phonepe.merchant.legion.core.utils.EventConstants.SurveyEvents.EVENT_BASED_TASK_CREATION_SUCCESS;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TagEvents.SAVE_TAG;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TagEvents.TOGGLE_TAG;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.BULK_SECTOR_FETCH_ERROR;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.BULK_SECTOR_UPDATE_FAILED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CAMPAIGN_CREATION;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_CREATED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_CREATION_FAILED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_DELETED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_DELETE_EVENT_RECEIVED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_DELETION_FAILED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_QUEUE_PUSH_FAILED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_QUEUE_PUSH_SUCCESS;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_REQUESTED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_VERIFICATION_FAILED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_VERIFIED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLIENT_TASK_VERIFY_EVENT_RECEIVED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.COMMENT_CREATION_SUCCESS;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.FAILED_TASK_RECREATED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.FORCE_TASK_COMPLETION;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.TASK_META_UPDATE;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.TASK_UPDATE_V2;

@Slf4j
public class TaskFoxtrotEventUtils {

    private TaskFoxtrotEventUtils() {
    }

    private static final String STATE_TRANSITION_ERROR_MESSAGE = "There is an exception trying to construct a foxtrot event for state machine transition event for eventName";
    private static final String MESSAGE = "There is an exception trying to construct a foxtrot event for {} {}";

    public static Event<TaskFoxtrotEvent> toScheduleTaskVerificationFoxtrotEvent(TaskCompleteRequest taskCompleteRequest, StoredTaskInstance storedTaskInstance, Date scheduledDate) {
        try {
            final TaskFoxtrotEvent data = TaskFoxtrotEvent.builder()
                    .actor(taskCompleteRequest.getCompletedBy())
                    .meta(taskCompleteRequest.getMeta())
                    .scheduledDate(scheduledDate)
                    .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                    .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                    .points(storedTaskInstance.getPoints())
                    .campaignId(storedTaskInstance.getCampaignId())
                    .build();

            return Event.<TaskFoxtrotEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.TaskEvents.SCHEDULE_TASK_VERIFICATION.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(storedTaskInstance.getTaskInstanceId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error("There is an exception trying to construct a foxtrot event for task event for eventName", e);
            return null;
        }
    }

    public static Event<TaskEsChangeEvent> toTaskInstanceUpdateFoxtrotEvent(
            DiscoveryTaskInstance discoveryTaskInstance) {
        try {
            String taskSector = null;
            if (!CommonUtils.isNullOrEmpty(discoveryTaskInstance.getPolygonIds())) {
                taskSector = discoveryTaskInstance.getPolygonIds().get(0);
            }
            final TaskEsChangeEvent data = TaskEsChangeEvent.builder()
                    .discoveryTaskInstance(discoveryTaskInstance)
                    .sectorId(taskSector)
                    .build();

            return Event.<TaskEsChangeEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.TaskEvents.TASK_UPDATE.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(discoveryTaskInstance.getTaskInstanceId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error("There is an exception trying to construct a foxtrot event for task instance update event for eventName", e);
            return null;
        }
    }

    public static Event<TaskEsChangeEventV2> toTaskInstanceUpdateV2FoxtrotEvent(
            DiscoveryTaskInstance discoveryTaskInstance) {
        try {
            String taskSector = null;
            if (!CommonUtils.isNullOrEmpty(discoveryTaskInstance.getPolygonIds())) {
                taskSector = discoveryTaskInstance.getPolygonIds().get(0);
            }

            final TaskEsChangeEventV2 data = TaskEsChangeEventV2.builder()
                    .discoveryTaskInstance(discoveryTaskInstance)
                    .sectorId(taskSector)
                    .build();

            return CoreFoxtrotEventUtils.getEventObject(data, discoveryTaskInstance.getTaskInstanceId(),
                    TASK_UPDATE_V2);

        } catch (Exception e) {
            log.error("There is an exception trying to construct a foxtrot event v2 for task instance update event for eventName", e);
            return null;
        }
    }

    public static Event<FailedTaskRecreatedEvent> toFailedTaskRecreatedEvent(StoredTaskInstance oldStoredTaskInstance, StoredTaskInstance newStoredTaskInstance) {
        try {

            final FailedTaskRecreatedEvent data = new FailedTaskRecreatedEvent(oldStoredTaskInstance,
                    newStoredTaskInstance);

            return CoreFoxtrotEventUtils.getEventObject(data, newStoredTaskInstance.getTaskInstanceId(),
                    FAILED_TASK_RECREATED);
        } catch (Exception e) {
            log.error("There is an exception trying to construct a foxtrot event for task event for eventName", e);
            return null;
        }
    }

    public static Event<TaskFoxtrotEvent> toTaskCompletionFoxtrotEvent(TaskCompleteRequest taskCompleteRequest, StoredTaskInstance storedTaskInstance) {
        try {

            final TaskFoxtrotEvent data = TaskFoxtrotEvent.builder()
                    .taskMetadata(getTaskMetaAsMap(storedTaskInstance))
                    .meta(taskCompleteRequest.getMeta())
                    .actor(taskCompleteRequest.getCompletedBy())
                    .entityId(storedTaskInstance.getEntityId())
                    .merchantId(getMidAndSidFromEid(storedTaskInstance.getEntityId())[0])
                    .storeId(getMidAndSidFromEid(storedTaskInstance.getEntityId())[1])
                    .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                    .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                    .points(storedTaskInstance.getPoints())
                    .campaignId(storedTaskInstance.getCampaignId())
                    .state(LegionTaskStateMachineState.COMPLETED)
                    .build();

            return Event.<TaskFoxtrotEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.TaskEvents.TASK_COMPLETION.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(storedTaskInstance.getTaskInstanceId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error(STATE_TRANSITION_ERROR_MESSAGE, e);
            return null;
        }
    }

    public static Event<TaskFoxtrotEvent> toTaskVerificationFoxtrotEvent(TaskCompleteRequest taskCompleteRequest,
            StoredTaskInstance storedTaskInstance) {
        try {
            final TaskFoxtrotEvent data = TaskFoxtrotEvent.builder()
                    .actor(taskCompleteRequest.getCompletedBy())
                    .meta(taskCompleteRequest.getMeta())
                    .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                    .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                    .points(storedTaskInstance.getPoints())
                    .campaignId(storedTaskInstance.getCampaignId())
                    .active(storedTaskInstance.isActive())
                    .build();

            return Event.<TaskFoxtrotEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.TaskEvents.TASK_VERIFICATION.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(storedTaskInstance.getTaskInstanceId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error(STATE_TRANSITION_ERROR_MESSAGE, e);
            return null;
        }
    }

    public static Event<TaskFoxtrotEvent> toPostTaskVerificationFoxtrotEvent(StoredTaskInstance storedTaskInstance) {
        try {

            final TaskFoxtrotEvent data = TaskFoxtrotEvent.builder()
                    .actor(storedTaskInstance.getCurActor())
                    .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                    .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                    .points(storedTaskInstance.getPoints())
                    .state(storedTaskInstance.getCurState())
                    .campaignId(storedTaskInstance.getCampaignId())
                    .active(storedTaskInstance.isActive())
                    .build();
            return Event.<TaskFoxtrotEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.TaskEvents.TASK_VERIFICATION_STATUS.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(storedTaskInstance.getTaskInstanceId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();

        } catch (Exception e) {
            log.error(STATE_TRANSITION_ERROR_MESSAGE, e);
            return null;
        }
    }

    public static Event<TaskDefinitionEvent> toTaskDefinitionInstanceFoxtrotEvent(
            TaskDefinitionInstance taskDefinitionInstance) {
        try {
            final TaskDefinitionEvent data = TaskDefinitionEvent.builder()
                    .actionId(taskDefinitionInstance.getActionId())
                    .createdBy(taskDefinitionInstance.getCreatedBy())
                    .name(taskDefinitionInstance.getName())
                    .namespace(taskDefinitionInstance.getNamespace())
                    .points(taskDefinitionInstance.getPoints())
                    .priority(taskDefinitionInstance.getPriority())
                    .taskDefinitionId(taskDefinitionInstance.getTaskDefinitionId())
                    .updatedBy(taskDefinitionInstance.getUpdatedBy())
                    .build();
            return Event.<TaskDefinitionEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EventConstants.TaskEvents.TASK_DEFINITION_CREATION.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(data)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(taskDefinitionInstance.getTaskDefinitionId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();
        } catch (Exception e) {
            log.error("There is an exception trying to construct a foxtrot event for task definition for eventName", e);
            return null;
        }
    }

    public static Event<Campaign> toCampaignFoxtrotEvent(Campaign campaign) {
        try {
            return Event.<Campaign>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(CAMPAIGN_CREATION.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(campaign)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(campaign.getCampaignId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();
        } catch (Exception e) {
            log.error(MESSAGE, CAMPAIGN_CREATION.name(), e);
            return null;
        }
    }

    public static Event<BulkSectorUpdateFailedEvent> toBulkSectorUpdateFailedEvent(List<String> taskInstanceIds,
            int status) {
        try {
            final BulkSectorUpdateFailedEvent event = BulkSectorUpdateFailedEvent.builder()
                    .taskInstanceIds(taskInstanceIds)
                    .status(status)
                    .build();
            return Event.<BulkSectorUpdateFailedEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(BULK_SECTOR_UPDATE_FAILED.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(event)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(UUID.randomUUID().toString())
                    .time(new Date(System.currentTimeMillis()))
                    .build();
        } catch (Exception e) {
            log.error(MESSAGE, BULK_SECTOR_UPDATE_FAILED.name(), e);
            return null;
        }
    }


    public static Event<EventBasedTaskCreationSuccess> toEventBasedTaskCreationSuccess(
            StoredTaskInstance finalTaskInstance, EventBasedTaskCreationRequest request) {
        try {
            EventBasedTaskCreationSuccess eventBasedTaskCreationSuccess = EventBasedTaskCreationSuccess.builder()
                    .actionId(finalTaskInstance.getActionId())
                    .taskDefinitionId(finalTaskInstance.getTaskDefinitionId())
                    .campaignId(finalTaskInstance.getCampaignId())
                    .markAvailable(request.getMarkAvailable())
                    .actor(finalTaskInstance.getCurActor())
                    .taskInstanceId(finalTaskInstance.getTaskInstanceId())
                    .entityId(finalTaskInstance.getEntityId())
                    .client(request.getClient())
                    .build();
            return Event.<EventBasedTaskCreationSuccess>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EVENT_BASED_TASK_CREATION_SUCCESS.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(eventBasedTaskCreationSuccess)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(eventBasedTaskCreationSuccess.getEntityId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();
        } catch (Exception e) {
            log.error(MESSAGE, EVENT_BASED_TASK_CREATION_SUCCESS.name(), e);
            return null;
        }
    }

    public static Event<EventBasedTaskCreationFailed> toEventBasedTaskCreationFailed(
            EventBasedTaskCreationRequest request, String errorMessage) {
        try {
            EventBasedTaskCreationFailed eventBasedTaskCreationFailed = EventBasedTaskCreationFailed.builder()
                    .agentId(request.getAgentId())
                    .role(request.getRole())
                    .campaignId(request.getCampaignId())
                    .entityId(request.getEntityId())
                    .markAvailable(request.getMarkAvailable())
                    .taskDefinitionId(request.getTaskDefinitionId())
                    .errorMessage(errorMessage)
                    .client(request.getClient())
                    .taskType(request.getTaskType())
                    .build();
            return Event.<EventBasedTaskCreationFailed>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(EVENT_BASED_TASK_CREATION_FAILURE.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(eventBasedTaskCreationFailed)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(eventBasedTaskCreationFailed.getEntityId())
                    .time(new Date(System.currentTimeMillis()))
                    .build();
        } catch (Exception ex) {
            log.error(MESSAGE, EVENT_BASED_TASK_CREATION_FAILURE.name(), ex);
            return null;
        }
    }

    public static Event<BulkSectorFetchErrorEvent> toBulkSectorFetchError(String taskInstanceId,
            EsLocationRequest location, List<String> oldSectors, String errorMessage) {
        try {
            final BulkSectorFetchErrorEvent event = BulkSectorFetchErrorEvent.builder()
                    .taskInstanceId(taskInstanceId)
                    .location(location)
                    .oldSectors(oldSectors)
                    .errorMessage(errorMessage)
                    .build();
            return Event.<BulkSectorFetchErrorEvent>builder()
                    .app(EventConstants.EVENT_APP_NAME)
                    .eventType(BULK_SECTOR_FETCH_ERROR.name())
                    .id(UUID.randomUUID().toString())
                    .eventData(event)
                    .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                    .groupingKey(UUID.randomUUID().toString())
                    .time(new Date(System.currentTimeMillis()))
                    .build();
        } catch (Exception e) {
            log.error(MESSAGE, BULK_SECTOR_FETCH_ERROR.name(), e);
            return null;
        }
    }

    public static Event<StoreTag> saveTagEvent(StoreTag storeTag) {
        return Event.<StoreTag>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(SAVE_TAG.name())
                .id(UUID.randomUUID().toString())
                .eventData(storeTag)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ToggleTagEvent> toggleTagEvent(StoreTag storeTag) {
        ToggleTagEvent toggleTagEvent = ToggleTagEvent.builder()
                .tag(storeTag.getTagId())
                .tenantType(storeTag.getTenantType())
                .active(storeTag.isActive())
                .build();
        return Event.<ToggleTagEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(TOGGLE_TAG.name())
                .id(UUID.randomUUID().toString())
                .eventData(toggleTagEvent)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskCreatedEvent> toClientTaskCreatedEvent(StoredTaskInstance storedTaskInstance,
            ClientTaskCreateAndAssignRequest request) {
        ClientTaskCreatedEvent event = ClientTaskCreatedEvent.builder()
                .assigneeId(request.getAssigneeId())
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .createdBy(request.getCreatedBy())
                .taskActionId(storedTaskInstance.getActionId())
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .campaignId(storedTaskInstance.getCampaignId())
                .entityId(storedTaskInstance.getEntityId())
                .taskInstanceMeta(request.getTaskInstanceMeta() != null ? request.getTaskInstanceMeta()
                        .getTaskMetaAsMap() : null)
                .build();

        return Event.<ClientTaskCreatedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_CREATED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskRequestedEvent> toClientTaskRequestedEvent(ClientTaskCreateAndAssignRequest request) {
        ClientTaskRequestedEvent event = ClientTaskRequestedEvent.builder()
                .assigneeId(request.getAssigneeId())
                .createdBy(request.getCreatedBy())
                .taskDefinitionId(request.getTaskDefinitionId())
                .campaignId(request.getCampaignId())
                .entityId(request.getEntityId())
                .build();

        return Event.<ClientTaskRequestedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_REQUESTED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskRequestedEvent> toQueuePushSuccessEvent(ClientTaskCreateAndAssignRequest request) {
        ClientTaskRequestedEvent event = ClientTaskRequestedEvent.builder()
                .assigneeId(request.getAssigneeId())
                .createdBy(request.getCreatedBy())
                .taskDefinitionId(request.getTaskDefinitionId())
                .campaignId(request.getCampaignId())
                .entityId(request.getEntityId())
                .build();

        return Event.<ClientTaskRequestedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_QUEUE_PUSH_SUCCESS.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<QueuePushFailedEvent> toQueuePushFailedEvent(ClientTaskCreateAndAssignRequest request,
            Exception e) {
        QueuePushFailedEvent event = QueuePushFailedEvent.builder()
                .assigneeId(request.getAssigneeId())
                .createdBy(request.getCreatedBy())
                .taskDefinitionId(request.getTaskDefinitionId())
                .campaignId(request.getCampaignId())
                .entityId(request.getEntityId())
                .errorMessage(e.getMessage())
                .build();

        return Event.<QueuePushFailedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_QUEUE_PUSH_FAILED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskDeletionEvent> toClientTaskDeletedEvent(ClientTaskDeleteRequest request) {
        ClientTaskDeletionEvent event = ClientTaskDeletionEvent.builder()
                .deletedBy(request.getDeletedBy())
                .reason(request.getReason())
                .taskInstanceId(request.getTaskInstanceId())
                .build();

        return Event.<ClientTaskDeletionEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_DELETED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskVerifiedEvent> toClientTaskVerifiedEvent(TaskManualVerificationRequest request) {
        ClientTaskVerifiedEvent event = ClientTaskVerifiedEvent.builder()
                .taskInstanceId(request.getTaskInstanceId())
                .reason(request.getReason())
                .verifiedBy(request.getVerifiedBy())
                .build();
        return toEvent(event, CLIENT_TASK_VERIFIED);
    }

    public static Event<ClientTaskVerifiedEvent> toClientTaskVerifyEventReceived(
            TaskManualVerificationRequest request) {
        ClientTaskVerifiedEvent event = ClientTaskVerifiedEvent.builder()
                .taskInstanceId(request.getTaskInstanceId())
                .reason(request.getReason())
                .verifiedBy(request.getVerifiedBy())
                .build();
        return toEvent(event, CLIENT_TASK_VERIFY_EVENT_RECEIVED);
    }

    public static Event<ClientTaskCreationFailedEvent> toClientTaskCreationFailedEvent(
            ClientTaskCreateAndAssignRequest request, Exception e) {
        ClientTaskCreationFailedEvent event = ClientTaskCreationFailedEvent.builder()
                .assigneeId(request.getAssigneeId())
                .entityId(request.getEntityId())
                .errorMessage(e.getMessage())
                .markAvailable(request.isMarkAvailable())
                .taskDefinitionId(request.getTaskDefinitionId())
                .taskInstanceMeta(request.getTaskInstanceMeta())
                .campaignId(request.getCampaignId())
                .createdBy(request.getCreatedBy())
                .build();

        return Event.<ClientTaskCreationFailedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_CREATION_FAILED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskDeletionFailedEvent> toClientTaskDeleteEventReceived(
            ClientTaskDeleteRequest request) {
        ClientTaskDeletionFailedEvent event = ClientTaskDeletionFailedEvent.builder()
                .deletedBy(request.getDeletedBy())
                .reason(request.getReason())
                .taskInstanceId(request.getTaskInstanceId())
                .build();
        return Event.<ClientTaskDeletionFailedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_DELETE_EVENT_RECEIVED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskDeletionFailedEvent> toClientTaskDeletionFailed(ClientTaskDeleteRequest request,
            Exception e) {
        ClientTaskDeletionFailedEvent event = ClientTaskDeletionFailedEvent.builder()
                .deletedBy(request.getDeletedBy())
                .reason(request.getReason())
                .taskInstanceId(request.getTaskInstanceId())
                .errorMessage(e.getMessage())
                .build();
        return Event.<ClientTaskDeletionFailedEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(CLIENT_TASK_DELETION_FAILED.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<ClientTaskVerificationFailedEvent> toClientTaskVerificationFailed(
            TaskManualVerificationRequest request, Exception e) {

        ClientTaskVerificationFailedEvent event = ClientTaskVerificationFailedEvent.builder()
                .taskManualVerificationRequest(request)
                .errorMessage(e.getMessage())
                .build();

        return toEvent(event, CLIENT_TASK_VERIFICATION_FAILED);
    }

    public static <T> Event<T> toEvent(T event, EventConstants.TaskEvents eventType) {
        return Event.<T>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(eventType.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Map<TaskMetaType, String> getTaskMetaAsMap(StoredTaskInstance storedTaskInstance) {
        if (CommonUtils.isNullOrEmpty(storedTaskInstance.getInstanceMeta())) {
            return Collections.emptyMap();
        }
        TaskInstanceMeta taskInstanceMeta = SerDe.readValue(storedTaskInstance.getInstanceMeta(),
                new TypeReference<TaskInstanceMeta>() {
                });

        return taskMetaListToMap(taskInstanceMeta.getTaskMetaList());
    }

    public static Map<TaskMetaType, String> taskMetaListToMap(List<TaskMetaInformation> taskMetaList) {
        if (taskMetaList == null) {
            return Collections.emptyMap();
        }
        return taskMetaList.stream()
                .collect(Collectors.toMap(TaskMetaInformation::getType, info -> String.valueOf(info.getValue())));
    }

    public static Event<TaskMetaUpdateEvent> toTaskMetaUpdateEvent(TaskMetaUpdateRequest request,
            DiscoveryTaskInstance taskInstance) {
        final TaskMetaUpdateEvent taskMetaUpdateEvent = TaskMetaUpdateEvent.builder()
                .taskInstance(taskInstance)
                .request(request)
                .build();
        return CoreFoxtrotEventUtils.getEventObject(taskMetaUpdateEvent, request.getTaskInstanceId(), TASK_META_UPDATE);
    }

    public static Event<ForceTaskCompletionEvent> toForceTaskCompletionEvent(StoredTaskInstance taskInstance) {
        final ForceTaskCompletionEvent forceTaskCompletionEvent = ForceTaskCompletionEvent.builder()
                .taskInstanceId(taskInstance.getTaskInstanceId())
                .actor(taskInstance.getCurActor())
                .merchantId(getMidAndSidFromEid(taskInstance.getEntityId())[0])
                .storeId(getMidAndSidFromEid(taskInstance.getEntityId())[1])
                .build();
        return CoreFoxtrotEventUtils.getEventObject(forceTaskCompletionEvent, taskInstance.getTaskInstanceId(),
                FORCE_TASK_COMPLETION);
    }

    public static Event<CommentOnTaskEvent> toCommentOnTaskEvent(StoredCommentsOnTask storedCommentsOnTask) {
        final CommentOnTaskEvent commentOnTaskEvent = CommentOnTaskEvent.builder()
                .commentId(storedCommentsOnTask.getCommentId())
                .agentId(storedCommentsOnTask.getCreatedBy())
                .actionId(storedCommentsOnTask.getTaskInstance()
                        .getActionId())
                .taskInstanceId(storedCommentsOnTask.getTaskInstanceId())
                .entityId(storedCommentsOnTask.getEntityId())
                .build();
        return CoreFoxtrotEventUtils.getEventObject(commentOnTaskEvent, commentOnTaskEvent.getCommentId(),
                COMMENT_CREATION_SUCCESS);
    }

    public static <T> Event<T> toEvent(T event, EventConstants.FormEvents eventType) {
        return Event.<T>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(eventType.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(UUID.randomUUID().toString())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<FeedbackRequestFoxtrotEvent> toFeedbackCreateOrUpdateFoxtrotEvent(
            FeedbackRequest feedbackRequest, String actor) {
        FeedbackRequestFoxtrotEvent event = FeedbackRequestFoxtrotEvent.builder()
                .feedbackId(feedbackRequest.getFeedbackId())
                .feedbackAssetId(feedbackRequest.getFeedbackAssetId())
                .campaignId(feedbackRequest.getCampaignId())
                .formType(feedbackRequest.getFormType())
                .userId(actor)
                .build();
        return Event.<FeedbackRequestFoxtrotEvent>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(FEEDBACK_CREATION_OR_UPDATION_EVENT.name())
                .id(UUID.randomUUID().toString())
                .eventData(event)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(feedbackRequest.getFeedbackAssetId())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<FeedbackRequestFailureEvent> toFeedbackCreateOrUpdateFailureFoxtrotEvent(
            FeedbackRequest feedbackRequest, Exception e) {
        FeedbackRequestFailureEvent event = FeedbackRequestFailureEvent.builder()
                .feedbackRequest(feedbackRequest)
                .errorMessage(e.getMessage())
                .build();
        return toEvent(event, FEEDBACK_CREATION_OR_UPDATION_FAILURE_EVENT);
    }

    public static Event<FormConfigRequest> toFormConfigCreateOrUpdateFoxtrotEvent(FormConfigRequest formConfigRequest) {
        return Event.<FormConfigRequest>builder()
                .app(EventConstants.EVENT_APP_NAME)
                .eventType(FORM_CONFIG_CREATION_OR_UPDATION_EVENT.name())
                .id(UUID.randomUUID().toString())
                .eventData(formConfigRequest)
                .eventSchemaVersion(EventConstants.EVENT_SCHEMA_VERSION)
                .groupingKey(formConfigRequest.getFormType())
                .time(new Date(System.currentTimeMillis()))
                .build();
    }

    public static Event<FormConfigRequestFailureEvent> toFormConfigCreateOrUpdateFailureFoxtrotEvent(
            FormConfigRequest formConfigRequest, Exception e) {
        FormConfigRequestFailureEvent event = FormConfigRequestFailureEvent.builder()
                .formConfigRequest(formConfigRequest)
                .errorMessage(e.getMessage())
                .build();
        return toEvent(event, FORM_CONFIG_CREATION_OR_UPDATION_FAILURE_EVENT);
    }

    public static Event<BrickbatFormsRequestV2FailureEvent> toGetBrickbatFormAndFeedbackFailureFoxtrotEvent(
            BrickbatFormsRequestV2 requestV2, Exception e) {
        BrickbatFormsRequestV2FailureEvent event = BrickbatFormsRequestV2FailureEvent.builder()
                .request(requestV2)
                .errorMessage(e.getMessage())
                .build();
        return toEvent(event, GET_BRICKBAT_FORM_AND_FEEDBACK_FAILURE_EVENT);
    }

    public static Event<BrickbatFlatFeedbackRequestFailureEvent> toGetFlatMerchantStoreFeedbackFailureFoxtrotEvent(
            BrickbatFlatFeedbackRequest request, Exception e) {
        BrickbatFlatFeedbackRequestFailureEvent event = BrickbatFlatFeedbackRequestFailureEvent.builder()
                .request(request)
                .errorMessage(e.getMessage())
                .build();
        return toEvent(event, GET_FLAT_MERCHANT_STORE_FEEDBACK_FAILURE_EVENT);
    }

    public static Event<BrickbatSubmitFeedbackRequestFailureEvent> toCreateFeedbackAndAuditFailureFoxtrotEvent(
            BrickbatSubmitFeedbackRequest request, Exception e) {
        BrickbatSubmitFeedbackRequestFailureEvent event = BrickbatSubmitFeedbackRequestFailureEvent.builder()
                .request(request)
                .errorMessage(e.getMessage())
                .build();
        return toEvent(event, CREATE_FEEDBACK_AND_AUDIT_FAILURE_EVENT);
    }
}
