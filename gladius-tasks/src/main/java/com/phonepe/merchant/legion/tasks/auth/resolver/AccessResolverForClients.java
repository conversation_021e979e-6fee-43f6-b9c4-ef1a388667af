package com.phonepe.merchant.legion.tasks.auth.resolver;

import com.google.api.client.util.IOUtils;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.request.DefinitionIdIdentifier;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsByTypeCache;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.resolver.OverrideAccessResolver;
import com.phonepe.olympus.im.models.authn.UserType;
import com.phonepe.olympus.im.models.authz.Operator;
import com.phonepe.olympus.im.models.user.SystemUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import edu.emory.mathcs.backport.java.util.Collections;
import io.appform.functionmetrics.MonitoredFunction;
import io.dropwizard.util.Strings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.ws.rs.container.ContainerRequestContext;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.DEFAULT;

@Slf4j
@Singleton
public abstract class AccessResolverForClients implements OverrideAccessResolver {

    private final Supplier<OlympusIMClient> olympusIMClientSupplier;
    private final TaskDefinitionCache taskDefinitionCache;
    private final TaskInstanceManagementService taskInstanceManagementService;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;
    private final TaskDefinitionIdsByTypeCache taskDefinitionIdsByTypeCache;


    protected AccessResolverForClients(final Supplier<OlympusIMClient> olympusIMClientSupplier,
                                       final TaskDefinitionCache taskDefinitionCache,
                                       final TaskInstanceManagementServiceImpl taskInstanceManagementService,
                                       final FoxtrotEventIngestionService foxtrotEventIngestionService,
                                       final TaskDefinitionIdsByTypeCache taskDefinitionIdsByTypeCache) {
        this.olympusIMClientSupplier = olympusIMClientSupplier;
        this.taskDefinitionCache = taskDefinitionCache;
        this.taskInstanceManagementService = taskInstanceManagementService;
        this.foxtrotEventIngestionService = foxtrotEventIngestionService;
        this.taskDefinitionIdsByTypeCache = taskDefinitionIdsByTypeCache;
    }

    @SneakyThrows
    @Override
    @MonitoredFunction
    public Optional<Boolean> isAuthorized(ContainerRequestContext containerRequestContext, @NotNull AccessAllowed accessAllowed) {
        UserAuthDetails userAuthDetails = olympusIMClientSupplier.get().getUserAuthDetails(containerRequestContext);
        OlympusIMClient olympusIMClient = olympusIMClientSupplier.get();
        String[] permissions = accessAllowed.permissions();
        Operator permissionAuthorizationOperator = accessAllowed.permissionAuthorizationOperator();
        try {
            if (isClientToken(userAuthDetails)) {
                byte[] entity = copyStream(containerRequestContext);
                DefinitionIdIdentifier definition = SerDe.readValue(entity, getRequestClass());
                SystemUserDetails systemUserDetails = (SystemUserDetails) userAuthDetails.getUserDetails();
                List<String> definitionIds = definitionIdDerivative(Objects.requireNonNull(definition));
                log.debug("Got request to resolve access for {} {}", definitionIds, getRequestClass());
                Set<Set<String>> clients = definitionIds.stream().map(taskDefinitionId ->
                                taskDefinitionCache.get(taskDefinitionId).getAllClients())
                        .collect(Collectors.toSet());
                log.debug("Got request to resolve access for {} {} {}", definitionIds, getRequestClass(), clients);
                boolean allowedAccess = clients.isEmpty()
                        || clients.stream().allMatch(Set::isEmpty)
                        || clients.stream().allMatch(subSet -> subSet.contains(systemUserDetails.getComponentInstanceGroupId()));
                if (allowedAccess) {
                    return Optional.of(olympusIMClient.verifyPermissions(userAuthDetails, permissions, permissionAuthorizationOperator));
                } else {
                    return Optional.of(Boolean.FALSE);
                }
            }
            return Optional.of(olympusIMClient.verifyPermissions(userAuthDetails, permissions, permissionAuthorizationOperator));
        } catch (LegionException legionException) {
            log.error("Error encountered", legionException);
            foxtrotEventIngestionService.ingestTaskAccessResolverFailureEvent(userAuthDetails.getClientId(), legionException);
            throw legionException;
        } catch (Exception e) {
            log.error("Unable to resolve the request", e);
            foxtrotEventIngestionService.ingestTaskAccessResolverFailureEvent(userAuthDetails.getClientId(), e);
            throw LegionException.error(CoreErrorCode.INVALID_REQUEST, Map.of("message",
                    "Access Resolution failed due to invalid ContainerRequestContext"));
        }
    }

    private List<String> definitionIdDerivative(DefinitionIdIdentifier definition) {
        if (null == definition.getDefinitionId() && null == definition.getInstanceId()) {
            List<TaskDefinitionInstance> taskDefinitionInstances = taskDefinitionIdsByTypeCache.get(DEFAULT).get(definition.getType());
            if (taskDefinitionInstances == null || taskDefinitionInstances.isEmpty()) {
               return Collections.emptyList();

            }
            return taskDefinitionInstances.stream()
                    .map(TaskDefinitionInstance::getTaskDefinitionId).toList();
        }

        if (!Strings.isNullOrEmpty(definition.getInstanceId())) {
            return List.of(taskInstanceManagementService.getById(definition.getInstanceId()).getTaskDefinitionId());
        }

        if (!Strings.isNullOrEmpty(definition.getDefinitionId())) {
            return List.of(definition.getDefinitionId());
        }
        throw new IllegalArgumentException("Invalid Request one or more field is missing");
    }

    private boolean isClientToken(UserAuthDetails userAuthDetails) {
        return Objects.nonNull(userAuthDetails) && Objects.nonNull(userAuthDetails.getUserDetails())
                && UserType.SYSTEM == (userAuthDetails.getUserDetails().getUserType());
    }

    private byte[] copyStream(ContainerRequestContext containerRequestContext) throws IOException {
        if (containerRequestContext.hasEntity()) {
            try (InputStream in = containerRequestContext.getEntityStream()) {
                ByteArrayOutputStream out = new ByteArrayOutputStream(64 * 1024);
                IOUtils.copy(in, out);
                byte[] entity = out.toByteArray();
                //restore input
                containerRequestContext.setEntityStream(new ByteArrayInputStream(entity));
                return entity;
            }
        }
        throw LegionException.error(CoreErrorCode.NO_BODY_FOUND);
    }

    public abstract Class<? extends DefinitionIdIdentifier> getRequestClass();
}