package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.request.ActionDetails;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.CategoryDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.tasks.services.CategoryInsightService;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import killswitch.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/v1/user/console")
@Tag(name = "User Console Related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class UserConsoleResource {

    private final TaskActionService taskActionService;
    private final TaskDiscoveryService taskDiscoveryService;
    private final TaskDefinitionService taskDefinitionService;
    private final TaskAttributeService taskAttributeService;
    private final CategoryInsightService categoryInsightService;



    @GET
    @Timed
    @Authorize(value = "getAllActions")
    @RolesAllowed(value = "getAllActions")
    @Operation(summary = "Get Task Actions",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/actions")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<List<TaskActionInstance>> getAllActions(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal){
        return GenericResponse.<List<TaskActionInstance>>builder()
                .success(true)
                .data(taskActionService.getAllActions())
                .build();
    }

    @GET
    @Path("/definitions/{actionId}")
    @Timed
    @Authorize(value = "getDefinitionsByActionId")
    @RolesAllowed(value = "getDefinitionsByActionId")
    @Operation(summary = "Fetch Task Definitions for an Action")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<List<TaskDefinitionInstance>> fetchByActionId(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                                          @PathParam("actionId") @NotNull final String actionId) {
        List<TaskDefinitionInstance> response = taskDefinitionService.fetchByActionId(actionId);
        return GenericResponse.<List<TaskDefinitionInstance>>builder()
                .success(response != null)
                .data(response)
                .build();
    }

    @POST
    @Timed
    @Authorize(value = "getScpTaskListing")
    @RolesAllowed(value = "getScpTaskListing")
    @Operation(summary = "Get Task Listing",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/get/listing")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskListResponse> getTaskListing(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid @NotNull TaskListRequest taskListRequest) {
        return GenericResponse.<TaskListResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getTaskListing(taskListRequest))
                .build();
    }

    @GET
    @Timed
    @Authorize(value = "getAllAttributes")
    @RolesAllowed(value = "getAllAttributes")
    @Operation(summary = "Get All Active Attributes",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/attributes")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<List<TaskAttributeInstance>> getAllAttributes(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal){
        return GenericResponse.<List<TaskAttributeInstance>>builder()
                .success(true)
                .data(taskAttributeService.getAllAttributes())
                .build();
    }

    @GET
    @Path("/fetch/category/details")
    @Timed
    @Operation(summary = "Get all categories")
    @ExceptionMetered
    @LegionGateKeeper
    @RolesAllowed(value = "fetch_category_details") // check with sid
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<CategoryDetails> fetchCategoryDetails(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                 @Parameter(hidden = true) @RequestContext RequestInfo requestInfo) {
        CategoryDetails response = categoryInsightService.fetchCategoryDetails();
        return GenericResponse.<CategoryDetails>builder()
                .success(response != null)
                .data(response)
                .build();

    }

    @GET
    @Path("/actions/category/{category}")
    @Timed
    @Operation(summary = "Get all actions for a given category")
    @RolesAllowed(value = "fetch_actions_for_given_category")
    @ExceptionMetered
    @LegionGateKeeper
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<List<ActionDetails>> fetchActionDetails(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                   @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                                   @PathParam("category") @NotNull final Category category) {

        List<ActionDetails> response = categoryInsightService.fetchAllActionsOfCategory(category);
        return GenericResponse.<List<ActionDetails>>builder()
                .success(response != null)
                .data(response)
                .build();

    }
}
