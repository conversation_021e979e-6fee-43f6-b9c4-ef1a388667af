package com.phonepe.merchant.legion.tasks.search.response.filter;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.legion.tasks.bindings.filter.views.AssignedViewWiseFiltersProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.views.DiscoveryViewWiseFiltersProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.views.LeadViewWiseFiltersProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
@Singleton
public class RequestViewWiseFilterFactory {

    private final RequestViewWiseFilter generateAssignedViewWiseFilters;
    private final RequestViewWiseFilter generateDiscoveryViewWiseFilters;
    private final RequestViewWiseFilter generateLeadViewWiseFilters;

    @Inject
    public RequestViewWiseFilterFactory(
            @AssignedViewWiseFiltersProvider
            RequestViewWiseFilter generateAssignedViewWiseFilters,
            @DiscoveryViewWiseFiltersProvider
            RequestViewWiseFilter generateDiscoveryViewWiseFilters,
            @LeadViewWiseFiltersProvider
            RequestViewWiseFilter generateLeadViewWiseFilters) {
        this.generateAssignedViewWiseFilters = generateAssignedViewWiseFilters;
        this.generateDiscoveryViewWiseFilters = generateDiscoveryViewWiseFilters;
        this.generateLeadViewWiseFilters = generateLeadViewWiseFilters;

    }

    public ViewWiseFilters generateViewWiseFilter(TaskFilters taskFilters, Map<String, List<FilterOptions>> filterOptions,
                                                  TaskSearchRequestType type) {
        return type.accept(new TaskSearchRequestType.TaskSearchRequestTypeVisitor<>() {
            @Override
            public ViewWiseFilters visitAssignedRequest(TaskSearchRequestType payload) {
                return generateAssignedViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFilters visitDiscoveryRequest(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFilters visitSectorAssignedRequest(TaskSearchRequestType payload) {
                return generateAssignedViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFilters visitSectorDiscoveryRequest(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFilters viewEscalatedView(TaskSearchRequestType payload) {
                return null;
            }

            @Override
            public ViewWiseFilters viewHotspotAssignedView(TaskSearchRequestType payload) {
                return generateAssignedViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFilters viewHotspotDiscoveryView(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFilters visitLeadRequest(TaskSearchRequestType payload) {
                return generateLeadViewWiseFilters.generateViewWiseFilters(taskFilters, filterOptions, type);
            }
        }, type);
    }

    public ViewWiseFiltersV2 generateViewWiseFilterV2(TaskFiltersV2 taskFilters, Map<String, List<FilterOptionsV2>> filterOptions,
                                                      TaskSearchRequestType type) {
        return type.accept(new TaskSearchRequestType.TaskSearchRequestTypeVisitor<>() {
            @Override
            public ViewWiseFiltersV2 visitAssignedRequest(TaskSearchRequestType payload) {
                return generateAssignedViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 visitDiscoveryRequest(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 visitSectorAssignedRequest(TaskSearchRequestType payload) {
                return generateAssignedViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 visitSectorDiscoveryRequest(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 viewEscalatedView(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 viewHotspotAssignedView(TaskSearchRequestType payload) {
                return generateAssignedViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 viewHotspotDiscoveryView(TaskSearchRequestType payload) {
                return generateDiscoveryViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }

            @Override
            public ViewWiseFiltersV2 visitLeadRequest(TaskSearchRequestType payload) {
                return generateLeadViewWiseFilters.generateViewWiseFiltersV2(taskFilters, filterOptions, type);
            }
        }, type);
    }

}
