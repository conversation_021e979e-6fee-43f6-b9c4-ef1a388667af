package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.request.TasksInPolygonRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.geopolygon.GeoFenceRemappingSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.EsDocScrollResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;


public interface TaskHousekeepingService {
    void syncDueDateInDb(String taskInstanceId);

    // To Be Deprecated
    boolean updateSectorAgainstSectorId(String sectorId, long createdAt);

    // To Be Deprecated
    boolean updateSectorForTask(String taskInstanceId);

    TaskListResponse getTaskInGeoFenceOfSector(TasksInPolygonRequest taskSearchRequest);

    void refreshTaskInstance(String taskInstanceId);

    EsDocScrollResponse getEsDocsWithinGeoFence(GeoFenceRemappingSearchRequest request);
}
