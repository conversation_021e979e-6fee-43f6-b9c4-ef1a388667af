package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CampaignFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.HotspotAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.HotspotDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.LeadViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.AssignedSectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.DiscoverySectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.services.CampaignService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.models.merchants.tasks.EntityType;
import com.utils.StringUtils;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ru.vyarus.dropwizard.guice.module.yaml.bind.Config;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.TASK_TYPES;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.WHITELISTED_DEFINITIONS;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.CAMPAIGN_EXPIRED;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_CLIENT_TASK_CREATION_REQUEST;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_ENTITY_ID;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_TYPE;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.TASK_NOT_IN_ACCESSIBLE_SECTOR;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ValidationServiceImpl implements ValidationService {
    private final CampaignService campaignService;
    private final EntityStore entityStore;
    private final LegionService legionService;
    private final AtlasService atlasService;
    private final TaskDefinitionService taskDefinitionService;
    private final TaskESRepository taskEsRepository;
    @Config(value = TASK_TYPES)
    private final List<String> taskTypes;
    @Config(value = WHITELISTED_DEFINITIONS)
    private final Set<String> whitelistedDefinitions;

    @Override
    public TaskDefinitionInstance validateAndGetTaskDefinition(String taskDefinitionId) {
        try {
            return taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder().taskDefinitionId(taskDefinitionId).build());
        } catch (Exception e) {
            throw LegionException.error(CoreErrorCode.TASK_DEFINITION_NOT_FOUND, Map.of("message", "Invalid definition Id"));
        }
    }

    @Override
    @MonitoredFunction
    public void validateEntity(String entityId, EntityType entityType) {
        Optional<Entity> entityStoreById = entityStore.getById(EntityStoreRequest.builder()
                .referenceId(entityId)
                .entityType(entityType)
                .build());
        if (entityStoreById.isEmpty()) {
            throw LegionException.error(INVALID_ENTITY_ID, Map.of(MESSAGE, "Entity Id is invalid"));
        }
    }

    @Override
    @MonitoredFunction
    public Campaign validateAndGetCampaign(String campaignId) {
        Campaign campaign = campaignService.get(CampaignFetchByIdRequest.builder()
                .campaignId(campaignId)
                .build());
        Date currDate = new Date();
        Date campaignEndDate = null;
        if (campaign.getExpiryPeriod() == ExpiryPeriod.TIMESTAMP) {
            campaignEndDate = new Date(campaign.getExpiryValue());
        }
        if (campaignEndDate == null)
            return campaign;
        if (currDate.compareTo(campaignEndDate) >= 0) {
            throw LegionException.error(CAMPAIGN_EXPIRED, Map.of(MESSAGE, "Campaign is expired"));
        }
        return campaign;
    }

    @Override
    public void validateBoundedAssignmentWithMissingAssignee(boolean markAvailable, String assigneeId) {
        if (!markAvailable && isNullOrEmpty(assigneeId)) {
            throw LegionException.error(INVALID_CLIENT_TASK_CREATION_REQUEST,
                    Map.of(MESSAGE, "Task cannot be assigned as agentId is null"));
        }
    }

    @Override
    @MonitoredFunction
    public String validateRequesterAndGetActor(String requesterId, TaskSearchRequest taskSearchRequest) {
        String actorId = taskSearchRequest.getTaskSearchRequestType().accept(new TaskSearchRequestType.TaskSearchRequestTypeVisitor<String, TaskSearchRequest>() {
            @Override
            public String visitAssignedRequest(TaskSearchRequest payload) {
                return ((AssignedViewTaskFetchRequest) payload).getAssignedTo();
            }

            @Override
            public String visitDiscoveryRequest(TaskSearchRequest payload) {
                return ((DiscoveryViewTaskSearchRequest) payload).getAgentId();
            }

            @Override
            public String visitSectorAssignedRequest(TaskSearchRequest payload) {
                return ((SectorAssignedViewTaskSearchRequest) payload).getAssignedTo();
            }

            @Override
            public String visitSectorDiscoveryRequest(TaskSearchRequest payload) {
                return ((SectorDiscoveryViewTaskSearchRequest) payload).getAgentId();
            }

            @Override
            public String viewEscalatedView(TaskSearchRequest payload) {
                return ((EscalatedViewTaskSearchRequest) payload).getTsmId();
            }

            @Override
            public String viewHotspotAssignedView(TaskSearchRequest payload) {
                return ((HotspotAssignedViewTaskSearchRequest) payload).getAssignedTo();
            }

            @Override
            public String viewHotspotDiscoveryView(TaskSearchRequest payload) {
                return ((HotspotDiscoveryViewTaskSearchRequest) payload).getAgentId();
            }

            @Override
            public String visitLeadRequest(TaskSearchRequest payload) {
                return ((LeadViewTaskSearchRequest) payload).getAssignedTo();
            }
        }, taskSearchRequest);

        validateRequesterInHierarchy(actorId, requesterId);
        return actorId;
    }

    @Override
    @MonitoredFunction
    public String validateRequesterAndGetActor(String requesterId, SectorMapViewTaskSearchRequest taskSearchRequest) {
        String actorId = taskSearchRequest.getTaskSearchRequestType().accept(new TaskViewRequestType.TaskViewRequestTypeVisitor<String, SectorMapViewTaskSearchRequest>() {

            @Override
            public String visitAssignedRequest(SectorMapViewTaskSearchRequest payload) {
                return ((AssignedSectorMapViewTaskSearchRequest) payload).getAssignedTo();
            }

            @Override
            public String visitDiscoveryRequest(SectorMapViewTaskSearchRequest payload) {
                return ((DiscoverySectorMapViewTaskSearchRequest) payload).getReporteeId();
            }

            @Override
            public String visitEntityRequest(SectorMapViewTaskSearchRequest payload) {
                return "";
            }

            @Override
            public String visitEntityHistorRequest(SectorMapViewTaskSearchRequest payload) {
                return "";
            }
        }, taskSearchRequest);

        validateRequesterInHierarchy(actorId, requesterId);
        return actorId;
    }

    @Override
    @MonitoredFunction
    public void validateRequesterInHierarchy(String userId, String requesterId) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(requesterId) || userId.equals(requesterId)) {
            return;
        }
        Set<String> userIds = new HashSet<>();
        AgentProfile agentProfile = legionService.getAgentProfile(userId);
        legionService.getAgentProfile(requesterId);
        while (agentProfile.getManagerId() != null && !userIds.contains(agentProfile.getManagerId())) {
            userIds.add(agentProfile.getManagerId());
            if (requesterId.equals(agentProfile.getManagerId())) {
                return;
            }
            agentProfile = legionService.getAgentProfile(agentProfile.getManagerId());
        }

        log.info("Throwing unauthorised error for user {} and requester {}", userId, requesterId);
        throw LegionException.error(LegionTaskErrorCode.UNAUTHORISED_DATA_REQUEST);
    }

    @Override
    public void validateAccessibleSectors(String taskInstanceId, String endUserToken) {
        DiscoveryTaskInstance discoveryTaskInstance = taskEsRepository.get(taskInstanceId);
        if (Objects.isNull(discoveryTaskInstance)) {
            throw LegionException.error(INVALID_TASK_INSTANCE_ID);
        }
        List<String> polygonIds = atlasService.getSectorIds(discoveryTaskInstance.getLocation());
        List<String> accessibleSectors = legionService.getAllAccessibleSectors(org.apache.commons.lang3.StringUtils.EMPTY, endUserToken);
        if (polygonIds.stream().noneMatch(accessibleSectors::contains)) {
            throw LegionException.error(TASK_NOT_IN_ACCESSIBLE_SECTOR);
        }
    }

    @Override
    public void validateTaskType(String taskType) {
        if (!taskTypes.contains(taskType))
            throw LegionException.error(INVALID_TASK_TYPE);

    }

    @Override
    public boolean checkIfDefinitionIsWhitelisted(String definitionId) {
        return whitelistedDefinitions.isEmpty() || whitelistedDefinitions.contains(definitionId);
    }
}
