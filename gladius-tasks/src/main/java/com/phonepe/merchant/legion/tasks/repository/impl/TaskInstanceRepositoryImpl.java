package com.phonepe.merchant.legion.tasks.repository.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.commands.AuditAccessCommands;
import com.phonepe.merchant.legion.core.commands.LookupAccessCommands;
import com.phonepe.merchant.legion.core.daos.AuditDao;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.utils.IdUtils;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.functionmetrics.MonitoredFunction;
import org.hibernate.envers.query.AuditEntity;

import java.util.List;
import java.util.Optional;
import java.util.function.UnaryOperator;

import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_INSTANCE_ID_PREFIX;

@Singleton
public class TaskInstanceRepositoryImpl implements TaskInstanceRepository {

  private final LookupAccessCommands<StoredTaskInstance> accessor;
  private final AuditAccessCommands<StoredTaskInstance> auditAccessor;
  private final IdUtils idUtils;
  private final LookupDao<StoredTaskInstance> lookupDao;

  @Inject
  public TaskInstanceRepositoryImpl(LookupDao<StoredTaskInstance> lookupDao,
                                    AuditDao<StoredTaskInstance> auditDao,
                                    FoxtrotEventIngestionService eventIngestionService,
                                    IdUtils idUtils) {
    this.accessor = new LookupAccessCommands<>(lookupDao, eventIngestionService);
    this.auditAccessor = new AuditAccessCommands<>(auditDao, eventIngestionService);
    this.lookupDao = lookupDao;
    this.idUtils = idUtils;
  }

  @Override
  @MonitoredFunction
  public String generateTaskInstanceId(String entityId){
    return idUtils.createTaskInstanceId(TASK_INSTANCE_ID_PREFIX, entityId, lookupDao);
  }

  @Override
  @MonitoredFunction
  public StoredTaskInstance save(StoredTaskInstance storedAgentTaskInstance) {
    Optional<StoredTaskInstance> savedTaskInstance = accessor.save(storedAgentTaskInstance);
    if (savedTaskInstance.isEmpty()) {
      throw LegionException.error(CoreErrorCode.DAO_ERROR);
    }
    return savedTaskInstance.get();
  }

  @Override
  @MonitoredFunction
  public Optional<StoredTaskInstance> get(String taskInstanceId) {
    return accessor.get(taskInstanceId);
  }

  @Override
  public List<Audit<StoredTaskInstance>> audit(String taskInstanceId) {
    return auditAccessor.get(taskInstanceId, List.of(AuditEntity.property(TASK_INSTANCE_ID).eq(taskInstanceId)));
  }

  @Override
  @MonitoredFunction
  public boolean update(String taskInstanceId, UnaryOperator<StoredTaskInstance> mutator) {
    return accessor.update(taskInstanceId, mutator);
  }

}
