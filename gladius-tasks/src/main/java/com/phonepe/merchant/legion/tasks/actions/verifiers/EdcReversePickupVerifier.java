package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.ParadoxService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.paradox.models.workflow.registration.responses.TaskStatus;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.GenericUtil.getMidAndSidFromEid;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_REVERSE_PICKUP_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;

@Slf4j
@ActionVerifierMarker(name = EDC_REVERSE_PICKUP_VERIFIER)
public class EdcReversePickupVerifier extends ActionVerifier {

    private final ParadoxService paradoxService;
    private static final String EDC_REVERSE_PICKUP = "EDC_REVERSE_PICKUP";

    @Inject
    public EdcReversePickupVerifier(ParadoxService paradoxService) {
        this.paradoxService = paradoxService;
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        String taskInstanceId = taskCompleteRequest.getTaskInstanceId();
        String merchantId = getMidAndSidFromEid(taskCompleteRequest.getStoredTaskInstance().getEntityId())[0];
        TaskStatus taskStatus = paradoxService.getParadoxTaskStatus(merchantId, taskInstanceId, EDC_REVERSE_PICKUP);

        Boolean verified = null;
        if(TaskStatus.COMPLETED == taskStatus) {
            verified = true;
        }
        return VerifierResponse.builder()
                .verified(verified)
                .context(context)
                .build();
    }

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.STORE) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }

}
