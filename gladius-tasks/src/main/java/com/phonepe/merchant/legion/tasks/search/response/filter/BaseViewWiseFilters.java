package com.phonepe.merchant.legion.tasks.search.response.filter;

import com.collections.CollectionUtils;
import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.Operator;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFilterOptionsV2;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.OBJECTIVES_KEYWORD;

public abstract class BaseViewWiseFilters implements RequestViewWiseFilter {
    static final String POINTS_DISPLAY_TEXT = "Points";
    static final String TASK_TYPE = "Task Type";
    static final String LEAD_KEY = "lead_status";
    static final String LEAD_DISPLAY_TEXT = "Lead Status";

    protected List<TaskFilterOptions> getFilterOptions(Map<String, List<FilterOptions>> filterOptions,
                                                       List<TaskFilterOptions> filters) {
        List<TaskFilterOptions> viewWiseFilters = new ArrayList<>(filters);

        if (filterOptions.containsKey(OBJECTIVES)) {
            viewWiseFilters.add(TaskFilterOptions.builder()
                    .key(OBJECTIVES_KEYWORD).displayText(OBJECTIVE).multipleSelect(true)
                    .options(filterOptions.get(OBJECTIVES)).build());
        }
        if (filterOptions.containsKey(POINTS)) {
            viewWiseFilters.add(TaskFilterOptions.builder()
                    .key(POINTS).displayText(POINTS_DISPLAY_TEXT).multipleSelect(true)
                    .options(filterOptions.get(POINTS)).build());
        }
        if (filterOptions.containsKey(ACTION_ID)) {
            viewWiseFilters.add(TaskFilterOptions.builder()
                    .key(ACTION_ID).displayText(TASK_TYPE).multipleSelect(true)
                    .options(filterOptions.get(ACTION_ID)).build());
        }

        return viewWiseFilters.stream().filter(taskFilterOptions ->
                CollectionUtils.isNotEmpty(taskFilterOptions.getOptions())
        ).toList();
    }

    protected List<TaskFilterOptionsV2> getFilterOptionsV2(Map<String, List<FilterOptionsV2>> filterOptions,
                                                       List<TaskFilterOptionsV2> filters) {
        List<TaskFilterOptionsV2> viewWiseFilters = new ArrayList<>(filters);

        if (filterOptions.containsKey(OBJECTIVES)) {
            viewWiseFilters.add(TaskFilterOptionsV2.builder()
                    .key(OBJECTIVES)
                    .groupingOperator(Operator.OR)
                    .displayText(OBJECTIVE).multipleSelect(true)
                    .options(filterOptions.get(OBJECTIVES)).build());
        }
        if (filterOptions.containsKey(POINTS)) {
            viewWiseFilters.add(TaskFilterOptionsV2.builder()
                    .key(POINTS)
                    .groupingOperator(Operator.OR)
                    .displayText(POINTS_DISPLAY_TEXT).multipleSelect(true)
                    .options(filterOptions.get(POINTS)).build());
        }
        if (filterOptions.containsKey(ACTION_ID)) {
            viewWiseFilters.add(TaskFilterOptionsV2.builder()
                    .key(ACTION_ID)
                    .groupingOperator(Operator.OR)
                    .displayText(TASK_TYPE).multipleSelect(true)
                    .options(filterOptions.get(ACTION_ID)).build());
        }

        if (filterOptions.containsKey(LEAD_INTENT)) {
            viewWiseFilters.add(TaskFilterOptionsV2.builder()
                    .key(LEAD_KEY)
                    .groupingOperator(Operator.OR)
                    .displayText(LEAD_DISPLAY_TEXT).multipleSelect(true)
                    .options(filterOptions.get(LEAD_INTENT)).build());
        }

        return viewWiseFilters.stream().filter(taskFilterOptions ->
                CollectionUtils.isNotEmpty(taskFilterOptions.getOptions())
        ).toList();
    }

}
