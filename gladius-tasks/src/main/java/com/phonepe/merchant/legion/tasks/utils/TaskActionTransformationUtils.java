package com.phonepe.merchant.legion.tasks.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredActionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationStrategy;
import com.phonepe.merchant.gladius.models.tasks.verification.VerificationStrategy;
import com.phonepe.merchant.legion.core.utils.SerDe;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TaskActionTransformationUtils {
    private TaskActionTransformationUtils() {
    }

    public static StoredTaskAction toStoredAction(TaskActionInstance instance) {
        StoredTaskAction storedTaskAction = StoredTaskAction.builder()
                .actionId(instance.getActionId())
                .entityType(instance.getEntityType())
                .namespace(instance.getNamespace())
                .createdBy(instance.getCreatedBy())
                .updatedBy(instance.getCreatedBy())
                .subSteps(SerDe.writeValueAsBytes(instance.getSubSteps()))
                .verificationStrategy(SerDe.writeValueAsBytes(instance.getVerificationStrategy()))
                .description(instance.getDescription())
                .build();
        if (instance.getCompletionValidationStrategy() != null) {
            storedTaskAction.setCompletionValidationStrategy(SerDe.writeValueAsBytes(instance.getCompletionValidationStrategy()));
        }
        if (instance.getStartTaskValidationStrategy() != null) {
            storedTaskAction.setStartTaskValidationStrategy(SerDe.writeValueAsBytes(instance.getStartTaskValidationStrategy()));
        }
        if (instance.getAssignTaskValidationStrategy() != null) {
            storedTaskAction.setAssignTaskValidationStrategy(SerDe.writeValueAsBytes(instance.getAssignTaskValidationStrategy()));
        }

        List<StoredActionAttributeMappings> attributes = new ArrayList<>();
        if (null != instance.getAttributes()) {
            attributes = TaskUtils.getAttributes(instance, storedTaskAction);
        }
        storedTaskAction.setMetaData(instance.getMetaData());

        storedTaskAction.setAttributes(attributes);
        return storedTaskAction;
    }

    public static TaskActionInstance toTaskActionInstance(StoredTaskAction storedTaskAction){
        return toTaskActionInstance(storedTaskAction, null);
    }

    public static TaskActionInstance toTaskActionInstance(StoredTaskAction storedTaskAction,
                                                          Map<String, Set<String>> attributes) {
        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .actionId(storedTaskAction.getActionId())
                .entityType(storedTaskAction.getEntityType())
                .namespace(storedTaskAction.getNamespace())
                .createdBy(storedTaskAction.getCreatedBy())
                .updatedBy(storedTaskAction.getUpdatedBy())
                .description(storedTaskAction.getDescription())
                .subSteps(SerDe.readValue(storedTaskAction.getSubSteps(),
                        new TypeReference<List<Object>>() {}))
                .verificationStrategy(SerDe.readValue(storedTaskAction.getVerificationStrategy(),
                        new TypeReference<VerificationStrategy>() {}))
                .attributes(attributes)
                .build();
        if (storedTaskAction.getCompletionValidationStrategy() != null && storedTaskAction.getCompletionValidationStrategy().length > 0) {
            taskActionInstance.setCompletionValidationStrategy(SerDe.readValue(storedTaskAction.getCompletionValidationStrategy(),
                    new TypeReference<ValidationStrategy>() {
                    }));
        }

        if (storedTaskAction.getStartTaskValidationStrategy() != null && storedTaskAction.getStartTaskValidationStrategy().length > 0) {
            taskActionInstance.setStartTaskValidationStrategy(SerDe.readValue(storedTaskAction.getStartTaskValidationStrategy(),
                    new TypeReference<ValidationStrategy>() {
                    }));
        }
        if (storedTaskAction.getAssignTaskValidationStrategy() != null && storedTaskAction.getAssignTaskValidationStrategy().length > 0) {
            taskActionInstance.setAssignTaskValidationStrategy(SerDe.readValue(storedTaskAction.getAssignTaskValidationStrategy(),
                    new TypeReference<ValidationStrategy>() {
                    }));
        }
        if (storedTaskAction.getMetaData() != null) {
            taskActionInstance.setMetaData(storedTaskAction.getMetaData());
        }
        return taskActionInstance;
    }

    public static StoredTaskAction update(StoredTaskAction storedTaskAction, TaskActionInstance request) {
        storedTaskAction.setSubSteps(SerDe.writeValueAsBytes(request.getSubSteps()));
        storedTaskAction.setDescription(request.getDescription());
        storedTaskAction.setVerificationStrategy(SerDe.writeValueAsBytes(request.getVerificationStrategy()));
        storedTaskAction.setUpdatedBy(request.getUpdatedBy());

        if (request.getCompletionValidationStrategy() != null) {
            storedTaskAction.setCompletionValidationStrategy(SerDe.writeValueAsBytes(request.getCompletionValidationStrategy()));
        }

        if (request.getStartTaskValidationStrategy() != null) {
            storedTaskAction.setStartTaskValidationStrategy(SerDe.writeValueAsBytes(request.getStartTaskValidationStrategy()));
        }

        if(request.getAssignTaskValidationStrategy() != null) {
            storedTaskAction.setAssignTaskValidationStrategy(SerDe.writeValueAsBytes(request.getAssignTaskValidationStrategy()));
        }

        if(request.getAttributes() != null) {
            List<StoredActionAttributeMappings> mergedAttributes = TaskUtils.mergeActionAttributes(
                    storedTaskAction.getAttributes(), TaskUtils.getAttributes(request, storedTaskAction));
            storedTaskAction.setAttributes(mergedAttributes);
        }

        storedTaskAction.setMetaData(request.getMetaData());
        return storedTaskAction;
    }

}
