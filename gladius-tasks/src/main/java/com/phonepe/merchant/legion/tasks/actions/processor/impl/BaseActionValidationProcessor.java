package com.phonepe.merchant.legion.tasks.actions.processor.impl;

import com.google.common.collect.Maps;
import com.google.inject.Injector;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationStrategy;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.ErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionValidatorMarker;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.models.merchants.tasks.EntityType;
import org.reflections.Reflections;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.TASK_VALIDATION_FAILURE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;

public abstract class BaseActionValidationProcessor<T extends LocationCommandRequest>
        implements ActionValidationProcessor<T> {

    protected static final String VALIDATION_HANDLER_PACKAGE = "com.phonepe.merchant.legion.tasks.actions";
    protected Map<String, ActionValidator> validators = Maps.newConcurrentMap();

    protected final TaskActionService taskActionService;
    protected final FoxtrotEventIngestionService foxtrotEventIngestionService;

    public BaseActionValidationProcessor(Injector injector,
                                         TaskActionService taskActionService,
                                         FoxtrotEventIngestionService foxtrotEventIngestionService) {
        this.taskActionService = taskActionService;
        this.foxtrotEventIngestionService = foxtrotEventIngestionService;

        Reflections reflections = new Reflections(VALIDATION_HANDLER_PACKAGE);
        final Set<Class<?>> annotatedClasses = reflections
                .getTypesAnnotatedWith(ActionValidatorMarker.class);

        annotatedClasses.forEach(annotatedType -> {
            if (ActionValidator.class.isAssignableFrom(annotatedType)) {
                ActionValidatorMarker annotation = annotatedType.getAnnotation(ActionValidatorMarker.class);
                final ActionValidator instance = ActionValidator.class
                        .cast(injector.getInstance(annotatedType));
                validators.put(annotation.name(), instance);
            }
        });
    }

    public BaseActionValidationProcessor(Map<String, ActionValidator> validators,
                                         TaskActionService taskActionService,
                                         FoxtrotEventIngestionService foxtrotEventIngestionService) {
        this.validators = validators;
        this.taskActionService = taskActionService;
        this.foxtrotEventIngestionService = foxtrotEventIngestionService;
    }

    @Override
    public void validate(LocationCommandRequest taskCommandRequest) {
        TaskActionInstance taskActionInstance = taskActionService.getFromCache(
                TaskActionFetchByIdRequest.builder()
                        .taskActionId(taskCommandRequest.getStoredTaskInstance().getActionId())
                        .build());
        Set<ValidatorResponse> errorContext = new HashSet<>();

        ValidationContainer validationContainer = getTaskValidationContainer(taskActionInstance);

        if (validationContainer != null) {
            if (validationContainer.validate(taskCommandRequest, validators, errorContext)) {
                return;
            }
            foxtrotEventIngestionService.ingestTaskPrerequisiteFailedEvent(
                    taskCommandRequest,
                    errorContext);
            if (errorContext.isEmpty()) {
                throw LegionException.error(TASK_VALIDATION_FAILURE);
            }
            ValidatorResponse validatorResponse = errorContext.iterator().next();
            if (validatorResponse.getErrorMessage() == null)
                throw LegionException.error((ErrorCode) validatorResponse.getErrorCode());
            else
                throw LegionException.error((ErrorCode) validatorResponse.getErrorCode(),
                        Map.of(MESSAGE, validatorResponse.getErrorMessage()));
        }
    }

    public abstract ValidationContainer getTaskValidationContainer(TaskActionInstance taskActionInstance);

    public void validate(EntityType entityType, ValidationStrategy validationStrategy) {
        if (validationStrategy == null || validationStrategy.getValidationConfig() == null) {
            return;
        }
        validationStrategy.getValidationConfig().validate(entityType, validators);
    }

}
