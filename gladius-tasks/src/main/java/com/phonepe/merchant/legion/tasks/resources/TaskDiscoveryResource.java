package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.request.AgentTaskEligibilityRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDetailRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TaskStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TasksStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.AgentTaskEligibilityResponse;
import com.phonepe.merchant.gladius.models.tasks.response.EntityStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDetailResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskStateStatsByAction;
import com.phonepe.merchant.gladius.models.tasks.response.UserStatsAndFilters;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import killswitch.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/v1/task")
@Tag(name = "Task Discovery APIs")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TaskDiscoveryResource {

    private final TaskDiscoveryService taskDiscoveryService;
    private final ValidationService validationService;

    @POST
    @Timed
    @Authorize(value = "userTaskSearch")
    @RolesAllowed(value = "userTaskSearch")
    @Operation(summary = "Search tasks",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/search")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
        public GenericResponse<TaskSearchResponse> searchTasks(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                           @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                           @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                           @Valid @NotNull TaskSearchRequest taskSearchRequest,
                                                           @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {

        String requesterId = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        String actor = StringUtils.firstNonEmpty(validationService.validateRequesterAndGetActor(requesterId, taskSearchRequest)
                , requesterId);
        return GenericResponse.<TaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.search(actor, taskSearchRequest))
                .build();
    }

    @POST
    @Timed
    @ExceptionMetered
    @Authorize(value = "escalationSearch")
    @RolesAllowed(value = "escalationSearch")
    @Operation(summary = "Escalation task Search",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/escalationTaskSearch")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
    public GenericResponse<TaskSearchResponse> escalationSearchTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,@Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                                    @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                    @Valid @NotNull EscalatedViewTaskSearchRequest escalatedViewTaskSearchRequest,
                                                                    @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile,
                                                                    @HeaderParam(AUTHORIZATION) String auth) {

        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        escalatedViewTaskSearchRequest.setEndUserToken(auth);
        escalatedViewTaskSearchRequest.setTsmId(actor);
        escalatedViewTaskSearchRequest.setSorter(Sorter.CREATED_AT);
        validationService.validateRequesterAndGetActor(actor, escalatedViewTaskSearchRequest);
        return GenericResponse.<TaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.search(actor, escalatedViewTaskSearchRequest))
                .build();
    }

    @POST
    @Timed
    @Authorize(value = "userTaskSearch")
    @RolesAllowed(value = "userTaskSearch")
    @Operation(summary = "Fetch list of task in a sector for map view",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/list")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
        public GenericResponse<SectorTaskSearchResponse> getTasksForMapView(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                                        @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                                        @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                        @Valid @NotNull SectorMapViewTaskSearchRequest taskSearchRequest,
                                                                        @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String userId = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        String actor = StringUtils.firstNonEmpty(validationService.validateRequesterAndGetActor(userId, taskSearchRequest), userId);
        return GenericResponse.<SectorTaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getTaskList(actor, taskSearchRequest))
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Get list of tasks for a particular entity",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "userTaskListing")
    @RolesAllowed(value = "userTaskListing")
    @Path("/listing")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
        public GenericResponse<TaskSearchResponse> getTaskListingForAgent(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                                      @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                      @Valid TaskListingRequest request,
                                                                      @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        return GenericResponse.<TaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getAgentTaskListing(actor, request))
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Get a list of all tasks associated with an entity ID ",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "userTaskListing")
    @RolesAllowed(value = "userTaskListing")
    @Path("/listAllActiveTasksForEntity")
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<TaskSearchResponse> getTaskListForEntity(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid EntityTaskListingRequest request) {

        return GenericResponse.<TaskSearchResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getAllEntityActiveTasks(request))
                .build();
    }

    @GET
    @Timed
    @Operation(summary = "Get Entity Stats",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "userTaskListing")
    @RolesAllowed(value = "userTaskListing")
    @Path("/stats/{entityId}")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
        public GenericResponse<EntityStatsResponse> getEntityStats(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @PathParam("entityId") String entityId,
                                                               @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                               @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                               @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        return GenericResponse.<EntityStatsResponse>builder()
                .success(true)
                .data(taskDiscoveryService.getEntityStats(actor, entityId))
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Get tasks state stats",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @RolesAllowed(value = "taskStateStats")
    @ExceptionMetered
    @Path("/state/stats")
    @ApiKillerMeta(tags = {OperationType.READ})
        public GenericResponse<List<TaskStateStatsByAction>> getTasksStateStats(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                            @Valid TasksStatsRequest tasksStatsRequest) {
        return GenericResponse.<List<TaskStateStatsByAction>>builder()
                .success(true)
                .data(taskDiscoveryService.getTasksStateStatsByAction(tasksStatsRequest))
                .build();
    }

    @POST
    @Timed
    @Operation(summary = "Get Tasks stats",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "userTaskStats")
    @RolesAllowed(value = "userTaskStats")
    @Path("/stats")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
        public GenericResponse<UserStatsAndFilters> getUserStats(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                             @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                             TaskStatsRequest taskStatsRequest,
                                                             @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String requester = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        // Actor won't be null here, it will take value from userDetails.getExternalReferenceId()
        // which we get from token and token will always come otherwise it will be 403
        if (StringUtils.isNotBlank(taskStatsRequest.getAssignedTo()) ) {
            validationService.validateRequesterInHierarchy(taskStatsRequest.getAssignedTo(), requester);
        } else {
            taskStatsRequest.setAssignedTo(requester);
        }
        return GenericResponse.<UserStatsAndFilters>builder()
                .success(true)
                .data(taskDiscoveryService.getUserStats(taskStatsRequest))
                .build();
    }
    @GET
    @Timed
    @Operation(summary = "Get Filter Options",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "getTaskFilters")
    @RolesAllowed(value = "getTaskFilters")
    @Path("/filters/{taskRequestType}")
    @ApiKillerMeta(tags = {OperationType.READ})
        public GenericResponse<ViewWiseFilters> getFilterOptions(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @PathParam("taskRequestType") TaskSearchRequestType type) {
        return GenericResponse.<ViewWiseFilters>builder()
                .success(true)
                .data(taskDiscoveryService.getTaskFilterOptions(type))
                .build();
    }

    @GET
    @Timed
    @Operation(summary = "check number of task present in servicing base",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "userTaskListing")
    @RolesAllowed(value = "userTaskListing")
    @Path("/service/base")
    @ApiKillerMeta(tags = {OperationType.READ})
        public GenericResponse<List<DiscoveryTaskInstance>> getServiceBaseTasks(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @NotEmpty @QueryParam("entityId") String entityId) {
        return GenericResponse.<List<DiscoveryTaskInstance>>builder()
                .success(true)
                .data(taskDiscoveryService.getServiceBaseTasks(entityId))
                .build();
    }

    @GET
    @Path("/taskDetails/{taskInstanceId}")
    @Timed
    @Authorize(value = "taskDetails")
    @RolesAllowed(value = "taskDetails")
    @Operation(summary = "Fetch Task Instance")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
        public GenericResponse<TaskDetailResponse> getTaskDetails(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                              @NotNull @NotEmpty @PathParam("taskInstanceId") String taskInstanceId
    ) {
        TaskDetailRequest taskDetailRequest = TaskDetailRequest.builder().taskInstanceId(taskInstanceId).build();
        TaskDetailResponse response = taskDiscoveryService.getTaskDetailsById(taskDetailRequest);
        return GenericResponse.<TaskDetailResponse>builder()
                .success(response != null)
                .data(response)
                .build();
    }


    @GET
    @Timed
    @Operation(summary = "Get list of agents who are eligible to perform a task",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "agentTaskEligibility")
    @RolesAllowed(value = "agentTaskEligibility")
    @Path("/{taskInstanceId}/fetch/eligible/agents")
    @ApiKillerMeta(tags = {killswitch.enums.OperationType.READ})
    @LegionGateKeeper
    public GenericResponse<AgentTaskEligibilityResponse> fetchAgentsEligibleForTask(@Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                                                    @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                                    @BeanParam AgentTaskEligibilityRequest request,
                                                                                    @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String userId = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        return GenericResponse.<AgentTaskEligibilityResponse>builder()
                .success(true)
                .data(taskDiscoveryService.fetchAgentsEligibleForTask(userId, request))
                .build();
    }

}
