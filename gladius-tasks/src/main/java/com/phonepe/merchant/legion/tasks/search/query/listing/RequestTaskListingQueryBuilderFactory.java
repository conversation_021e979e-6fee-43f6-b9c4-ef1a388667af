package com.phonepe.merchant.legion.tasks.search.query.listing;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ListingRequestMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.legion.tasks.EntityHistoryViewEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.AgentTaskEligibilityEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.AllTaskListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.AssignedViewTaskListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.DiscoveryViewTaskListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.EntityViewListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.TaskTypeListingEnricherProvider;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import java.util.ArrayList;
import java.util.List;

import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;

@Slf4j
@Singleton
public class RequestTaskListingQueryBuilderFactory {

    private final AssignedViewTaskListingEnricher assignedViewTaskListingEnricher;
    private final DiscoveryViewTaskListingEnricher discoveryViewTaskListingEnricher;
    private final EntityViewTaskListingEnricher entityViewTaskListingEnricher;
    private final EntityTaskListingEnricher entityTaskListingEnricher;
    private final AgentTaskEligibilityEnricher agentTaskEligibilityEnricher;
    private final TaskTypeListingQueryEnricher taskTypeListingQueryEnricher;
    private final EntityHistoryViewTaskListingEnricher entityHistoryViewTaskListingEnricher;

    @Inject
    public RequestTaskListingQueryBuilderFactory(
            @AssignedViewTaskListingEnricherProvider
            RequestViewTaskListingEnricher assignedViewTaskListingEnricher,
            @DiscoveryViewTaskListingEnricherProvider
            RequestViewTaskListingEnricher discoveryViewTaskListingEnricher,
            @EntityViewListingEnricherProvider
            RequestViewTaskListingEnricher entityViewTaskListingEnricher,
            @AllTaskListingEnricherProvider
            RequestViewTaskListingEnricher fetchAllTaskListingEnricher,
            @TaskTypeListingEnricherProvider
            RequestViewTaskListingEnricher taskListingEnricher,
            @AgentTaskEligibilityEnricherProvider
            RequestViewTaskListingEnricher agentTaskEligibilityEnricher,
            @EntityHistoryViewEnricherProvider
            RequestViewTaskListingEnricher entityTaskHistoryViewEnricher) {
        this.assignedViewTaskListingEnricher = (AssignedViewTaskListingEnricher) assignedViewTaskListingEnricher;
        this.discoveryViewTaskListingEnricher = (DiscoveryViewTaskListingEnricher) discoveryViewTaskListingEnricher;
        this.entityViewTaskListingEnricher = (EntityViewTaskListingEnricher) entityViewTaskListingEnricher;
        this.entityTaskListingEnricher = (EntityTaskListingEnricher) fetchAllTaskListingEnricher;
        this.taskTypeListingQueryEnricher = (TaskTypeListingQueryEnricher) taskListingEnricher;
        this.agentTaskEligibilityEnricher = (AgentTaskEligibilityEnricher) agentTaskEligibilityEnricher;
        this.entityHistoryViewTaskListingEnricher = (EntityHistoryViewTaskListingEnricher) entityTaskHistoryViewEnricher;
    }

    private List<Filter> applyFilters(EntityTaskListingRequest request) {
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_ID, request.getEntityId()));
        if (request.getListingRequestMeta() != null) {
            ListingRequestMeta listingRequestMeta = request.getListingRequestMeta();
            if (!isNullOrEmpty(listingRequestMeta.getAdditionalFilters())) {
                filters.addAll(listingRequestMeta.getAdditionalFilters());
            }
        }
        return filters;
    }

    public BoolQueryBuilder buildQuery(String actor, TaskListingRequest request) {
        List<Filter> filters = applyFilters(request);
        return request.getRequestType().accept(new TaskViewRequestType.TaskViewRequestTypeVisitor<>() {
            @Override
            public BoolQueryBuilder visitAssignedRequest(TaskListingRequest payload) {
                return assignedViewTaskListingEnricher.getQuery(actor, filters);
            }

            @Override
            public BoolQueryBuilder visitDiscoveryRequest(TaskListingRequest payload) {
                return discoveryViewTaskListingEnricher.getQuery(actor, filters);
            }

            @Override
            public BoolQueryBuilder visitEntityRequest(TaskListingRequest payload) {
                return entityViewTaskListingEnricher.getQuery(actor, filters);
            }

            @Override
            public BoolQueryBuilder visitEntityHistorRequest(TaskListingRequest payload) {
                return entityHistoryViewTaskListingEnricher.getQuery(actor, filters);
            }
        }, request);

    }

    public BoolQueryBuilder buildEntityTaskListingQuery(EntityTaskListingRequest request) {
        List<Filter> filterList = applyFilters(request);
        return entityTaskListingEnricher.getQuery("actor", filterList);
    }

    public BoolQueryBuilder buildEntityTaskListingQuery(EntityTaskListingRequest request, BoolQueryBuilder builder) {
        List<Filter> filterList = applyFilters(request);
        return builder.must(taskTypeListingQueryEnricher.getQuery("actor", filterList));
    }

    public BoolQueryBuilder buildAgentTaskEligibilityQuery(List<String> agentIds, String taskInstanceId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_INSTANCE_ID, taskInstanceId));
        BoolQueryBuilder agentShouldQuery = QueryBuilders.boolQuery();
        for (String agentId : agentIds) {
            BoolQueryBuilder agentQuery = agentTaskEligibilityEnricher.getQuery(agentId, List.of());
            agentQuery.queryName(agentId);
            agentShouldQuery.should(agentQuery);
        }
        boolQueryBuilder.must(agentShouldQuery);
        return boolQueryBuilder;
    }

}
