package com.phonepe.merchant.legion.tasks.search.query.filter;

import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.filter.LeadViewTaskFilterRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import org.elasticsearch.index.query.QueryBuilder;

import java.time.Clock;
import java.util.List;

import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

@Singleton
public class LeadViewTaskFilterRequestQueryBuilder extends BaseTaskFilterRequestQueryBuilder<LeadViewTaskFilterRequest> {

    public LeadViewTaskFilterRequestQueryBuilder() {
        super(Clock.systemDefaultZone());
    }

    protected LeadViewTaskFilterRequestQueryBuilder(Clock clock) {
        super(clock);
    }

    @Override
    public QueryBuilder enrichFilters(String actor, LeadViewTaskFilterRequest request) {
        List<Filter> filters = getBaseFilters();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, actor));
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, EntityType.PHONE_NUMBER.name()));
        return getBoolQuery(filters);
    }
}
