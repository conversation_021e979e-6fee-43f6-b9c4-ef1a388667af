package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntity;
import com.phonepe.merchant.gladius.models.external.request.ExternalEntityAdditionalFields;
import com.phonepe.merchant.gladius.models.external.request.ExternalEntityFetchByIdRequest;
import com.phonepe.merchant.gladius.models.external.request.ExternalEntityRequest;
import com.phonepe.merchant.gladius.models.survey.ClientQcTaskConfig;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EventBasedTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.QcTaskCreationEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.taskevents.SurveyTaskCreationOrAssignmentFailedEvent;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.core.utils.CoreFoxtrotEventUtils;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.flows.TransitionValidator;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDuplicateValidationService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.tasks.EntityType;
import com.utils.StringUtils;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MERCHANT_ID;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.PHONE_NUMBER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_ID;
import static com.phonepe.merchant.legion.core.utils.EventConstants.SurveyEvents.QC_TASK_CREATION_SUCCESS;
import static com.phonepe.merchant.legion.core.utils.EventConstants.SurveyEvents.SURVEY_QC_TASK_CREATION_FAILED;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.ASM;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.NSH;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.RH;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.SH;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.TSM;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.ZSM;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.BAD_REQUEST;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INTERNAL_ERROR;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_AGENT_ID;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.MANAGER_NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.SYSTEM;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toClientTaskCreatedEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toClientTaskCreationFailedEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toClientTaskDeletedEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toClientTaskDeletionFailed;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toClientTaskVerificationFailed;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toClientTaskVerifiedEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toEventBasedTaskCreationFailed;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toEventBasedTaskCreationSuccess;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ClientTaskServiceImpl implements ClientTaskService {

    private final TaskInstanceManagementService taskInstanceManagementService;
    private final TaskManagementService taskManagementService;
    private final LegionService legionService;
    private final ClientQcTaskConfig clientQcTaskConfig;
    private final FoxtrotEventExecutor eventExecutor;
    private final TaskDefinitionService taskDefinitionService;
    private final ValidationService validationService;
    private final MerchantService merchantService;
    private final ExternalEntityService externalEntityService;
    private final TransitionValidator transitionValidator;
    private final TaskDuplicateValidationService taskDuplicateValidationService;
    private static final List<AgentType> eligibleUserRoleForQCTaskAssignment = List.of(TSM, ASM, SH, ZSM, RH, NSH);

    /**
     * Create and assign QC task for managers
     *
     * @param taskInstanceId
     */
    @Override
    @MonitoredFunction
    public StoredTaskInstance createAndAssignQCTask(String taskInstanceId) {
        try {
            // 1. Check if task instance Exists if not send error
            log.info("Request received for creation of QC task against task instance :{}", taskInstanceId);
            TaskInstance task = taskInstanceManagementService.getById(TaskByIdRequest.builder()
                    .taskInstanceId(taskInstanceId)
                    .build());
            if (clientQcTaskConfig.getMapping().get(task.getTaskDefinitionId()) != null) {
                //2. Check if agent exist if not throw error, this should not fail. Get manager id as actor
                AgentProfile agent = legionService.getAgentProfile(task.getCurActor());
                //3. Create and assign the task, get taskDefinition id from config
                TaskCreateRequest taskCreateRequest = TaskCreateRequest.builder()
                        .markAvailable(false)
                        .taskInstance(CreateTaskInstanceRequest.builder()
                                .createdBy(task.getCurActor())
                                .entityId(taskInstanceId)
                                .campaignId(clientQcTaskConfig.getQcTaskCampaignId())
                                .taskDefinitionId(clientQcTaskConfig.getMapping().get(task.getTaskDefinitionId()).getTaskDefinitionId())
                                .build())
                        .build();
                StoredTaskInstance assignedTask = taskInstanceManagementService.createAndAssignTask(taskCreateRequest, agent.getManagerId());
                //4. Send foxtrot event to check success rate
                QcTaskCreationEvent qcTaskCreationEvent = QcTaskCreationEvent.builder()
                        .taskInstanceId(assignedTask.getTaskInstanceId())
                        .taskDefinitionId(assignedTask.getTaskDefinitionId())
                        .actor(assignedTask.getCurActor())
                        .build();
                eventExecutor.ingest(CoreFoxtrotEventUtils.getEventObject(qcTaskCreationEvent, assignedTask.getTaskInstanceId(), QC_TASK_CREATION_SUCCESS));
                log.info("QC task created for {} - QC task id {}", taskInstanceId, assignedTask.getTaskInstanceId());
                return assignedTask;
            } else {
                log.info("Skipping QC task Creation as this is not outReach task {}", taskInstanceId);
                return null;
            }
        } catch (Exception e) {
            SurveyTaskCreationOrAssignmentFailedEvent surveyTaskCreationOrAssignmentFailedEvent = SurveyTaskCreationOrAssignmentFailedEvent.builder()
                    .taskInstaceId(taskInstanceId)
                    .errorMessage(e.getMessage())
                    .build();
            eventExecutor.ingest(CoreFoxtrotEventUtils.getEventObject(surveyTaskCreationOrAssignmentFailedEvent, taskInstanceId, SURVEY_QC_TASK_CREATION_FAILED));
            return null;
        }
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance createTaskFromEvent(EventBasedTaskCreationRequest request) {
        try {
            TaskDefinitionInstance taskDefinition = taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder()
                    .taskDefinitionId(request.getTaskDefinitionId())
                    .build());
            if (!taskDefinition.getActionId().equals(request.getTaskType().name())) {
                throw LegionException.error(BAD_REQUEST,
                        Map.of(MESSAGE, "Task Type does not match any action id"));
            }
            CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                    .createdBy(SYSTEM)
                    .entityId(request.getEntityId())
                    .campaignId(request.getCampaignId())
                    .taskDefinitionId(request.getTaskDefinitionId())
                    .build();
            TaskCreateRequest taskCreateRequest = TaskCreateRequest.builder()
                    .markAvailable(request.getMarkAvailable())
                    .taskInstance(createTaskInstanceRequest)
                    .build();
            StoredTaskInstance finalTaskInstance;
            if (Boolean.TRUE.equals(request.getMarkAvailable())) {
                finalTaskInstance = taskManagementService.command(taskCreateRequest, null);
            } else {
                AgentProfile agentProfile = getEligibleUserProfileFromHierarchy(request.getAgentId(), eligibleUserRoleForQCTaskAssignment);
                finalTaskInstance = taskInstanceManagementService.createAndAssignTask(taskCreateRequest, agentProfile.getAgentId());
            }
            log.info("Task created from event - entity id: {} - task instance id: {}",
                    finalTaskInstance.getEntityId(),
                    finalTaskInstance.getTaskInstanceId());
            eventExecutor.ingest(toEventBasedTaskCreationSuccess(finalTaskInstance, request));
            return finalTaskInstance;
        } catch (Exception e) {
            log.info("Task created from event failed for - entity id: {} - task definition id: {} - campaign id: {}",
                    request.getEntityId(),
                    request.getTaskDefinitionId(),
                    request.getCampaignId());
            eventExecutor.ingest(toEventBasedTaskCreationFailed(request, e.getMessage()));
            throw LegionException.propagate(BAD_REQUEST, e);
        }
    }

    private AgentProfile getEligibleUserProfileFromHierarchy(String agentId, List<AgentType> eligibleUserRoles) {
        AgentProfile agentProfile = legionService.getAgentProfile(agentId);
        Set<String> visitedUserIds = new HashSet<>();
        visitedUserIds.add(agentProfile.getAgentId());
        if (StringUtils.isEmpty(agentProfile.getManagerId()) || visitedUserIds.contains(agentProfile.getManagerId())) {
            throw LegionException.error(INVALID_AGENT_ID,
                    Map.of(MESSAGE, "Requested role does not exist in agent hierarchy"));
        }
        agentProfile = legionService.getAgentProfile(agentProfile.getManagerId());
        while (!eligibleUserRoles.contains(agentProfile.getAgentType())) {
            visitedUserIds.add(agentProfile.getAgentId());
            if (StringUtils.isEmpty(agentProfile.getManagerId()) || visitedUserIds.contains(agentProfile.getManagerId())) {
                throw LegionException.error(INVALID_AGENT_ID,
                        Map.of(MESSAGE, "Requested role does not exist in agent hierarchy"));
            }
            agentProfile = legionService.getAgentProfile(agentProfile.getManagerId());
        }
        return agentProfile;
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance createAndAssignClientTask(ClientTaskCreateAndAssignRequest request) {
        try {
            log.info("Task requested by client :- {} - entity id: {}", request.getCreatedBy(), request.getEntityId());
            TaskDefinitionInstance taskDefinitionInstance = validationService.validateAndGetTaskDefinition(request.getTaskDefinitionId());
            Campaign campaign = validationService.validateAndGetCampaign(request.getCampaignId());
            validationService.validateEntity(request.getEntityId(), request.getEntityType());
            validationService.validateBoundedAssignmentWithMissingAssignee(request.isMarkAvailable(), request.getAssigneeId());
            List<DiscoveryTaskInstance> tasks = transitionValidator.getDuplicateTasks(request.getEntityType(), request.getEntityId(), taskDefinitionInstance.getActionId(), campaign);
            //this is done to check if no deleted tasks are present in the result due to ES replication lag
            tasks = tasks.stream()
                    .filter(task -> !transitionValidator.checkIfTaskDeletedInES(task.getTaskInstanceId()))
                    .toList();
            if (!tasks.isEmpty() && request.isForceTaskCreationRequest()) {
                StoredTaskInstance storedTaskInstance = taskDuplicateValidationService.validateAndGetDuplicateTaskInstance(tasks, request);
                if (storedTaskInstance != null) {
                    return storedTaskInstance;
                }
                tasks.forEach(task -> deleteClientTask(ClientTaskDeleteRequest.builder()
                        .taskInstanceId(task.getTaskInstanceId())
                        .reason(request.getReasonForDeletion())
                        .deletedBy(request.getCreatedBy())
                        .build()));
            } else if (!tasks.isEmpty()) {
                throw LegionException.error(LegionTaskErrorCode.DUPLICATE_TASK_CREATION_REQUEST, Map.of(TASK_INSTANCE_ID, tasks.get(0).getTaskInstanceId()));
            }

            String managerId = getManagerIdForManagerTaskCreation(request);
            StoredTaskInstance storedTaskInstance = createClientTaskAndAssign(request, managerId);
            log.info("Task created by client :- {} - entity id: {} - task instance id: {}", request.getCreatedBy(), storedTaskInstance.getEntityId(), storedTaskInstance.getTaskInstanceId());
            eventExecutor.ingest(toClientTaskCreatedEvent(storedTaskInstance, request));
            return storedTaskInstance;
        } catch (LegionException e) {
            log.info("Task created by client failed for - client id: {} - task definition id: {} - campaign id: {} {}", request.getCreatedBy(), request.getTaskDefinitionId(), request.getCampaignId(), request.getEntityId());
            eventExecutor.ingest(toClientTaskCreationFailedEvent(request, e));
            throw e;
        } catch (Exception e) {
            log.error("Error creating task for client:{}, entityId:{}, ", request.getCreatedBy(), request.getEntityId(), e);
            throw LegionException.error(CoreErrorCode.INTERNAL_ERROR, Map.of(MESSAGE, e.getMessage()));
        }
    }

    private String getManagerIdForManagerTaskCreation(ClientTaskCreateAndAssignRequest request) {
        String managerId = null;
        if (request.getAssigneeId() != null && request.isCreateTaskForManager()) {
            managerId = legionService.getAgentProfile(request.getAssigneeId()).getManagerId();
        }
        return managerId;
    }

    private StoredTaskInstance createClientTaskAndAssign(ClientTaskCreateAndAssignRequest request, String managerId) {
        CreateTaskInstanceRequest createTaskInstanceRequest = TaskInstanceTransformationUtils.toCreateTaskInstanceRequest(request);
        TaskCreateRequest taskCreateRequest = TaskCreateRequest.builder()
                .markAvailable(request.isMarkAvailable())
                .taskInstance(createTaskInstanceRequest)
                .build();
        if (!request.isMarkAvailable()) {
            if (request.isCreateTaskForManager() && null != managerId) {
                return taskInstanceManagementService.createAndAssignTask(taskCreateRequest, managerId);
            } else if (request.isCreateTaskForManager() && null == managerId) {
                throw LegionException.error(MANAGER_NOT_FOUND,
                        Map.of(MESSAGE, "Task for Manager can't be created as Manager does not exist"));
            } else {
                return taskInstanceManagementService.createAndAssignTask(taskCreateRequest, request.getAssigneeId());
            }
        } else {
            return taskManagementService.command(taskCreateRequest, null);
        }
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance deleteClientTask(ClientTaskDeleteRequest request) {
        try {
            ClientTaskDeleteRequest deletionRequest = ClientTaskDeleteRequest.builder()
                    .taskInstanceId(request.getTaskInstanceId())
                    .deletedBy(request.getDeletedBy())
                    .deletionReason(request.getReason())
                    .build();

            StoredTaskInstance storedTaskInstance = taskManagementService.deleteTask(deletionRequest);
            log.info("Task deleted by client :- {} - entity id: {} - task instance id: {}", request.getDeletedBy(), storedTaskInstance.getEntityId(), storedTaskInstance.getTaskInstanceId());
            eventExecutor.ingest(toClientTaskDeletedEvent(request));
            return storedTaskInstance;
        } catch (Exception e) {
            log.info("Task created by client failed for - task instance id: {}", request.getTaskInstanceId());
            eventExecutor.ingest(toClientTaskDeletionFailed(request, e));
            throw LegionException.propagate(BAD_REQUEST, e);
        }
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance verifyClientTask(TaskManualVerificationRequest request) {
        try {

            StoredTaskInstance storedTaskInstance = taskManagementService.verifyTask(request);
            log.info("Task verified by client :- {} - entity id: {} - task instance id: {}", request.getVerifiedBy(), storedTaskInstance.getEntityId(), storedTaskInstance.getTaskInstanceId());
            eventExecutor.ingest(toClientTaskVerifiedEvent(request));
            return storedTaskInstance;

        } catch (Exception e) {
            log.info("Task verification by client failed for - task instance id: {}", request.getTaskInstanceId());
            eventExecutor.ingest(toClientTaskVerificationFailed(request, e));
            throw LegionException.propagate(INTERNAL_ERROR, e);
        }
    }

    @Override
    public void tagMxnToExternalEntity(String merchantId, String storeId, String taskInstanceId) {
        String clientProvidedId = String.join("_", merchantId, storeId);

        // fetch the task
        TaskInstance taskInstance = taskInstanceManagementService.getById(TaskByIdRequest.builder()
                .taskInstanceId(taskInstanceId)
                .build());
        if (taskInstance.getEntityType() != EntityType.EXTERNAL) {
            return;
        }

        // fetch the merchant's info
        MerchantProfile merchantProfile = merchantService.getMerchantDetails(merchantId);

        // fetch the external entity
        String externalEntityId = taskInstance.getEntityId();
        ExternalEntity externalEntity = externalEntityService.get(ExternalEntityFetchByIdRequest.builder()
                .externalEntityId(externalEntityId)
                .build());

        // update external entity
        ExternalEntityAdditionalFields externalEntityMeta = externalEntity.getExternalEntityMeta();
        if (externalEntityMeta == null) {
            externalEntityMeta = new ExternalEntityAdditionalFields();
        }
        externalEntityMeta.setPhoneNumber(merchantProfile.getPhoneNumber());

        if (externalEntityMeta.getOtherMeta() == null) {
            externalEntityMeta.setOtherMeta(new HashMap<>());
        }
        externalEntityMeta.getOtherMeta().put(PHONE_NUMBER, externalEntityMeta.getPhoneNumber());
        externalEntityMeta.getOtherMeta().put(MERCHANT_ID, merchantId);
        externalEntityMeta.getOtherMeta().put(STORE_ID, storeId);

        externalEntityService.update(ExternalEntityRequest.builder()
                .externalEntityId(externalEntity.getExternalEntityId())
                .name(externalEntity.getName())
                .location(externalEntity.getLocation())
                .clientProvidedId(clientProvidedId)
                .clientId(externalEntity.getClientId())
                .externalEntityMeta(externalEntityMeta)
                .build());
    }

}
