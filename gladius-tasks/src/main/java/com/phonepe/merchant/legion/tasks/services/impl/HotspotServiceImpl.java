package com.phonepe.merchant.legion.tasks.services.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.hotspots.GeoRegion;
import com.phonepe.merchant.gladius.models.hotspots.GeoRegionHashEntity;
import com.phonepe.merchant.gladius.models.hotspots.HotspotRegion;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotAccessDetailsRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotCreateRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotMetaData;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotSyncRequest;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotAccessDetailsResponse;
import com.phonepe.merchant.gladius.models.hotspots.response.HotspotDto;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.EventConstants;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.hotspots.HotspotGenerator;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.GeneralPurposeCache;
import com.phonepe.merchant.legion.tasks.cache.HotspotConfigCache;
import com.phonepe.merchant.legion.tasks.cache.HotspotTaskStatCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.repository.HotspotRepository;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskSearchRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.services.HotspotService;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.Filters;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregator;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.LOCATION;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POLYGON_IDS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.search.query.BaseSearchRequestQueryBuilder.getBaseQuery;
import static com.phonepe.merchant.legion.tasks.utils.HotspotTransformationUtils.extractHotspotMetaData;
import static com.phonepe.merchant.legion.tasks.utils.HotspotTransformationUtils.toHotspotDto;
import static com.phonepe.merchant.legion.tasks.utils.HotspotTransformationUtils.toStoredHotspot;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.HOTSPOT_TYPE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MAX_RESULTS;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TOTAL_COUNT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TOTAL_POINTS;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTasksFromSearchHits;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Slf4j
public class HotspotServiceImpl implements HotspotService {
    private final HotspotRepository hotspotRepository;
    private final LegionService legionService;
    private final TaskSearchRequestQueryBuilderFactory taskSearchRequestQueryBuilderFactory;
    private final HotspotRequestQueryBuilder hotspotRequestQueryBuilder;
    private final ESRepository esRepository;
    private final HotspotGenerator hotspotGenerator;
    private final FilterCraftBuilderCache filterCraftBuilderCache;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;
    private final GeneralPurposeCache generalPurposeCache;
    private final AtlasService atlasService;
    private final HotspotConfigCache hotspotConfigCache;
    private final HotspotTaskStatCache hotspotTaskStatCache;
    private final SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    @MonitoredFunction
    @Override
    public List<HotspotDto> syncHotspots(String actorId, HotspotSyncRequest syncRequest) {
        try {
            StoredHotspotConfig storedHotspotConfig = hotspotConfigCache.getHotspotConfigFromCache(syncRequest.getHotspotType(), true);
            if (!syncRequest.isDryRun()) {
                // Deactivate Currently active Hotspot (of given Type) in the sector
                List<StoredHotspot> currentActiveHotspots = hotspotRepository.getSectorHotspots(syncRequest.getSectorId(), syncRequest.getHotspotType());
                currentActiveHotspots.forEach(hotspot -> deactivateHotspot(actorId, hotspot));
            }

            HotspotMetaData hotspotMetaData = extractHotspotMetaData(storedHotspotConfig.getHotspotMetadata());
            // Get Query Builder for the hotspot Type and Sector
            BoolQueryBuilder queryBuilder = filterCraftBuilderCache.get(FilterCraftBuilderCacheKey.builder()
                            .config(hotspotMetaData.getFiltercraftConfig().toString())
                            .build()).convertToESFilter()
                    .must(sectorGeofenceQueryBuilder.buildSectorGeofenceQuery(List.of(syncRequest.getSectorId()), true))
                    .must(getBaseQuery(LegionTaskStateMachineState.getDiscoverableStates()));

            // Get Geo Regions for the matching Tasks
            List<GeoRegion> geoRegions = getGeoRegions(queryBuilder, syncRequest.getGeoRegionHashEntity(), hotspotMetaData);

            return createHotspots(actorId, HotspotCreateRequest.builder()
                    .sectorId(syncRequest.getSectorId())
                    .geoRegionList(geoRegions)
                    .hotspotType(syncRequest.getHotspotType())
                    .build(), syncRequest.isDryRun());
        } catch (Exception e) {
            log.error("Sync hotspots failed", e);
            foxtrotEventIngestionService.ingestHotspotSyncFailedEvent(syncRequest);
            throw e;
        }
    }


    @Override
    public List<HotspotDto> getStoredHotspots(String actorId, String sectorId, String category) {
        String hotspotType = hotspotConfigCache.fetchHotspotTypeForCategoryAndSector(category, sectorId);
        List<StoredHotspot> storedHotspots = hotspotRepository.getSectorHotspots(sectorId, hotspotType);
        if (storedHotspots.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, HotspotDto.Stats> currentTaskStats = Objects.nonNull(actorId) ?
                hotspotTaskStatCache.get(hotspotType + "-" + sectorId, k -> getCurrentTaskStats(storedHotspots, actorId))
                : Collections.emptyMap();
        List<HotspotDto> result = new ArrayList<>(storedHotspots.stream().map(h -> {
            HotspotDto hotspotDto = toHotspotDto(h);
            hotspotDto.setStats(currentTaskStats.getOrDefault(hotspotDto.getId(), HotspotDto.Stats.builder().totalCount(0L).totalPoints(0.0).build()));
            return hotspotDto;
        }).toList());
        if (Objects.nonNull(actorId)) {
            result = new ArrayList<>(result.stream().filter(h -> h.getStats().getTotalCount() != 0).toList());
        }
        Collections.sort(result);
        return result;
    }

    @Override
    public List<HotspotDto> fetchHotspotsByLocation(double latitude, double longitude) {
        List<String> sectorIds = atlasService.getSectorIdByLatLong(latitude, longitude);
        List<StoredHotspot> storedHotspots = hotspotRepository.fetchSectorHotspots(sectorIds);
        if (storedHotspots.isEmpty()) {
            return Collections.emptyList();
        }
        return storedHotspots.stream()
                .filter(h -> {
                    HotspotRegion region = h.getRegion();
                    if (region == null || region.getGeofence() == null || region.getGeofence().isEmpty()) {
                        return false;
                    }
                    return region.isLocationInPolygon(latitude, longitude);
                })
                .map(h -> HotspotDto.builder()
                        .id(h.getId())
                        .type(h.getHotspotType())
                        .build())
                .collect(Collectors.toList());
    }


    @Override
    public HotspotAccessDetailsResponse getHotspotAccessDetails(String actorId, HotspotAccessDetailsRequest hotspotAccessDetailsRequest) {
        Set<String> storedHotspotTypes = (Set<String>) generalPurposeCache.get(HOTSPOT_TYPE, k -> hotspotRepository.getHotspotType());
        log.info("StoredHotspotTypes: {}", storedHotspotTypes);
        AgentProfile profile = legionService.getAgentProfile(actorId);
        List<String> accessibleSectors = legionService.getAllAccessibleSectorsOfAgent(actorId);
        Map<String, Set<String>> accessDetailsMap = new HashMap<>();
        storedHotspotTypes.forEach(storedHotspotType -> {
            StoredHotspotConfig storedHotspotConfig = hotspotConfigCache.getHotspotConfigFromCache(storedHotspotType, false);
            if (storedHotspotConfig != null && storedHotspotConfig.validRole(profile.getAgentType())) {
                List<String> sectors = storedHotspotConfig.validSectors(accessibleSectors);
                if (!sectors.isEmpty()) {
                    String category = storedHotspotConfig.getCategory();
                    accessDetailsMap.computeIfAbsent(category, k -> new HashSet<>()).addAll(sectors);
                }
            }
        });
        List<HotspotAccessDetailsResponse.HotspotAccessDetails> accessDetails = accessDetailsMap.entrySet().stream()
                .map(entry -> HotspotAccessDetailsResponse.HotspotAccessDetails.builder()
                        .type(entry.getKey())
                        .sectors(new ArrayList<>(entry.getValue()))
                        .build())
                .collect(Collectors.toList());
        return HotspotAccessDetailsResponse.builder().details(accessDetails).build();
    }

    public List<HotspotDto> createHotspots(String actorId, HotspotCreateRequest hotspotCreateRequest, boolean dryRun) {
        StoredHotspotConfig storedHotspotConfig = hotspotConfigCache.getHotspotConfigFromCache(hotspotCreateRequest.getHotspotType(), true);
        List<HotspotRegion> hotspotRegions = hotspotGenerator.generateHotspots(hotspotCreateRequest.getGeoRegionList());
        List<HotspotDto> result = new ArrayList<>(hotspotRegions.size());
        HotspotMetaData hotspotMetaData = SerDe.readValue(storedHotspotConfig.getHotspotMetadata(), new TypeReference<HotspotMetaData>() {
        });
        hotspotRegions.stream().filter(region -> region.getValue() >= hotspotMetaData.getMinTaskThreshold())
                .forEach(region -> {
            StoredHotspot storedHotspot = toStoredHotspot(actorId, hotspotCreateRequest, region, storedHotspotConfig);
            if (!dryRun) {
                storedHotspot = hotspotRepository.save(storedHotspot);
                foxtrotEventIngestionService.ingestHotspotStatusChangeEvent(storedHotspot, EventConstants.HotspotEvents.HOTSPOT_ACTIVE);
            }
            result.add(toHotspotDto(storedHotspot));
        });
        return result;
    }


    public HotspotDto deactivateHotspot(String actorId, String hotspotId){
        Optional<StoredHotspot> hotspot = hotspotRepository.get(hotspotId);
        if (hotspot.isEmpty()) {
            throw LegionException.error(CoreErrorCode.NOT_FOUND);
        }
        return deactivateHotspot(actorId, hotspot.get());
    }

    private HotspotDto deactivateHotspot(String actorId, StoredHotspot hotspot){
        hotspotRepository.deactivateHotspot(hotspot.getId(), actorId);
        Optional<StoredHotspot> deactivatedHotspot = hotspotRepository.get(hotspot.getId());
        if (deactivatedHotspot.isEmpty()) {
            throw LegionException.error(CoreErrorCode.NOT_FOUND);
        }
        foxtrotEventIngestionService.ingestHotspotStatusChangeEvent(deactivatedHotspot.get(), EventConstants.HotspotEvents.HOTSPOT_DEACTIVATED);
        return toHotspotDto(deactivatedHotspot.get());
    }


    private List<GeoRegion> getGeoRegions(QueryBuilder queryBuilder, GeoRegionHashEntity hashEntity, HotspotMetaData hotspotMetaData) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(queryBuilder)
                .fetchSource(new String[]{LOCATION}, null)
                .size(MAX_RESULTS);

        SearchRequest searchRequest = new SearchRequest(TASK_INDEX);
        searchRequest.source(searchSourceBuilder);
        searchRequest.scroll(TimeValue.timeValueMinutes(2L));

        SearchResponse searchResponse = esRepository.search(searchRequest, RequestOptions.DEFAULT);

        String scrollId = searchResponse.getScrollId();
        Map<String, Long> taskCountPerGridHash = new HashMap<>();

        while (true) {
            List<DiscoveryTaskInstance> tasks = getTasksFromSearchHits(searchResponse.getHits().iterator());
            tasks.forEach(task -> {
                String gridHash = hashEntity.getHashString(task.getLocation(), hotspotMetaData.getGridLevel());
                taskCountPerGridHash.merge(gridHash, 1L, Long::sum);
            });

            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
            searchScrollRequest.scroll(TimeValue.timeValueMinutes(2L));
            SearchResponse scrollResponse = esRepository.scroll(searchScrollRequest, RequestOptions.DEFAULT);

            if (scrollResponse.getHits().getHits().length == 0) {
                break;
            }

            // Update the scrollId and process the next batch
            scrollId = scrollResponse.getScrollId();
            searchResponse = scrollResponse;
        }

        return taskCountPerGridHash.entrySet().stream()
                .filter(entry -> entry.getValue() >= Optional.ofNullable(hotspotMetaData.getGridLevelTaskThreshold()).orElse(1))
                .map(entry -> GeoRegion.builder()
                        .geoRegionHashEntity(hashEntity)
                        .hashString(entry.getKey())
                        .value(entry.getValue())
                        .build())
                .collect(Collectors.toList());
    }


    private QueryBuilder getSectorDiscoveryViewQuery(StoredHotspot hotspot, String actorId) {
        return taskSearchRequestQueryBuilderFactory.queryBuilder(actorId, SectorDiscoveryViewTaskSearchRequest.builder()
                .taskSearchRequestType(TaskSearchRequestType.SECTOR_DISCOVERY_VIEW)
                .sectorId(hotspot.getSectorId())
                .filters(new ArrayList<>())
                .location(EsLocationRequest.builder().
                        lat(hotspot.getRegion().getCentroid().getLatitude())
                        .lon(hotspot.getRegion().getCentroid().getLongitude())
                        .build()).build());
    }

    @MonitoredFunction
    private Map<String, HotspotDto.Stats> getCurrentTaskStats(List<StoredHotspot> hotspots, String actorId) {
        // Build keyed filters for the geofences of each hotspot
        List<FiltersAggregator.KeyedFilter> hotspotFilters = hotspots.stream()
                .map(h -> new FiltersAggregator.KeyedFilter(h.getId(),
                        hotspotRequestQueryBuilder.buildQuery(actorId, h)))
                .toList();

        FiltersAggregationBuilder filtersAggregation = AggregationBuilders.filters(
                "hotspotFilters",
                hotspotFilters.toArray(new FiltersAggregator.KeyedFilter[0])
        );

        // Add sub-aggregations for each filter
        filtersAggregation.subAggregation(AggregationBuilders.count(TOTAL_COUNT).field(TASK_INSTANCE_ID));
        filtersAggregation.subAggregation(AggregationBuilders.sum(TOTAL_POINTS).field(POINTS));

        Optional<SearchResponse> optionalResponse = Optional.ofNullable(
                esRepository.getAggregationResult(
                        TASK_INDEX,
                        getSectorDiscoveryViewQuery(hotspots.get(0), actorId),
                        List.of(filtersAggregation)
                )
        );

        return optionalResponse.map(response -> {
            Filters filters = response.getAggregations().get("hotspotFilters");
            return filters.getBuckets().stream()
                    .collect(Collectors.toMap(
                            Filters.Bucket::getKeyAsString, // Hotspot ID as the key
                            bucket -> {
                                long totalCount = Optional.ofNullable(bucket.getAggregations().get(TOTAL_COUNT))
                                        .map(ValueCount.class::cast)
                                        .map(ValueCount::getValue)
                                        .orElse(0L);

                                double totalPoints = Optional.ofNullable(bucket.getAggregations().get(TOTAL_POINTS))
                                        .map(Sum.class::cast)
                                        .map(Sum::getValue)
                                        .orElse(0.0);

                                return HotspotDto.Stats.builder()
                                        .totalCount(totalCount)
                                        .totalPoints(totalPoints)
                                        .build();
                            }
                    ));
        }).orElse(Collections.emptyMap());
    }

}
