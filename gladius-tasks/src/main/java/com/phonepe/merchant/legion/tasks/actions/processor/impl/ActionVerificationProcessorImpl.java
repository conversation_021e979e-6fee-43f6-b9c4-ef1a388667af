package com.phonepe.merchant.legion.tasks.actions.processor.impl;

import com.google.common.collect.Maps;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.services.ClockWorkService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.merchant.legion.tasks.actions.models.ActionVerificationResponse;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionVerificationProcessor;
import com.phonepe.merchant.legion.tasks.actor.message.TaskVerifyRmqMessage;
import com.phonepe.merchant.legion.tasks.flows.models.LegionTaskStateMachineContext;
import com.phonepe.merchant.legion.tasks.repository.TaskTransitionRepository;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.platform.clockwork.model.ClockworkResponse;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import lombok.Builder;
import org.apache.commons.collections4.MapUtils;
import org.reflections.Reflections;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Singleton
public class ActionVerificationProcessorImpl implements ActionVerificationProcessor {

    private static final String VERIFICATION_HANDLER_PACKAGE = "com.phonepe.merchant.legion.tasks.actions";
    private Map<String, ActionVerifier> verifiers = Maps.newConcurrentMap();

    private final TaskTransitionRepository taskTransitionRepository;
    private final TaskActionService taskActionService;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;

    @Inject
    public ActionVerificationProcessorImpl(TaskTransitionRepository taskTransitionRepository,
                                           Injector injector,
                                           TaskActionService taskActionService,
                                           FoxtrotEventIngestionService foxtrotEventIngestionService) {
        this.taskTransitionRepository = taskTransitionRepository;
        this.taskActionService = taskActionService;
        this.foxtrotEventIngestionService = foxtrotEventIngestionService;

        Reflections reflections = new Reflections(VERIFICATION_HANDLER_PACKAGE);
        final Set<Class<?>> annotatedClasses = reflections
                .getTypesAnnotatedWith(ActionVerifierMarker.class);

        annotatedClasses.forEach(annotatedType -> {
            if (ActionVerifier.class.isAssignableFrom(annotatedType)) {
                ActionVerifierMarker annotation = annotatedType.getAnnotation(ActionVerifierMarker.class);
                final ActionVerifier instance = ActionVerifier.class
                        .cast(injector.getInstance(annotatedType));
                verifiers.put(annotation.name(), instance);
            }
        });
    }

    @Builder
    public ActionVerificationProcessorImpl(Map<String, ActionVerifier> verifiers,
                                           TaskTransitionRepository taskTransitionRepository,
                                           TaskActionService taskActionService,
                                           FoxtrotEventIngestionService foxtrotEventIngestionService) {
        this.verifiers = verifiers;
        this.taskTransitionRepository = taskTransitionRepository;
        this.taskActionService = taskActionService;
        this.foxtrotEventIngestionService = foxtrotEventIngestionService;
    }

    public StoredTaskInstance verify(LegionTaskStateMachineContext stateMachineContext, TaskCompleteRequest taskCompleteRequest, ClockWorkService clockWorkService) {
        if (!taskCompleteRequest.getStoredTaskInstance().isActive()) {
            return taskCompleteRequest.getStoredTaskInstance();
        }

        TaskActionInstance taskActionInstance = taskActionService.getFromCache(
                TaskActionFetchByIdRequest.builder()
                        .taskActionId(taskCompleteRequest.getStoredTaskInstance().getActionId())
                        .build());
        List<VerificationConfig> listOfVerifiers = taskActionInstance.getVerificationStrategy().getVerifiers();

        Map<String, Boolean> verificationContext = new HashMap<>();
        Map<String, Object> verifierContext = new HashMap<>();
        Map<String, Boolean> verificationMetaContext = new HashMap<>();
        listOfVerifiers.forEach(verifier -> {
            VerifierResponse verifierResponse = verifiers.get(verifier.getVerifierName())
                    .verify(taskCompleteRequest, verifier, verifierContext);
            verificationContext.put(verifier.getVerifierName(), verifierResponse.getVerified());
            if (MapUtils.isNotEmpty(verifierResponse.getVerificationContext())) {
                verificationMetaContext.putAll(verifierResponse.getVerificationContext());
            }
        });
        stateMachineContext.setVerificationContext(verificationMetaContext);
        Boolean isTaskVerified = true;
        for (Map.Entry<String, Boolean> isVerifierVerified : verificationContext.entrySet()) {
            if (isVerifierVerified.getValue() == null || Boolean.FALSE.equals(isVerifierVerified.getValue())) {
                isTaskVerified = isVerifierVerified.getValue();
                break;
            }
        }

        if (isTaskVerified == null && taskActionInstance.getVerificationStrategy().isRetryable()) { // check on undeterministic
            if (taskCompleteRequest.getRetriedTimes() > taskActionInstance.getVerificationStrategy().getMaxRetries()) {
                isTaskVerified = false;
            } else {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.SECOND, taskActionInstance.getVerificationStrategy().getNextScheduleInterval());
                taskCompleteRequest.setRetriedTimes(taskCompleteRequest.getRetriedTimes() + 1);
                ClockworkResponse<SchedulingResponse> clockworkResponse = clockWorkService.scheduleClockworkForProvider(TaskVerifyRmqMessage.builder()
                        .taskCompleteRequest(taskCompleteRequest)
                        .actorMessageType(taskActionInstance.getVerificationStrategy().getActorMessageType())
                        .build(), calendar.getTime());
                SchedulingResponse response = clockworkResponse.getData();
                foxtrotEventIngestionService.ingestClockworkRetryEvent(taskCompleteRequest, response.getJobId(), calendar.getTime());
                return taskCompleteRequest.getStoredTaskInstance();
            }
        } else if (isTaskVerified == null) {
            return taskCompleteRequest.getStoredTaskInstance();
        }
        ActionVerificationResponse verificationResponse = ActionVerificationResponse.builder()
                .context(verificationContext)
                .verified(isTaskVerified)
                .build();
        markVerification(verificationResponse, taskCompleteRequest);
        return taskCompleteRequest.getStoredTaskInstance();
    }

    private void markVerification(ActionVerificationResponse response, TaskCompleteRequest data) {
        taskTransitionRepository.save(
                TaskInstanceTransformationUtils.toVerificationTransition(response, data)
        );
        data.getStoredTaskInstance().setCurState(Boolean.TRUE.equals(response.getVerified()) ? LegionTaskStateMachineState.VERIFICATION_SUCCESS :
                LegionTaskStateMachineState.VERIFICATION_FAILED);
        data.setCompletedBy(data.getCompletedBy());
    }

    public void validate(EntityType entityType, List<VerificationConfig> verificationConfigs) {
        verificationConfigs.forEach(verificationConfig ->
                verifiers.get(verificationConfig.getVerifierName()).validate(entityType, verificationConfig)
        );
    }

    public void validateTaskCreation(CreateTaskInstanceRequest instanceRequest, TaskActionInstance actionInstance) {
        actionInstance.getVerificationStrategy().getVerifiers().forEach(verificationConfig ->
                verifiers.get(verificationConfig.getVerifierName()).validateTaskCreation(instanceRequest, actionInstance));
    }
}
