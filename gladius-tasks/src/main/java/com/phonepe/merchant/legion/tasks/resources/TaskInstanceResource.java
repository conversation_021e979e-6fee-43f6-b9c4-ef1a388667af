package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByTaskTypeRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskHistoryRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskRecreationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceExistResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceHistoryResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.TaskVerifyRmqMessage;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskCompletionByTypeResolver;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskCompletionRequestResolver;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskDeletionRequestResolver;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceHistoryService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskRecreationService;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.resolver.ConstantTenantTypeResolver;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import killswitch.enums.OperationType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/v1/task/instance")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Task Instance Related APIs")
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
public final class TaskInstanceResource {

    private final TaskInstanceManagementService taskInstanceManagementService;
    private final TaskInstanceHistoryService taskInstanceHistoryService;
    private final TaskManagementService taskManagementService;
    private final TaskRecreationService taskRecreationService;


    @Inject
    public TaskInstanceResource(TaskInstanceManagementService taskInstanceManagementService,
                                TaskManagementService taskManagementService,
                                TaskInstanceHistoryService taskInstanceHistoryService,
                                TaskRecreationService taskRecreationService) {
        this.taskInstanceManagementService = taskInstanceManagementService;
        this.taskManagementService = taskManagementService;
        this.taskInstanceHistoryService = taskInstanceHistoryService;
        this.taskRecreationService = taskRecreationService;
    }

    @POST
    @Path("/command")
    @Timed
    @Operation(summary = "Task Instance Command Execute")
    @Authorize(value = "command")
	@RolesAllowed(value = "command")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public GenericResponse<TaskInstance> command(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                 @NotNull @Valid TaskCommandRequest request,
                                                 @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                 @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                 @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile
    ){
    StoredTaskInstance command = taskManagementService.command(request,
            AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal));

    return GenericResponse.<TaskInstance>builder()
                .success(command != null)
                .data(TaskInstanceTransformationUtils.toTaskInstance(command))
                .build();
    }

    @POST
    @Path("/internal/command")
    @Timed
    @Operation(summary = "Task Instance Command Execute", hidden = true)
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<TaskInstance> internalCommand(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @Valid TaskCommandRequest request) {

        StoredTaskInstance command = taskManagementService.command(request, "housekeeping-ep");
        return GenericResponse.<TaskInstance>builder()
                .success(command != null)
                .data(TaskInstanceTransformationUtils.toTaskInstance(command))
                .build();
    }

    @POST
    @Path("/task/delete")
    @Timed
    @AccessAllowed(
            permissions = {"delete_task"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskDeletionRequestResolver.class)
    @Operation(summary = "Move task to deleted state")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
    @RolesAllowed(value = "delete_task") // check with sid
	public  GenericResponse<TaskInstance> deleteTask(@NotNull @Valid ClientTaskDeleteRequest request,
                                                     @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                     @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile
    ) {
        request.setDeletedBy(AuthUserDetails.getLegionUserId(agentProfile, null, userPrincipal));
        StoredTaskInstance storedTaskInstance = taskManagementService.deleteTask(request);
        return GenericResponse.<TaskInstance>builder()
                .success(storedTaskInstance != null)
                .data(TaskInstanceTransformationUtils.toTaskInstance(storedTaskInstance))
                .build();
    }

    @POST
    @Path("/task/expire")
    @Timed
    @Authorize(value = "expire_task")
    @RolesAllowed(value = "expire_task")
    @Operation(summary = "Move task to expired state")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public  GenericResponse<TaskInstance> expireTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @Valid TaskExpireRequest request,
                                                     @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                     @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                     @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile
    ) {
        request.setMarkedBy(AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal));
        StoredTaskInstance storedTaskInstance = taskManagementService.expireTask(request);
        return GenericResponse.<TaskInstance>builder()
                .success(storedTaskInstance != null)
                .data(TaskInstanceTransformationUtils.toTaskInstance(storedTaskInstance))
                .build();
    }

    @POST
    @Path("/task/verify")
    @Timed
    @RolesAllowed("manual_verify_task")
    @Operation(summary = "Move task to expired state")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public  GenericResponse<TaskInstance> verifyTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @Valid TaskManualVerificationRequest request,
                                                     @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                     @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                     @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile
    ) {
        request.setVerifiedBy(AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal));
        StoredTaskInstance storedTaskInstance = taskManagementService.verifyTask(request);
        return GenericResponse.<TaskInstance>builder()
                .success(storedTaskInstance != null)
                .data(TaskInstanceTransformationUtils.toTaskInstance(storedTaskInstance))
                .build();
    }

    @POST
    @Path("/verify/execute")
    @Timed
    @Operation(summary = "Task Instance Verification Execute")
    @RolesAllowed(value = "verify_task_execute")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Void> verify(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                        @NotNull @Valid TaskVerifyRmqMessage taskVerifyRmqMessage) {

        TaskActionMessagePublisher.taskVerificationRequest(taskVerifyRmqMessage);
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @GET
    @Path("/{taskInstanceId}")
    @Timed
    @Operation(summary = "Fetch Task Instance")
    @RolesAllowed(value = "taskDetails")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskInstance> get(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                             @NotNull @PathParam("taskInstanceId") String taskInstanceId) {
        TaskInstance response = taskInstanceManagementService.getById(
                TaskByIdRequest.builder()
                        .taskInstanceId(taskInstanceId)
                        .build()
        );
        return GenericResponse.<TaskInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }

    @GET
    @Path("/{taskInstanceId}/history")
    @Timed
    @Operation(summary = "Fetch Task Instance History")
    @RolesAllowed(value = "taskInstanceHistory")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<TaskInstanceHistoryResponse> getHistory(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                                   @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                                   @BeanParam TaskHistoryRequest taskHistoryRequest) {
        TaskInstanceHistoryResponse response = taskInstanceHistoryService.getHistoryById(taskHistoryRequest);
        return GenericResponse.<TaskInstanceHistoryResponse>builder()
                .success(response != null)
                .data(response)
                .build();
    }


    @PUT
    @Path("/{taskInstanceId}/update")
    @Timed
    @Operation(summary = "Update task in ES")
    @RolesAllowed(value = "update_task")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Void> update(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                        @NotNull @PathParam("taskInstanceId") String taskInstanceId,
                                        @NotNull @Valid TaskInstance request) {
        taskInstanceManagementService.update(
                taskInstanceId,
                TaskInstanceTransformationUtils.update(request),
                false
        );
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }


    @PUT
    @Path("/{taskInstanceId}/delete")
    @Timed
    @Operation(summary = "Mark task as inactive")
    @RolesAllowed(value = "delete_task")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<Void> delete(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                        @NotNull @PathParam("taskInstanceId") String taskInstanceId) {
        taskInstanceManagementService.delete(taskInstanceId);
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @POST
    @Path("/recreate")
    @Timed
    @Operation(summary = "Task Recreation On Failure")
    @RolesAllowed(value = "recreate_task")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
	public GenericResponse<TaskInstance> taskRecreate(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @NotNull @Valid TaskRecreationRequest taskRecreationRequest) {
        StoredTaskInstance storedTaskInstance = taskRecreationService.recreateTask(taskRecreationRequest);
        return GenericResponse.<TaskInstance>builder()
                .success(storedTaskInstance != null)
                .build();
    }

    @POST
    @Path("/reschedule")
    @Timed
    @Authorize(value = "command")
    @RolesAllowed(value = "command")
    @Operation(summary = "reschedule a task or cancel it" )
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public GenericResponse<Void> rescheduleTask(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Valid @NotNull TaskSchedulingPayload taskSchedulingPayload,
                                                @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
	                                            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile){
        String userId = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        return GenericResponse.<Void>builder()
                .success(taskInstanceManagementService.rescheduleTask(taskSchedulingPayload, userId))
                .build();
    }

    @PUT
    @Path("/meta")
    @Timed
    @Operation(summary = "Update Task Instance Meta")
    @Authorize(value = "updateTaskMeta")
    @RolesAllowed(value = "updateTaskMeta")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
    public GenericResponse<Void> updateMeta(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                            @NotNull @Valid TaskMetaUpdateRequest request,
                                            @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        request.setCreatedBy(actor);
        taskInstanceManagementService.updateTaskInstanceMeta(request);
        if (StringUtils.isNotEmpty(request.getCommentContent())) {
            taskInstanceManagementService.createCommentOnTask(actor, request.getTaskInstanceId(), request.getCommentContent());
        }
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @PUT
    @Path("/forceCompleteTask")
    @Timed
    @Operation(summary = "Mark task as complete")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @RolesAllowed(value = "forceTaskCompletion") // check with sid
    @LegionGateKeeper
    @AccessAllowed(
            permissions = {"forceTaskCompletion"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskCompletionRequestResolver.class)
    public GenericResponse<Void> markTaskAsComplete(@NotNull @Valid TaskCompletionByIdRequest request,
                                                    @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                    @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        request.setCompletedBy(AuthUserDetails.getLegionUserId(agentProfile, null, userPrincipal));
        taskInstanceManagementService.completeTaskById(request);
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @PUT
    @Path("/forceCompleteAllTasks")
    @Timed
    @Operation(summary = "Mark task as complete")
    @ExceptionMetered
    @RolesAllowed(value = "forceTaskCompletion") // check with sid
    @AccessAllowed(
            permissions = {"forceTaskCompletion"},
            tenantTypeResolver = ConstantTenantTypeResolver.class,
            tenantTypeResolverParam = com.phonepe.olympus.im.models.authz.enums.TenantType.Constants.COMPONENT_INSTANCE_GROUP,
            overrideAccessResolver = TaskCompletionByTypeResolver.class)
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
    public GenericResponse<Void> completeAllTasksByTaskType(@NotNull @Valid TaskCompletionByTaskTypeRequest request,
                                                            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        taskInstanceManagementService.completeAllTasksByType(request);
        return GenericResponse.<Void>builder()
                .success(true)
                .build();
    }

    @GET
    @Path("/exists")
    @Timed
    @Operation(summary = "Check if task exist for same definition Id and entity Id")
    @Authorize(value = "isTaskPresent")
    @RolesAllowed(value = "isTaskPresent")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<TaskInstanceExistResponse> taskInstanceExists(@Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                         @NotNull @QueryParam("taskDefinitionId") String taskDefinitionId,
                                                                         @NotNull @QueryParam("entityId") String entityId,
                                                                         @NotNull @QueryParam("campaignId") String campaignId
    ) {
        TaskInstanceExistResponse exists = taskInstanceManagementService.taskInstanceExists(entityId, taskDefinitionId, campaignId);
        return GenericResponse.<TaskInstanceExistResponse>builder()
                .success(true)
                .data(exists)
                .build();
    }

}