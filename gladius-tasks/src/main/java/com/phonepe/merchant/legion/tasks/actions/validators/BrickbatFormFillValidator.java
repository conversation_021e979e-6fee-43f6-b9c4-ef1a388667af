package com.phonepe.merchant.legion.tasks.actions.validators;

import com.flipkart.foxtrot.common.query.Filter;
import com.flipkart.foxtrot.common.query.general.EqualsFilter;
import com.flipkart.foxtrot.common.query.general.InFilter;
import com.flipkart.foxtrot.common.query.numeric.LessEqualFilter;
import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.core.FoxtrotRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.BrickbatFormFillValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ValidatorConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.BrickbatService;
import com.phonepe.merchant.legion.core.services.FoxtrotService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionValidatorMarker;
import com.phonepe.models.merchants.tasks.EntityType;
import edu.emory.mathcs.backport.java.util.Arrays;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_FORM_FILL_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_TABLE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EVENT_TYPE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.FOXTROT_TIME_KEY;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.FEEDBACK_NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.VALIDATION_DOWNSTREAM_SERVICE_FAILURE;

@ActionValidatorMarker(name = BRICKBAT_FORM_FILL_VALIDATOR)
public class BrickbatFormFillValidator<T extends LocationCommandRequest> implements ActionValidator<T> {

    private final BrickbatService brickbatService;
    private final FoxtrotService foxtrotService;

    private static final String NAMESPACE = "LEGION";
    private static final String BRICKBAT_ENTITY_TYPE = "SURVEY";

    private static final List<Object> EVENT_LIST = Arrays.asList(new String[]{"SURVEY_SUBMITTED", "CREATE_FEEDBACK"});
    private static final String TASK_INSTANCE_ID_FIELD = "eventData.userId";

    @Inject
    public BrickbatFormFillValidator(BrickbatService brickbatService,
                                     FoxtrotService foxtrotService) {
        this.brickbatService = brickbatService;
        this.foxtrotService = foxtrotService;
    }

    @Override
    public void validate(EntityType entityType, ValidatorConfig validatorConfig) {
        // This validator does not depend on entity type
    }

    @Override
    public ValidatorResponse validate(T taskCommandRequest, ValidatorConfig validatorConfig) {
        BrickbatFormFillValidatorConfig brickbatFormFillValidatorConfig = (BrickbatFormFillValidatorConfig) validatorConfig;

        ValidatorResponse validatorResponse = ValidatorResponse.builder()
                .validated(true)
                .build();
        if (brickbatFormFillValidatorConfig.getCampaignId() != null) {
            String feedbackId = String.join("::", NAMESPACE, BRICKBAT_ENTITY_TYPE,
                    brickbatFormFillValidatorConfig.getCampaignId(),
                    taskCommandRequest.getTaskInstanceId());

            try {
                brickbatService.getSurveyResult(feedbackId);
            } catch (LegionException e) {
                validatorResponse.setValidated(false);
                if (e.getErrorCode() == FEEDBACK_NOT_FOUND) {
                    validatorResponse.setErrorCode(e.getErrorCode());
                    validatorResponse.setErrorMessage("The step associated with filling the form has not been completed");
                } else {
                    validatorResponse.setErrorCode(VALIDATION_DOWNSTREAM_SERVICE_FAILURE);
                }
            }
        } else {
            ArrayList<Filter> filters = new ArrayList<>();
            filters.add(EqualsFilter.builder()
                    .field(TASK_INSTANCE_ID_FIELD)
                    .value(taskCommandRequest.getStoredTaskInstance().getTaskInstanceId())
                    .build());
            filters.add(InFilter.builder()
                    .field(EVENT_TYPE)
                    .values(EVENT_LIST)
                    .build());
            filters.add(LessEqualFilter.builder()
                    .field(FOXTROT_TIME_KEY)
                    .value(Calendar.getInstance().getTimeInMillis())
                    .temporal(true)
                    .build());
            FoxtrotRequest foxtrotRequest = FoxtrotRequest.builder()
                    .table(BRICKBAT_TABLE)
                    .filters(filters)
                    .build();

            if (foxtrotService.getFoxtrotEventCount(foxtrotRequest) == 0) {
                validatorResponse.setValidated(false);
                validatorResponse.setErrorCode(FEEDBACK_NOT_FOUND);
                validatorResponse.setErrorMessage("The step associated with filling the form has not been completed");
            }
        }

        return validatorResponse;
    }

}
