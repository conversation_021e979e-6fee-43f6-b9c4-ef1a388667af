package com.phonepe.merchant.legion.tasks.entitystore;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.populateEntitySourceMetaMapping;

@Slf4j
@Singleton
public class EntityStore {

  private final EntityDataFetchVisitor entityDataFetchVisitor;
  private final EntityMetaListFetchVisitor entityMetaListFetchVisitor;

  @Inject
  public EntityStore(AppConfig appConfig,
                     EntityDataFetchVisitor entityDataFetchVisitor,
                     EntityMetaListFetchVisitor entityMetaListFetchVisitor) {
    this.entityDataFetchVisitor = entityDataFetchVisitor;
    this.entityMetaListFetchVisitor = entityMetaListFetchVisitor;
    populateEntitySourceMetaMapping(appConfig.getEntitySourceMetaMapping());

  }

  @MonitoredFunction(className = "EntityStore", method = "getById")
  public Optional<Entity> getById(EntityStoreRequest esRequest) {
    return Optional.ofNullable(esRequest.getEntityType().accept(entityDataFetchVisitor,esRequest.getReferenceId()));
  }

  @MonitoredFunction(className = "EntityStore", method = "getEntityMetaList")
  public Map<String, EntityMeta> getEntityMetaMap(Map<EntityType,Set<String>> entityTypeListMap, Map<EntityType,Set<String>> taskInsatnceIdMap) {
    Map<String, EntityMeta> entityIdToMetaMapping = new HashMap<>();
    for (EntityType entityType: EnumSet.allOf(EntityType.class)) {
      List<EntityMeta> entityMetaList = null;
      if (entityTypeListMap.containsKey(entityType)) {
        if(EntityType.PHONE_NUMBER.equals(entityType)) {
          entityMetaList = getEntityMeta(entityType, taskInsatnceIdMap.get(entityType));
        } else {
          entityMetaList = getEntityMeta(entityType, entityTypeListMap.get(entityType));
        }
        if (entityMetaList != null) {
          entityMetaList.forEach(entityMeta -> entityIdToMetaMapping.put(entityMeta.getEntityId(),entityMeta));
        }
      }
    }
    return entityIdToMetaMapping;
  }

  @MonitoredFunction
  public List<EntityMeta> getEntityMeta(EntityType entityType, Set<String> entityIds) {
    return entityType.accept(entityMetaListFetchVisitor,entityIds);
  }

}
