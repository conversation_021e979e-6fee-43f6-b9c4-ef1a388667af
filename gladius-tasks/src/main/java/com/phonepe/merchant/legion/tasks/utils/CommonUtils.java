package com.phonepe.merchant.legion.tasks.utils;

import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.number.GreaterEqualsNumberFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.LesserEqualsNumberFilter;
import com.phonepe.merchant.legion.core.utils.DateUtils;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import lombok.experimental.UtilityClass;
import org.joda.time.LocalDate;

import java.util.List;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.RESCHEDULED_AT;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.DDP;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.DDP_BM;
import static com.phonepe.merchant.legion.models.profile.enums.AgentType.DDP_FOS;

@UtilityClass
public class CommonUtils {

    public static final List<AgentType> DDP_SECTOR_AGENT_ROLES = List.of(DDP_BM, DDP, DDP_FOS);

    public static Long calculateMinDate() {
        return LocalDate.now().minusMonths(2).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis();
    }

    public static Long calculateMaxDate() {
        return LocalDate.now().plusDays(1).toDateTimeAtStartOfDay().getMillis() - 1;
    }

    public static void enrichRequestWithNeedScheduledTaskFilters(List<Filter> filters, Long startDate, Long endDate) {
        long date = startDate != null ? startDate : DateUtils.getStartOfDayTimeInEpochInMS();
        filters.add(new GreaterEqualsNumberFilter(RESCHEDULED_AT, date));
        if (endDate != null) {
            filters.add(new LesserEqualsNumberFilter(RESCHEDULED_AT, endDate));
        }
    }

}
