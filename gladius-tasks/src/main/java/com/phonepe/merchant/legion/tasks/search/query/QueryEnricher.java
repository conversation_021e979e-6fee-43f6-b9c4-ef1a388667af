package com.phonepe.merchant.legion.tasks.search.query;

import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.predicate.ORFilter;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.search.query.listing.RequestTaskListingQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.query.mapview.SectorMapViewTaskSearchRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.utils.ProfileUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.ArrayList;
import java.util.List;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.AVAILABLE;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getDueDateFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getStartDateFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskStatusFilter;

@Slf4j
public class QueryEnricher {

    private final List<LegionTaskStateMachineState> assignedStates;
    private final LegionService legionService;
    private final RequestTaskListingQueryBuilderFactory requestTaskListingQueryBuilderFactory;
    private final SectorMapViewTaskSearchRequestQueryBuilderFactory sectorMapViewTaskSearchRequestQueryBuilderFactory;


    @Inject
    public QueryEnricher(LegionService legionService,
                         RequestTaskListingQueryBuilderFactory requestTaskListingQueryBuilderFactory,
                         SectorMapViewTaskSearchRequestQueryBuilderFactory sectorMapViewTaskSearchRequestQueryBuilderFactory) {
        assignedStates = LegionTaskStateMachineState.getAssignedStates();
        this.legionService = legionService;
        this.requestTaskListingQueryBuilderFactory = requestTaskListingQueryBuilderFactory;
        this.sectorMapViewTaskSearchRequestQueryBuilderFactory = sectorMapViewTaskSearchRequestQueryBuilderFactory;
    }


    private void enrichAssignedRequest(String actor, List<Filter> filterList) {

        //error thrown is 500, should be 400?
        Preconditions.checkNotNull(actor, "assignedTo cannot be null in assigned view");

        //fetch tasks assigned to or completed by a particular agent
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, actor));
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.COMPLETED_BY, actor));
        ORFilter orFilter = new ORFilter(filters);
        filterList.add(orFilter);

        filterList.add(getTaskStatusFilter(assignedStates));
    }

    public BoolQueryBuilder buildAgentTaskListingQuery(String actor, TaskListingRequest request) {
        return requestTaskListingQueryBuilderFactory.buildQuery(actor, request);
    }

    public BoolQueryBuilder buildEntityTaskListingQuery(EntityTaskListingRequest request) {
        return requestTaskListingQueryBuilderFactory.buildEntityTaskListingQuery(request);
    }

    public BoolQueryBuilder buildTaskTypeFilterQuery(EntityTaskListingRequest request, BoolQueryBuilder builder){
        return requestTaskListingQueryBuilderFactory.buildEntityTaskListingQuery(request, builder);
    }
    public QueryBuilder taskListFilterQueryBuilder(String actor, SectorMapViewTaskSearchRequest searchRequest) {
        return sectorMapViewTaskSearchRequestQueryBuilderFactory.buildQuery(actor, searchRequest);
    }

    public BoolQueryBuilder enrichEntityStatsFilter(String actor, String entityId, List<Filter> filters) {
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_ID, entityId));
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_STATE, AVAILABLE.getText()));

        filters.add(getDueDateFilter());
        filters.add(getStartDateFilter());

        AgentProfile userProfile = legionService.getAgentProfile(actor);
        BoolQueryBuilder boolQueryBuilder = TaskEsUtils.getTagFilter(ProfileUtils.tagEnricher(userProfile));
        BoolQueryBuilder roleBasedQuery = TaskEsUtils.getRoleFilter(userProfile.getAgentType().toString());
        BoolQueryBuilder filterQuery = getBoolQuery(filters);
        filterQuery.must(boolQueryBuilder);
        filterQuery.must(roleBasedQuery);
        return filterQuery;
    }

    public void enrichUserStatsFilter(TaskStatsRequest request) {
        request.getFilters().add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        enrichAssignedRequest(request.getAssignedTo(), request.getFilters());
    }

    public BoolQueryBuilder buildAgentsEligibleForTaskQuery(List<String> agentIds, String taskInstanceId) {
        return requestTaskListingQueryBuilderFactory.buildAgentTaskEligibilityQuery(agentIds, taskInstanceId);
    }

}
