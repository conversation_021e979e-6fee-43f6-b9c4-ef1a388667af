package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.CreateCommentRequest;
import com.phonepe.merchant.gladius.models.tasks.response.CommentsOnTaskResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.repository.CommentsOnTaskRepository;
import com.phonepe.merchant.legion.tasks.services.CommentsService;
import com.phonepe.merchant.legion.tasks.utils.CommentsOnTaskTransformationUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class CommentsServiceImpl implements CommentsService {

    private final LegionService legionService;
    private final FoxtrotEventExecutor eventExecutor;
    private final CommentsOnTaskRepository commentsOnTaskRepository;

    @Override
    public StoredCommentsOnTask createComment(CreateCommentRequest createCommentRequest, String actor) {
        StoredCommentsOnTask storedCommentsOnTask = null;
        try {
            String commentId = commentsOnTaskRepository.generateCommentId(createCommentRequest.getTaskInstanceId());
            storedCommentsOnTask = commentsOnTaskRepository.save(CommentsOnTaskTransformationUtils.toStoredCommentsOnTask(createCommentRequest, commentId, actor));
            eventExecutor.ingest(TaskFoxtrotEventUtils.toCommentOnTaskEvent(storedCommentsOnTask));
        } catch (Exception e) {
            log.error("Exception while comment creation: {}", createCommentRequest.toString(), e);
            throw e;
        }
        return storedCommentsOnTask;
    }

    @Override
    public List<CommentsOnTaskResponse> getByTaskInstanceId(String taskInstanceId, Sorter sorter, int start, int count) {
        try {
            List<StoredCommentsOnTask> storedCommentsOnTask = commentsOnTaskRepository.getFromTaskInstanceId(taskInstanceId, Sorter.CREATED_AT, start, count);
            if (CollectionUtils.isEmpty(storedCommentsOnTask)) {
                return new ArrayList<>();
            }
            Map<String, AgentProfile> agentProfileMap = getAgentProfileMap(storedCommentsOnTask.stream().map(StoredCommentsOnTask::getCreatedBy).collect(Collectors.toSet()));
            return storedCommentsOnTask.stream().map(
                    commentsOnTask -> {
                        AgentProfile agentProfile = agentProfileMap.get(commentsOnTask.getCreatedBy());
                        return CommentsOnTaskTransformationUtils.toCommentsOnTaskResponse(commentsOnTask, agentProfile);
                    }
            ).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception while getting comments for: {}", taskInstanceId, e);
            throw e;
        }
    }

    private Map<String, AgentProfile> getAgentProfileMap(Set<String> agentIds) {
        return agentIds.stream().collect(Collectors.toMap(
                agentId -> agentId,
                legionService::getAgentProfile
        ));
    }
}
