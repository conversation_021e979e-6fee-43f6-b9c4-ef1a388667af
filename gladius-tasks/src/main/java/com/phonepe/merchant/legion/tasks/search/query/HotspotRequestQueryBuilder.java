package com.phonepe.merchant.legion.tasks.search.query;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.repository.HotspotRepository;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;


import java.util.Objects;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.HOTSPOT_CONFIG_NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.HOTSPOT_NOT_FOUND;

@Singleton
@Slf4j
@SuppressWarnings("deprecation")
public class HotspotRequestQueryBuilder {
     private final HotspotRepository hotspotRepository;
     private final FilterCraftBuilderCache filterCraftBuilderCache;

     @Inject
    public HotspotRequestQueryBuilder(HotspotRepository hotspotRepository,
                                      FilterCraftBuilderCache filterCraftBuilderCache) {
        this.hotspotRepository = hotspotRepository;
        this.filterCraftBuilderCache = filterCraftBuilderCache;
    }

    public BoolQueryBuilder buildQuery(String actorId, String hotspotId) {
        Optional<StoredHotspot> hotspot = hotspotRepository.get(hotspotId);
        return getQuery(hotspot.orElse(null), actorId);
    }

    public BoolQueryBuilder buildQuery(String actorId, StoredHotspot hotspot) {
        return getQuery(hotspot, actorId);
    }

    @MonitoredFunction
    private BoolQueryBuilder getQuery(StoredHotspot hotspot, String actorId) {
         log.debug("Actor Id", actorId);
        if (Objects.isNull(hotspot)) {
            throw LegionException.error(HOTSPOT_NOT_FOUND);
        }
        if (Objects.isNull(hotspot.getFilterCraftConfig())) {
            throw LegionException.error(HOTSPOT_CONFIG_NOT_FOUND);
        }
        BoolQueryBuilder queryBuilder = filterCraftBuilderCache.get(FilterCraftBuilderCacheKey.builder()
                .config(hotspot.getFilterCraftConfig()).build())
                .convertToESFilter();
        queryBuilder.must(TaskEsUtils.geoPolygonQuery(hotspot.getRegion().getCoordinates()));
        log.debug("Hotspot Query: {}", queryBuilder);
        return queryBuilder;
    }

}
