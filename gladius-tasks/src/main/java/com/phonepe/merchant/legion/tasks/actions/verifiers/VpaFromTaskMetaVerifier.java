package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.EntityType;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.VPA_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_COMP_VPA_MAPPED;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;

@ActionVerifierMarker(name = VPA_VERIFIER)
public class VpaFromTaskMetaVerifier extends ActionVerifier {

    private final IntelService intelService;
    private static final String VPA_REGEX = "^[a-zA-Z0-9._-]{2,256}@[a-zA-Z0-9.-]{2,64}$";

    @Inject
    public VpaFromTaskMetaVerifier(IntelService intelService) {
        this.intelService = intelService;
    }

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.PHONE_NUMBER) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        return VerifierResponse.builder()
                .verified(true)
                .context(context)
                .build();
    }

    @Override
    public boolean validateTaskCreation(CreateTaskInstanceRequest instanceRequest, TaskActionInstance actionInstance) {
        List<String> compVpaList = getMetaValue(instanceRequest);
        if (compVpaList.isEmpty()) {
            return false; //nothing to validate here
        }
        List<String> invalidVpas = new ArrayList<>();
        for (String vpa : compVpaList) {
            if (vpa == null || !vpa.matches(VPA_REGEX)) {
                invalidVpas.add(String.valueOf(vpa));
                continue;
            }
            try {
                intelService.getVpaDetails(vpa);
            } catch (Exception ex) {
                invalidVpas.add(vpa);
            }
        }
        if (!invalidVpas.isEmpty()) {
            throw LegionException.error(
                    INVALID_COMP_VPA_MAPPED,
                    Map.of(MESSAGE, "Invalid comp-vpa mapped: " + String.join(", ", invalidVpas) + ". Lead cannot be created.")
            );
        }

        CompetitionQrResponse vpaDetails = intelService.getVpaDetails(compVpaList.get(0));
        //TO-DO: Remove location set from validation
        instanceRequest.setTransactionLocation(EsLocationRequest.builder()
                .lat((double) vpaDetails.getLatitude())
                .lon((double) vpaDetails.getLongitude())
                .build()
        );
        return true;
    }

    private List<String> getMetaValue(CreateTaskInstanceRequest request) {
        if (request.getTaskInstanceMeta() == null || request.getTaskInstanceMeta().getTaskMetaList() == null) {
            return Collections.emptyList();
        }
        Object value = request.getTaskInstanceMeta().getMetaValueObject(TaskMetaType.COMP_VPA);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return Collections.emptyList();
    }
}
