package com.phonepe.merchant.legion.tasks.actions.processor;

import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationStrategy;
import com.phonepe.models.merchants.tasks.EntityType;

public interface ActionValidationProcessor<T extends LocationCommandRequest> {

    void validate(T taskCommandRequest);

    void validate(EntityType entityType, ValidationStrategy validationStrategy);
}
