package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.legion.core.cache.AsyncCache;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import io.dropwizard.lifecycle.Managed;
import lombok.extern.slf4j.Slf4j;
import ru.vyarus.dropwizard.guice.module.installer.feature.eager.EagerSingleton;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.UNKNOWN;

@Slf4j
@EagerSingleton
public class TaskDefinitionIdsByTypeCache extends AsyncCache<String, Map<String, List<TaskDefinitionInstance>>> implements Managed {
    private static final String DEFAULT_KEY = "ALL_TASK_DEFINITION";
    @Inject
    public TaskDefinitionIdsByTypeCache(Map<CacheName, CacheConfig> cacheConfigs, Provider<TaskDefinitionRepository> taskDefinitionRepositoryProvider, MetricRegistry metricRegistry,
                                        CacheUtils cacheUtils) {
        super(CacheName.TASK_DEFINITION_TYPE_MAPPING, cacheConfigs.get(CacheName.TASK_DEFINITION_TYPE_MAPPING), key -> {
            try {
                List<StoredTaskDefinition> taskDefinitions = taskDefinitionRepositoryProvider.get().getAll();
                Map<String , List<TaskDefinitionInstance>> map = new HashMap<>();
                taskDefinitions.stream()
                        .filter(storedTaskDefinition -> storedTaskDefinition.getDefinitionAttributes() != null)
                        .filter(storedTaskDefinition -> {
                            TaskDefinitionAttributes context = SerDe.readValue(storedTaskDefinition.getDefinitionAttributes(), new TypeReference<TaskDefinitionAttributes>() {});
                            return context != null && context.getLeadConfig() != null &&
                                    (context.getLeadConfig().getLeadCreation() != null || context.getLeadConfig().getLeadUpdation() != null);
                        })
                        .map(storedTaskDefinition -> new AbstractMap.SimpleEntry<>(
                                SerDe.readValue(storedTaskDefinition.getDefinitionAttributes(), new TypeReference<TaskDefinitionAttributes>() {}),
                                storedTaskDefinition
                        ))
                        .forEach(entry -> {
                            String taskType = entry.getKey().getTaskType();
                            if (taskType == null) {
                                taskType = UNKNOWN;
                            }
                            map.computeIfAbsent(taskType, k -> new ArrayList<>())
                            .add(TaskDefinitionInstance.builder()
                                    .taskDefinitionId(entry.getValue().getTaskDefinitionId())
                                    .definitionAttributes(SerDe.readValue(entry.getValue().getDefinitionAttributes(),
                                            new TypeReference<TaskDefinitionAttributes>() {}))
                                    .build());
                                }
                        );

                return map;
            } catch (Exception e) {
                log.warn("Error while loading StoredTaskAction for actionId: {}", key);
                throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
            }
        }, metricRegistry);
        cacheUtils.registerCache(CacheName.TASK_DEFINITION_TYPE_MAPPING, this);
    }

    @Override
    public void start() throws Exception {
        get(DEFAULT_KEY);
    }

    @Override
    public void stop() throws Exception {
        /**
         * This is empty, no need to do anything in stop
         */
    }
}