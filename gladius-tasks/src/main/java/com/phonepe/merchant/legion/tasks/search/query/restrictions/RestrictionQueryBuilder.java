package com.phonepe.merchant.legion.tasks.search.query.restrictions;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.legion.core.config.DiscoveryViewRestrictionConfig;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.GeneralPurposeCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.models.DiscoveryRestrictionContext;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.models.RestrictionConfig;
import com.phonepe.merchant.legion.tasks.search.query.restrictions.models.RestrictionsConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.google.common.base.Preconditions.checkArgument;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class RestrictionQueryBuilder {
    private final ChimeraLiteRepository chimeraLiteRepository;
    private final FilterCraftBuilderCache filterCraftBuilderCache;
    private final GeneralPurposeCache generalPurposeCache;
    private final DiscoveryViewRestrictionConfig discoveryViewRestrictionConfig;

    public List<BoolQueryBuilder> getRestrictionQueries(DiscoveryRestrictionContext context, String treatmentGroup) {
        List<RestrictionsConfig> restrictionsConfigList = (List<RestrictionsConfig>)generalPurposeCache
                .get(discoveryViewRestrictionConfig.getChimeraKey(), k -> SerDe.readValue(chimeraLiteRepository.getStringValuedChimeraConfig(discoveryViewRestrictionConfig.getChimeraKey()), new TypeReference<List<RestrictionsConfig>>() {}));
        if (Objects.isNull(restrictionsConfigList)) {
            return List.of();
        }
        Optional<RestrictionsConfig> restrictionsConfigOptional = restrictionsConfigList.stream()
                .filter(config -> treatmentGroup.equals(config.getTreatmentGroup())).findFirst();
        if (restrictionsConfigOptional.isEmpty()) {
            return List.of();
        }
        RestrictionsConfig  restrictionsConfig = restrictionsConfigOptional.get();
        if (!restrictionsConfig.isRestrictionsEnabled() ||Objects.isNull(restrictionsConfig.getRestrictions()) || restrictionsConfig.getRestrictions().isEmpty()) {
            return List.of();
        }
        return restrictionsConfig.getRestrictions().stream()
                .map(restrictionConfigs -> {
                    checkArgument(!restrictionConfigs.isEmpty());
                    List<BoolQueryBuilder> queries = getBoolQueryBuilderStream(context, restrictionConfigs);
                    if (queries.isEmpty()) {
                      return null;
                    } else if (queries.size() == 1) {
                        return queries.get(0);
                    } else {
                        BoolQueryBuilder query = new BoolQueryBuilder();
                        queries.forEach(query::should);
                        return query;
                    }
                })
                .filter(Objects::nonNull)
                .toList();
    }

    public List<String> getSectorProfileTags() {
        return discoveryViewRestrictionConfig.getSectorProfileTags();
    }

    private List<BoolQueryBuilder> getBoolQueryBuilderStream(DiscoveryRestrictionContext context, List<RestrictionConfig> restrictionConfigs) {
        return restrictionConfigs.stream()
                .filter(restrictionConfig -> {
                    String config = (String) generalPurposeCache.get(restrictionConfig.getConditionKey(), k ->
                            chimeraLiteRepository.getStringValuedChimeraConfig(restrictionConfig.getConditionKey()));
                    return filterCraftBuilderCache.get(FilterCraftBuilderCacheKey.builder()
                                    .config(config)
                                    .build())
                            .evaluate(context);
                })
                .map(restrictionConfig -> {
                    String config = (String) generalPurposeCache.get(restrictionConfig.getRestrictionKey(), k ->
                            chimeraLiteRepository.getStringValuedChimeraConfig(restrictionConfig.getRestrictionKey()));
                    return filterCraftBuilderCache.get(FilterCraftBuilderCacheKey.builder()
                            .config(config)
                            .context(SerDe.writeValueAsString(context))
                            .build()).convertToESFilter();
                })
                .toList();
    }
}
