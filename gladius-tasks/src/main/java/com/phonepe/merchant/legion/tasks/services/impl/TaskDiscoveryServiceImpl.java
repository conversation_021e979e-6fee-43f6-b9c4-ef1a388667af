package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.NotEqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.GreaterEqualsNumberFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.LesserEqualsNumberFilter;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.AgentTaskEligibilityRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDetailRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TaskStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TasksStatsRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.AgentTaskEligibilityResponse;
import com.phonepe.merchant.gladius.models.tasks.response.EntityStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDetailResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskListResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskStateStatsByAction;
import com.phonepe.merchant.gladius.models.tasks.response.UserStatsAndFilters;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.config.AttributeInfoConfig;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.DateUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.enums.AgentStatus;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.tasks.cache.TaskFiltersCache;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.flows.Validations;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import com.phonepe.merchant.legion.tasks.search.executors.SectorMapViewTaskSearchQueryExecutor;
import com.phonepe.merchant.legion.tasks.search.executors.TaskSearchQueryExecutor;
import com.phonepe.merchant.legion.tasks.search.query.QueryEnricher;
import com.phonepe.merchant.legion.tasks.search.query.scoring.FunctionalQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.scoring.models.PrioritiseStrategyConfig;
import com.phonepe.merchant.legion.tasks.search.query.search.BadgeConfigRequestExecutor;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskSearchRequestQueryBuilderFactory;
import com.phonepe.merchant.legion.tasks.search.response.filter.RequestViewWiseFilterFactory;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.AgentMetaBuilderUtils;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.CREATED;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ATTRIBUTES;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.CREATED_AT;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.START_DATE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_STATE;
import static com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType.ENTITY_HISTORY_VIEW;
import static com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils.getParsedStringTermsFromEsSearchResponse;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.COMPLETED_ON;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.CREATED_AT_LABEL;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.DISTANCE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.DISTANCE_UNIT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POINTS;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POINTS_UNIT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SECTOR_ID_LABEL;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.START_DUE_DATE;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.NOT_FOUND;
import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.copyEntityMeta;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.ACTIONID;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.CURRENT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MAX_PAGE_SIZE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.OBJECTIVES;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getActionTaskStatusAgg;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getDueDateFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getEntityStatsAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getEntityStatsFromAgg;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getFilterValuesFromAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getStartDateFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskActionAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskObjectiveAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskStatesFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTasksCountFromAgg;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getUpdatedAtFilter;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getUserStatsAggregation;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getUserStatsFromAgg;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.toDiscoveryTaskInstanceList;
import static com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils.discoveryTaskInstanceToTaskDetails;
import static com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils.discoveryTaskInstanceToTaskMeta;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TaskDiscoveryServiceImpl implements TaskDiscoveryService {

    private final ESRepository esRepository;
    private final EntityStore entityStore;
    private final QueryEnricher enricher;
    private final TaskActionService taskActionService;
    private final TaskDefinitionService taskDefinitionService;
    private final SectorMapViewTaskSearchQueryExecutor sectorMapViewTaskSearchQueryExecutor;
    private final TaskFiltersCache taskFiltersCache;
    private final TaskActionRepository taskActionRepository;
    private final TaskSearchQueryExecutor taskSearchQueryExecutor;
    private final Validations validations;
    private final TaskInstanceManagementService taskInstanceManagementService;
    private final TaskSearchRequestQueryBuilderFactory taskSearchRequestQueryBuilderFactory;
    private final RequestViewWiseFilterFactory requestViewWiseFilterFactory;
    private final TaskAttributeService taskAttributeService;
    private final Miscellaneous miscellaneous;
    private final FunctionalQueryBuilder functionalQueryBuilder;
    private final BadgeConfigRequestExecutor badgeConfigRequestExecutor;
    private final LegionService legionService;
    private final AtlasService atlasService;
    private final ValidationService validationService;
    private final Map<String, AttributeInfoConfig> attributeInfo;
    private static final String FILTERS_JSON_FILE = "filters.json";
    private static final String TASK_TYPE_FILTERS_DEFAULT_KEY = "DEFAULT_KEY";


    private static final String MESSAGE_KEY="message";

    private TaskSearchResponse processSearchResponse(TaskSearchRequest request, SearchResponse searchResponse) {
        TotalHits totalHits = searchResponse.getHits().getTotalHits();
        long taskCount = 0;
        if (totalHits != null) {
            taskCount = totalHits.value;
        }
        List<TaskMetaResponse> taskMetaResponses = new ArrayList<>();
        List<DiscoveryTaskInstance> taskInstances = toDiscoveryTaskInstanceList(request.getPageNo(), request.getPageSize(), request.getLocation(), searchResponse, request.getSorter());
        Map<EntityType, Set<String>> entityIdMap = new EnumMap<>(EntityType.class);
        Map<EntityType, Set<String>> taskInstanceIdMap = new EnumMap<>(EntityType.class);
        for (DiscoveryTaskInstance taskInstance : taskInstances) {
            TaskMetaResponse taskMetaResponse = discoveryTaskInstanceToTaskMeta(taskInstance,
                    taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                            .taskDefinitionId(taskInstance.getTaskDefinitionId())
                            .build()), null);

            taskMetaResponse.setSteps(null);
            setLeadIntentInTaskMeta(taskMetaResponse, taskInstance);
            enrichTaskWithAttributes(taskMetaResponse, taskInstance);
            if (request.getTaskSearchRequestType() == TaskSearchRequestType.ESCALATED_VIEW) {
                taskMetaResponse.addEscalationViewAttribute(SECTOR_ID_LABEL, atlasService.getSectorIds(taskInstance.getLocation()).get(0));
                taskMetaResponse.addEscalationViewAttribute(CREATED_AT_LABEL, DateUtils.convertEpochToDate(taskInstance.getCreatedAt(), "MMM, dd yyyy"));
            }
            if (taskInstance.getAssignedTo() != null) {
                taskMetaResponse.setAgentMeta(AgentMetaBuilderUtils.getAgentMetaFromProfile(legionService.getAgentProfile(taskInstance.getAssignedTo())));
            }
            taskMetaResponses.add(taskMetaResponse);
            taskMetaResponse.setBadges(badgeConfigRequestExecutor.findMatchingBadges(taskInstance));
            buildEntityIdMapFromEntityType(taskInstance.getEntityType(), entityIdMap, taskInstance.getEntityId(), taskInstanceIdMap, taskInstance.getTaskInstanceId());
        }
        setEntityMetaInTaskMetaResponse(taskMetaResponses, entityIdMap, taskInstanceIdMap);
        return TaskSearchResponse.builder()
                .taskCount(taskCount)
                .taskList(taskMetaResponses)
                .build();
    }

    private EntityMeta getSingleEntityMeta(EntityType entityType, String entityId) {
        if (entityType == null || isNullOrEmpty(entityId)) {
            return null;
        }
        List<EntityMeta> entityMetas = entityStore.getEntityMeta(entityType, new HashSet<>(Arrays.asList(entityId)));
        if (entityMetas != null && !entityMetas.isEmpty()) {
            return entityMetas.get(0);
        }
        return null;
    }

    @Override
    public TaskListResponse getTaskListing(TaskListRequest taskListRequest) {
        //query builder
        if (taskListRequest.getFilters() == null) {
            taskListRequest.setFilters(new ArrayList<>());
        }
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(taskListRequest.getFilters());
        // executor
        SearchResponse searchResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder,
                taskListRequest.getPageNo(), taskListRequest.getPageSize(), CREATED_AT, SortOrder.ASC);
        // response mapper
        List<DiscoveryTaskInstance> tasks = toDiscoveryTaskInstanceList(taskListRequest.getPageNo(),
                taskListRequest.getPageSize(), null, searchResponse, Sorter.NONE);
        long taskCount = getTaskCount(searchResponse);
        return TaskListResponse.builder()
                .tasks(tasks)
                .count(taskCount)
                .pageNo(taskListRequest.getPageNo())
                .pageSize(taskListRequest.getPageSize())
                .build();
    }

    @Override
    public TaskSearchResponse getAgentTaskListing(String actor, TaskListingRequest request) {
        //query builder
        try {
            log.info("Received request for getAgentTaskListing : {} , with actor : {} ", request, actor);
            BoolQueryBuilder boolQueryBuilder = enricher.buildAgentTaskListingQuery(actor, request);
            String sortField = (request.getRequestType() == ENTITY_HISTORY_VIEW) ? CREATED_AT
                    : DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS;
            // executor
            SearchResponse searchResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder,
                    request.getPageNo(), request.getPageSize(), sortField, SortOrder.DESC);
            //
            log.info("SearchResponse received for es");
            return executeAgentTaskListingQuery(request, searchResponse);
        } catch (Exception e) {
            log.info("Exception occurred while getAgentTaskListing : ", e);
            throw LegionException.error(LegionTaskErrorCode.INTERNAL_ERROR, Map.of(MESSAGE_KEY, "Some error occurred while getting agent task list"));
        }
    }

    @Override
    public TaskSearchResponse getAllEntityActiveTasks(EntityTaskListingRequest request) {
        // query builder
        BoolQueryBuilder query = enricher.buildEntityTaskListingQuery(request);
        int start = request.getPageNo();
        int limit = request.getPageSize();
        SearchResponse searchResponse = esRepository.search(TASK_INDEX, query,(start - 1) * limit, limit);
        return executeAgentTaskListingQuery(request, searchResponse);
    }

    @Override
    public TaskSearchResponse getAllActiveTasksForCompletion(EntityTaskListingRequest request, BoolQueryBuilder builder) {
        // query builder
        BoolQueryBuilder query = enricher.buildTaskTypeFilterQuery(request, builder);
        int start = request.getPageNo();
        int limit = request.getPageSize();
        SearchResponse searchResponse = esRepository.search(TASK_INDEX, query,(start - 1) * limit, limit);
        return executeAgentTaskListingQuery(request, searchResponse);
    }

    private TaskSearchResponse executeAgentTaskListingQuery(EntityTaskListingRequest request, SearchResponse searchResponse) {
        // response mapper
        List<DiscoveryTaskInstance> taskListResponse = toDiscoveryTaskInstanceList(
                request.getPageNo(), request.getPageSize(), null, searchResponse, Sorter.POINTS);

        return generateTaskSearchResponse(taskListResponse, searchResponse);
    }

    private TaskSearchResponse generateTaskSearchResponse(List<DiscoveryTaskInstance>taskListResponse, SearchResponse searchResponse) {
        long taskCount = getTaskCount(searchResponse);
        List<TaskMetaResponse> taskMetaResponses = taskListResponse.stream()
                .map(this::getTaskMetaResponse).toList();
        Map<EntityType, Set<String>> entityIdMap = new EnumMap<>(EntityType.class);
        Map<EntityType, Set<String>> taskInstanceIdMap = new EnumMap<>(EntityType.class);
        for (TaskMetaResponse metaResponse : taskMetaResponses) {
            buildEntityIdMapFromEntityType(metaResponse.getEntityType(), entityIdMap, metaResponse.getEntityId(), taskInstanceIdMap, metaResponse.getTaskInstanceId());
        }
        setEntityMetaInTaskMetaResponse(taskMetaResponses, entityIdMap, taskInstanceIdMap);
        return TaskSearchResponse.builder()
                .taskList(taskMetaResponses)
                .taskCount(taskCount)
                .build();
    }

    private void buildEntityIdMapFromEntityType(EntityType entityType, Map<EntityType, Set<String>> entityIdMap, String entityId,
                                                Map<EntityType, Set<String>> taskInsatanceIdMap, String taskInstanceId) {
        if (!entityIdMap.containsKey(entityType)) {
            Set<String> entityIds = new HashSet<>();
            entityIds.add(entityId);
            entityIdMap.put(entityType, entityIds);
            Set<String> taskInstanceIds = new HashSet<>();
            taskInstanceIds.add(taskInstanceId);
            taskInsatanceIdMap.put(entityType, taskInstanceIds);
        } else {
            entityIdMap.get(entityType).add(entityId);
            taskInsatanceIdMap.get(entityType).add(taskInstanceId);
        }
    }

    private void setEntityMetaInTaskMetaResponse(List<TaskMetaResponse> taskMetaResponses,
                                                 Map<EntityType, Set<String>> entityIdMap,
                                                 Map<EntityType, Set<String>> taskInstanceIdMap) {
        Map<String, EntityMeta> entityIdToMeta = entityStore.getEntityMetaMap(entityIdMap, taskInstanceIdMap);
        taskMetaResponses.forEach(response -> {
            EntityMeta entityMeta = copyEntityMeta(entityIdToMeta.get(response.getEntityId()));
            if (entityMeta != null && isNullOrEmpty(entityMeta.getName())) {
                entityMeta.setName(response.getDescription());
            }
            response.setEntityMeta(entityMeta);
        });
    }

    @NotNull
    public TaskMetaResponse getTaskMetaResponse(DiscoveryTaskInstance task) {
        TaskActionInstance taskActionInstance = taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                .taskActionId(task.getActionId())
                .build());
        TaskDefinitionInstance taskDefinitionInstance = taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(task.getTaskDefinitionId())
                .build());
        TaskInstance taskInstance = taskInstanceManagementService.getById(TaskByIdRequest.builder()
                .taskInstanceId(task.getTaskInstanceId())
                .build());
        TaskMetaResponse taskMetaResponse = discoveryTaskInstanceToTaskMeta(task, taskDefinitionInstance, taskInstance);
        if (task.getAssignedTo() != null) {
            taskMetaResponse.setAgentMeta(AgentMetaBuilderUtils.getAgentMetaFromProfile(legionService.getAgentProfile(task.getAssignedTo())));
        }
        enrichTaskWithAttributes(taskMetaResponse, task);
        taskMetaResponse.setBadges(badgeConfigRequestExecutor.findMatchingBadges(task));
        taskMetaResponse.setAllowLeadIntentUpdate(validationService.checkIfDefinitionIsWhitelisted(task.getTaskDefinitionId()) ?
                taskDefinitionInstance.isLeadUpdationAllowed() : getActionIdList(LeadManagementConfiguration.getLeadUpdation()).contains(taskInstance.getActionId()));
        taskMetaResponse.setTaskType(taskActionInstance.getActionId());
        return setLeadIntentInTaskMeta(taskMetaResponse, task);
    }

    private void enrichTaskWithAttributes(TaskMetaResponse taskMetaResponse, DiscoveryTaskInstance taskInstance) {

        // Add Distance attribute if available
        if (taskInstance.getDistance() != null) {
            AttributeInfoConfig distanceInfo = attributeInfo.get(DISTANCE);
            taskMetaResponse.addAttribute(DISTANCE_UNIT, distanceInfo.getIcon(), distanceInfo.getUnit(), taskInstance.getDistance());
        }
        // Add Points attribute
        AttributeInfoConfig pointInfo = attributeInfo.get(POINTS);
        taskMetaResponse.addAttribute(POINTS_UNIT, pointInfo.getIcon(), pointInfo.getUnit(), taskInstance.getPoints());

        String startDate = DateUtils.convertEpochToDate(taskInstance.getStartDate(), "dd MMM yy");
        String dueDate = DateUtils.convertEpochToDate(taskInstance.getDueDate(), "dd MMM yy");
        String startDueDate = String.format("%s - %s", startDate, dueDate);

        // Add Start Due date attribute
        AttributeInfoConfig startDueDateInfo = attributeInfo.get(START_DUE_DATE);
        taskMetaResponse.addAttribute("", startDueDateInfo.getIcon(), startDueDateInfo.getUnit(), startDueDate);

        // Add CompletedOn attribute if available
        if (taskInstance.getCompletedOn() != null) {
            String completedOn = DateUtils.convertEpochToDate(taskInstance.getCompletedOn(), "MMM dd, yyyy");
            AttributeInfoConfig completedOnInfo = attributeInfo.get(COMPLETED_ON);
            taskMetaResponse.addAttribute("", completedOnInfo.getIcon(), completedOnInfo.getUnit(), completedOn);
        }
    }

    private List<String> getActionIdList(List<ActionToRemarkConfig> leadCreation) {
        return leadCreation
                .stream()
                .map(ActionToRemarkConfig::getActionId)
                .toList();
    }

    private TaskMetaResponse setLeadIntentInTaskMeta(TaskMetaResponse taskMetaResponse, DiscoveryTaskInstance task) {
        if (null != task.getTaskInstanceMeta() && null != task.getTaskInstanceMeta().getTaskMetaList()) {
            task.getTaskInstanceMeta().getTaskMetaList().stream()
                    .filter(metaInformation -> metaInformation.getType() == TaskMetaType.LEAD_INTENT)
                    .findFirst()
                    .ifPresent(metaInformation ->
                            taskMetaResponse.setLeadIntent(metaInformation.getValue().toString()));
        }
        taskMetaResponse.setMaxAllowedDateForIntent(DateUtils.addDaysToEpochDate(
                task.getCreatedAt(),
                miscellaneous.getMaxPossibleRescheduleOffsetInDays()
        ));
        return taskMetaResponse;
    }

    private static long getTaskCount(SearchResponse searchResponse) {
        long taskCount = 0;
        TotalHits totalHits = searchResponse.getHits().getTotalHits();
        if (totalHits != null) {
            taskCount = totalHits.value;
        }
        return taskCount;
    }

    @Override
    @MonitoredFunction
    public TaskSearchResponse search(String actor, TaskSearchRequest taskSearchRequest) {
        try {
            //Logging the request
            log.info("Task search request: {} and requested by: {}", taskSearchRequest, actor);
            //query builder
            QueryBuilder query = taskSearchRequestQueryBuilderFactory.queryBuilder(actor, taskSearchRequest);
            log.info("tasklist query builder :{} ", actor);
            //executor
            SearchResponse searchResponse;
            PrioritiseStrategyConfig prioritiseStrategyConfig = functionalQueryBuilder.getPrioritiseStrategyConfig(actor);
            if (Sorter.LOCATION == taskSearchRequest.getSorter() && prioritiseStrategyConfig.isFunctionScoringEnabled() && taskSearchRequest.getTaskSearchRequestType() == TaskSearchRequestType.SECTOR_DISCOVERY_VIEW) {
                functionalQueryBuilder.ingestSortingEvent(actor, prioritiseStrategyConfig);
                searchResponse = Sorter.PRIORITY.accept(taskSearchRequest, query, taskSearchQueryExecutor);
            } else {
                searchResponse = taskSearchRequest.getSorter().accept(taskSearchRequest, query, taskSearchQueryExecutor);
            }

            //response mapping
            return processSearchResponse(taskSearchRequest, searchResponse);
        } catch (Exception e) {
            log.error("Error occurred while processing request:" + actor + " and exception: ", e);
            throw LegionException.error(LegionTaskErrorCode.INTERNAL_ERROR,
                    Map.of(MESSAGE_KEY, "Some error occurred while searching for task"));
        }
    }

    //for map view
    @Override
    public SectorTaskSearchResponse getTaskList(String userId, SectorMapViewTaskSearchRequest request) {
        try {
            validations.validateTaskSectorWithUser(userId, Collections.singletonList(request.getSectorId()));
            //query builder
            QueryBuilder query = enricher.taskListFilterQueryBuilder(userId, request);
            //executor
            SearchResponse response = request.getSorter().accept(request, query, sectorMapViewTaskSearchQueryExecutor);
            //response mapping
            long totalTaskCount = getTaskCount(response);
            List<DiscoveryTaskInstance> discoveryTaskInstanceList = Arrays.stream(response.getHits().getHits())
                    .map(hit -> SerDe.readValue(hit.getSourceAsString(), DiscoveryTaskInstance.class)).toList();
            return TaskInstanceTransformationUtils.toSectorTaskList(discoveryTaskInstanceList, totalTaskCount);
        } catch (Exception e) {
            log.error("Error occurred while processing task list request :{} ", userId, e);
            throw e;
        }
    }

    @Override
    public DiscoveryTaskInstance getById(TaskByIdRequest taskByIdRequest) {
        return esRepository.get(taskByIdRequest.getTaskInstanceId(), TASK_INDEX, DiscoveryTaskInstance.class);
    }

    @Override
    public UserStatsAndFilters getUserStats(TaskStatsRequest request) {
        if (request.getFilters() == null) {
            request.setFilters(new ArrayList<>());
        }
        enricher.enrichUserStatsFilter(request);
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(request.getFilters());
        AggregationBuilder aggregationBuilder = getUserStatsAggregation();
        Aggregations aggregations = esRepository.getAggregations(TASK_INDEX, boolQueryBuilder, aggregationBuilder, 0);
        return getUserStatsFromAgg(aggregations);
    }

    @Override
    public EntityStatsResponse getEntityStats(String actor, String entityId) {
        List<Filter> filters = new ArrayList<>();
        //query builder
        BoolQueryBuilder query = enricher.enrichEntityStatsFilter(actor, entityId, filters);
        AggregationBuilder aggregationBuilder = getEntityStatsAggregation();
        //executor
        Aggregations aggregations = esRepository.getAggregations(TASK_INDEX, query, aggregationBuilder, 0);
        // response mapper
        EntityStatsResponse entityStatsResponse = getEntityStatsFromAgg(aggregations);
        entityStatsResponse.setEntityMeta(getSingleEntityMeta(entityStatsResponse.getEntityType(), entityId));
        return entityStatsResponse;
    }

    @Override
    public ViewWiseFilters getTaskFilterOptions(TaskSearchRequestType type) {
        final InputStream data = TaskDiscoveryServiceImpl.class.getClassLoader().getResourceAsStream(FILTERS_JSON_FILE);
        TaskFilters taskFilters = SerDe.readValue(data, TaskFilters.class);
        List<FilterOptions> taskTypeFilterOptions = taskFiltersCache.get(TASK_TYPE_FILTERS_DEFAULT_KEY);
        Map<String, List<FilterOptions>> filterOptions = new HashMap<>();
        List<FilterOptions> objectiveFilters = taskTypeFilterOptions.stream().filter(objectiveFilter -> objectiveFilter.getFieldName().equals("objectives")).toList();
        List<FilterOptions> taskTypeFilters = taskTypeFilterOptions.stream().filter(objectiveFilter -> objectiveFilter.getFieldName().equals("action_id")).toList();
        filterOptions.put(OBJECTIVES, objectiveFilters);
        filterOptions.put(ACTION_ID, taskTypeFilters);
        return requestViewWiseFilterFactory.generateViewWiseFilter(taskFilters, filterOptions, type);

    }

    @Override
    public List<DiscoveryTaskInstance> getServiceBaseTasks(String entityId) {

        Map<String, Long> thisMonth = TaskEsUtils.getThisMonthEpochTime();

        List<Filter> filters = new ArrayList<>();
        filters.add(new GreaterEqualsNumberFilter(DUE_DATE, thisMonth.get(CURRENT)));
        filters.add(new LesserEqualsNumberFilter(START_DATE, thisMonth.get(CURRENT)));
        filters.add(new EqualsFilter(ACTIVE, true));
        filters.add(new NotEqualsFilter(TASK_STATE, CREATED.getText()));
        filters.add(new EqualsFilter(ENTITY_ID, entityId));
        return esRepository.search(TASK_INDEX, getBoolQuery(filters), 0, MAX_PAGE_SIZE, DiscoveryTaskInstance.class);
    }

    @Override
    public List<FilterOptions> getTaskFilters() {
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(ACTIVE, true));
        filters.add(getStartDateFilter());
        filters.add(getDueDateFilter());
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        AggregationBuilder taskActionAggregation = getTaskActionAggregation();
        AggregationBuilder taskObjectiveAggregation = getTaskObjectiveAggregation();
        List<AggregationBuilder> aggregationBuilders = Arrays.asList(taskActionAggregation, taskObjectiveAggregation);
        SearchResponse searchResponse = esRepository.getAggregationResult(TASK_INDEX, boolQueryBuilder, aggregationBuilders);
        List<String> actionIdList = getFilterValuesFromAggregation(getParsedStringTermsFromEsSearchResponse(searchResponse, ACTION_ID));
        List<String> objectiveList = getFilterValuesFromAggregation(getParsedStringTermsFromEsSearchResponse(searchResponse, ATTRIBUTES));
        Collections.sort(actionIdList);
        Collections.sort(objectiveList);
        List<FilterOptions> taskTypeFilters = new ArrayList<>();
        addActionIdsToTaskFilters(taskTypeFilters, actionIdList);
        addObjectivesToTaskFilters(taskTypeFilters, objectiveList);
        return taskTypeFilters;
    }

    private void addActionIdsToTaskFilters(List<FilterOptions> taskTypeFilters, List<String> actionIdList) {
        for (String actionId : actionIdList) {
            String taskDescription = taskActionRepository.get(actionId)
                    .orElseThrow(() -> LegionException.error(CoreErrorCode.NOT_FOUND, Collections.singletonMap(MESSAGE_KEY, "This action is missing" + actionId))).getDescription();
            taskTypeFilters.add(FilterOptions.builder()
                    .key(actionId)
                    .fieldName(ACTIONID)
                    .displayText(taskDescription)
                    .build());
        }
    }

    private void addObjectivesToTaskFilters(List<FilterOptions> taskTypeFilters, List<String> objectiveList) {
        objectiveList.forEach(objectiveId -> taskTypeFilters.add(FilterOptions.builder()
                .key(objectiveId)
                .displayText(taskAttributeService.getFromCache(objectiveId).getName())
                .fieldName(OBJECTIVES)
                .build()));
    }

    @Override
    public TaskDetailResponse getTaskDetailsById(TaskDetailRequest taskDetailRequest) {
        String taskInstanceId = taskDetailRequest.getTaskInstanceId();
        DiscoveryTaskInstance taskInstance = esRepository.get(taskInstanceId, TASK_INDEX, DiscoveryTaskInstance.class);
        if (taskInstance == null) {
            throw LegionException.error(LegionTaskErrorCode.INVALID_TASK_INSTANCE_ID);
        }
        TaskDefinitionInstance taskDefinitionInstance = taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskInstance.getTaskDefinitionId())
                .build());
        TaskDetailResponse taskDetailResponse = getTaskDetails(taskInstance, taskDefinitionInstance);
        taskDetailResponse.setMaxAllowedDateForIntent(DateUtils.addDaysToEpochDate(
                taskInstance.getCreatedAt(),
                miscellaneous.getMaxPossibleRescheduleOffsetInDays()
        ));
        boolean isLeadIntentUpdateAllowed = validationService.checkIfDefinitionIsWhitelisted(taskInstance.getTaskDefinitionId()) ?
                taskDefinitionInstance.isLeadUpdationAllowed() : getActionIdList(LeadManagementConfiguration.getLeadUpdation()).contains(taskInstance.getActionId());
        taskDetailResponse.setAllowLeadIntentUpdate(isLeadIntentUpdateAllowed);
        taskDetailResponse.setUpdateLeadIntentAllowed(isLeadIntentUpdateAllowed);
        taskDetailResponse.setTaskType(taskInstance.getActionId());
        return taskDetailResponse;
    }

    private TaskDetailResponse getTaskDetails(DiscoveryTaskInstance discoveryTaskInstance, TaskDefinitionInstance taskDefinitionInstance) {
        TaskActionInstance taskActionInstance = taskActionService.getFromDB(TaskActionFetchByIdRequest.builder()
                .taskActionId(discoveryTaskInstance.getActionId())
                .build());

        TaskInstance taskInstance = taskInstanceManagementService.getById(TaskByIdRequest.builder()
                .taskInstanceId(discoveryTaskInstance.getTaskInstanceId())
                .build());

        List<TaskAttributeInstance> taskDefinitionAttributesList = getTaskDefinitionAttributesList(discoveryTaskInstance);
        return discoveryTaskInstanceToTaskDetails(discoveryTaskInstance, taskActionInstance, taskDefinitionInstance, taskInstance, taskDefinitionAttributesList);
    }

    private List<TaskAttributeInstance> getTaskDefinitionAttributesList(DiscoveryTaskInstance discoveryTaskInstance) {
        List<TaskAttributeInstance> taskDefinitionAttributesList = new ArrayList<>();
        if (discoveryTaskInstance.getAttributes() != null) {
            for (Map.Entry<String, Set<String>> taskDefinitionAttribute : discoveryTaskInstance.getAttributes().entrySet()) {
                Set<String> attributeValueSet = taskDefinitionAttribute.getValue();
                for (String attributeValue : attributeValueSet) {
                    TaskAttributeInstance attributeInstance = taskAttributeService.getFromCache(attributeValue);
                    if (Objects.isNull(attributeInstance)) {
                        throw LegionException.error(CoreErrorCode.TASK_DEFINITION_ATTRIBUTE_ABSENT);
                    }

                    if (attributeInstance.getAttributeType().isDisplayAllowed()) {
                        taskDefinitionAttributesList.add(TaskAttributeInstance.builder()
                                .attributeType(attributeInstance.getAttributeType())
                                .name(attributeInstance.getName())
                                .taskattributeValue(attributeInstance.getTaskattributeValue())
                                .build());
                    }
                }
            }
        }
        return taskDefinitionAttributesList;
    }

    @Override
    public List<TaskStateStatsByAction> getTasksStateStatsByAction(TasksStatsRequest tasksStatsRequest) {
        List<Filter> filters = new ArrayList<>();
        filters.add(getTaskStatesFilter());
        filters.add(getUpdatedAtFilter(tasksStatsRequest.getLastUpdatedAt()));
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        AggregationBuilder aggregationBuilder = getActionTaskStatusAgg();
        Aggregations aggregations = esRepository.getAggregations(TASK_INDEX, boolQueryBuilder, aggregationBuilder, 0);
        return getTasksCountFromAgg(aggregations);
    }

    @Override
    public AgentTaskEligibilityResponse fetchAgentsEligibleForTask(String userId, AgentTaskEligibilityRequest request) {
        log.info("Fetching eligible agents for task with taskInstanceId={}", request.getTaskInstanceId());
        try {
            Entity entity = fetchEntityById(request);
            List<String> sectorIds = entity.getPolygonIds();
            AgentProfilesInSectorResponse activeAgentsInSectors = legionService.getAgentProfilesInSector(sectorIds);
            List<String> activeAgents = sectorIds.stream()
                    .flatMap(sectorId -> activeAgentsInSectors.getProfilesPerSector()
                            .getOrDefault(sectorId, Collections.emptyList())
                            .stream()
                            .filter(agent -> agent.getStatus() == AgentStatus.ACTIVATED)
                            .map(AgentProfile::getAgentId))
                    .collect(Collectors.toList());

            List<String> eligibleAgents = getEligibleAgentsForTask(activeAgents, request.getTaskInstanceId());

            return AgentTaskEligibilityResponse.builder()
                    .eligibleAgentsIds(eligibleAgents)
                    .build();
        } catch (Exception e) {
            log.info("[taskInstanceId={}] Error while fetching eligible agents: {}, error: ", request.getTaskInstanceId(), request, e);
            throw e;
        }
    }

    private Entity fetchEntityById(AgentTaskEligibilityRequest request) {
        try {
            Optional<Entity> entityStoreById = entityStore.getById(EntityStoreRequest.builder()
                    .referenceId(request.getEntityId())
                    .entityType(request.getEntityType())
                    .build());
            if (entityStoreById.isEmpty() || !entityStoreById.get().getType().equals(request.getEntityType())) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_ENTITY_ID);
            }
            return entityStoreById.get();
        } catch (LegionException e) {
            if (e.getErrorCode() == NOT_FOUND) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_ENTITY_ID);
            }
            throw e;
        }
    }

    private List<String> getEligibleAgentsForTask(List<String> agentIds, String taskInstanceId) {
        BoolQueryBuilder boolQueryBuilder = enricher.buildAgentsEligibleForTaskQuery(agentIds, taskInstanceId);
        SearchResponse searchResponse = esRepository.searchWithSorting(TASK_INDEX, boolQueryBuilder,
                1, 10000, DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS, SortOrder.DESC);

        SearchHits hits = searchResponse.getHits();
        if (hits.getHits().length == 0) {
            log.info("No results found for query: {}", boolQueryBuilder.toString());
            return Collections.emptyList();
        }

        Set<String> eligibleAgents = new HashSet<>();
        for (SearchHit hit : hits.getHits()) {
            Collections.addAll(eligibleAgents, hit.getMatchedQueries());
        }
        List<String> result = new ArrayList<>(eligibleAgents);
        log.info("Eligible Agents Found: {}", result);
        return result;
    }

}