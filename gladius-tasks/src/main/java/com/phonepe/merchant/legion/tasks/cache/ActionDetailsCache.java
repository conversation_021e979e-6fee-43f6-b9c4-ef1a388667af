package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.request.ActionDetails;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.legion.core.cache.AsyncCache;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import lombok.extern.slf4j.Slf4j;
import ru.vyarus.dropwizard.guice.module.installer.feature.eager.EagerSingleton;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

@Slf4j
@EagerSingleton
public class ActionDetailsCache extends AsyncCache<Category, List<ActionDetails>> {


    @Inject
    public ActionDetailsCache(Map<CacheName, CacheConfig> cacheConfigs, Provider<TaskDefinitionRepository> taskDefinitionRepositoryProvider,
                              MetricRegistry metricRegistry, CacheUtils cacheUtils, ValidationService validationService) {
        super(CacheName.ACTION_DETAILS, cacheConfigs.get(CacheName.ACTION_DETAILS), category -> {
            try {
                return fetchAllActionsOfCategory(taskDefinitionRepositoryProvider, category, validationService);
            } catch (Exception e) {
                log.warn("Error while loading action details for category: {}", category, e);
                throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
            }
        }, metricRegistry);
        cacheUtils.registerCache(CacheName.ACTION_DETAILS, this);
    }


    public static List<ActionDetails> fetchAllActionsOfCategory(
            Provider<TaskDefinitionRepository> taskDefinitionRepositoryProvider,
            Category category, ValidationService validationService) {

        List<StoredTaskDefinition> storedTaskDefinitions = taskDefinitionRepositoryProvider.get().getAll();
        Set<String> visitedActionIds = new HashSet<>();

        return storedTaskDefinitions.stream()
                .flatMap(storedTaskDefinition -> {
                    TaskDefinitionAttributes attributes = SerDe.<TaskDefinitionAttributes>readValue(
                            storedTaskDefinition.getDefinitionAttributes(),
                            new TypeReference<>() {
                            }
                    );

                    if (attributes == null || attributes.getCategory() == null) {
                        return Stream.empty();
                    }
                    if (category != Category.ALL && attributes.getCategory() != category) {
                        return Stream.empty();
                    }

                    // If NOT whitelisted → take from LeadManagementConfiguration
                    if (!validationService.checkIfDefinitionIsWhitelisted(storedTaskDefinition.getTaskDefinitionId())) {
                        return LeadManagementConfiguration.getLeadUpdation().stream()
                                .filter(config -> config.getActionId() != null
                                        && config.getActionId().equals(storedTaskDefinition.getActionId()));
                    }

                    // Else whitelisted → take from leadConfig
                    LeadConfig leadConfig = attributes.getLeadConfig();
                    if (leadConfig != null && leadConfig.getLeadUpdation() != null && !leadConfig.getLeadUpdation().isEmpty()) {
                        return leadConfig.getLeadUpdation().stream();
                    }

                    return Stream.empty();
                })
                .map(config -> ActionDetails.builder()
                        .actionId(config.getActionId())
                        .description(config.getDisplayText())
                        .build())
                .filter(details -> visitedActionIds.add(details.getActionId()))
                .toList();
    }
}
