package com.phonepe.merchant.legion.tasks.auth.resolver;

import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.request.DefinitionIdIdentifier;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByIdRequest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsByTypeCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsCache;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.olympus.im.client.OlympusIMClient;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
@Singleton
public class TaskCompletionRequestResolver extends AccessResolverForClients {
    public TaskCompletionRequestResolver(final Supplier<OlympusIMClient> olympusIMClientSupplier,
                                         final TaskDefinitionIdsCache taskDefinitionIdsCache,
                                         final TaskInstanceManagementServiceImpl taskInstanceManagementService,
                                         final FoxtrotEventIngestionService foxtrotEventIngestionService,
                                         final TaskDefinitionIdsByTypeCache taskDefinitionIdsByTypeCache) {
        super(olympusIMClientSupplier, taskDefinitionIdsCache, taskInstanceManagementService, foxtrotEventIngestionService, taskDefinitionIdsByTypeCache);
    }

    @Override
    public Class<? extends DefinitionIdIdentifier> getRequestClass() {
        return TaskCompletionByIdRequest.class;
    }
}