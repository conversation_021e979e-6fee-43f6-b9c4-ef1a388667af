package com.phonepe.merchant.legion.tasks.repository;

import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;

import java.util.List;
import java.util.Optional;
import java.util.function.UnaryOperator;

public interface TaskInstanceRepository {

  String generateTaskInstanceId(String entityId);
  StoredTaskInstance save(StoredTaskInstance storedAgentTaskInstance);

  Optional<StoredTaskInstance> get(String taskInstanceId);

  List<Audit<StoredTaskInstance>> audit(String taskInstanceId);

  boolean update(final String taskInstanceId, UnaryOperator<StoredTaskInstance> mutator) ;
}
