package com.phonepe.merchant.legion.tasks.services;


import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.models.merchants.tasks.EntityType;

public interface ValidationService {

    TaskDefinitionInstance validateAndGetTaskDefinition(String taskDefinitionId);

    void validateEntity(String entityId, EntityType entityType);

    Campaign validateAndGetCampaign(String campaignId);

    void validateBoundedAssignmentWithMissingAssignee(boolean markAvailable, String assigneeId);

    String validateRequesterAndGetActor(String userId, TaskSearchRequest taskSearchRequest);

    String validateRequesterAndGetActor(String userId, SectorMapViewTaskSearchRequest sectorMapViewTaskSearchRequest);

    void validateRequesterInHierarchy(String userId, String requesterId);

    void validateAccessibleSectors(String taskInstanceId, String endUserToken);

    void validateTaskType(String taskType);

    boolean checkIfDefinitionIsWhitelisted(String definitionId);

}
