package com.phonepe.merchant.legion.tasks.actions.validators;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.AgentTypeValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ValidatorConfig;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionValidatorMarker;
import com.phonepe.models.merchants.tasks.EntityType;


import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.AGENT_TYPE_VALIDATOR;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.SELF_ASSIGN_NOT_ALLOWED;

@ActionValidatorMarker(name = AGENT_TYPE_VALIDATOR)
public class AgentTypeValidator implements ActionValidator<TaskAssignRequest> {
    private final LegionService legionService;

    @Inject
    public AgentTypeValidator(LegionService legionService) {
        this.legionService = legionService;
    }

    @Override
    public void validate(EntityType entityType, ValidatorConfig validatorConfig) {
        // This validator does not depend on entity type
    }

    @Override
    public ValidatorResponse validate(TaskAssignRequest taskAssignRequest, ValidatorConfig validatorConfig) {
        AgentTypeValidatorConfig agentTypeValidatorConfig = (AgentTypeValidatorConfig) validatorConfig;
        AgentProfile agentProfile = legionService.getAgentProfile(taskAssignRequest.getAssignedTo());
        if (agentTypeValidatorConfig.getBlacklistedAgentTypes().isEmpty()|| !agentTypeValidatorConfig.getBlacklistedAgentTypes()
                .contains(agentProfile.getAgentType())) {
            return ValidatorResponse.builder()
                    .validated(true)
                    .build();
        }

        return ValidatorResponse.builder()
                .validated(false)
                .errorCode(SELF_ASSIGN_NOT_ALLOWED)
                .errorMessage("Agent is not allowed to perform this action")
                .build();

    }

}
