package com.phonepe.merchant.legion.tasks.entitystore;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity;
import com.phonepe.merchant.gladius.models.external.request.ExternalEntityFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.IntelService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.platform.atlas.model.fence.Shape;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.agentToAgentEntity;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.merchantToMerchantEntity;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.sectorToSectorEntity;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.storeToStoreEntity;
import static com.phonepe.merchant.legion.tasks.utils.EntityTransformationUtils.vpaToVpaEntity;

public class EntityDataFetchVisitor implements EntityType.EntityTypeVisitor<Entity, String> {

    private final MerchantService merchantService;
    private final AtlasService atlasService;
    private final IntelService intelService;
    private final TaskInstanceRepository taskInstanceRepository;
    private final ExternalEntityService externalEntityService;
    private final LegionService profileCRUDService;

    @Inject
    public EntityDataFetchVisitor(MerchantService merchantService,
                                  AtlasService atlasService,
                                  IntelService intelService,
                                  TaskInstanceRepository taskInstanceRepository,
                                  ExternalEntityService externalEntityService,
                                  LegionService profileCRUDService) {
        this.merchantService = merchantService;
        this.atlasService = atlasService;
        this.intelService = intelService;
        this.taskInstanceRepository = taskInstanceRepository;
        this.externalEntityService = externalEntityService;
        this.profileCRUDService = profileCRUDService;
    }


    private void throwInvalidEntityId() {
        throw LegionException.error(CoreErrorCode.NOT_FOUND,
                Map.of("message","Invalid Entity Id"));
    }
    @Override
    public Entity visitUser(String entityId) {
        return null;
    }

    @Override
    public Entity visitSector(String sectorId) {
        Shape response = atlasService.getSectorCoordinates(sectorId);
        return sectorToSectorEntity(sectorId, response);
    }

    @Override
    public Entity visitMerchant(String merchantId) {
        MerchantProfile merchantProfile = merchantService.getMerchantDetails(merchantId);
        List<PhysicalStore> stores = merchantService.getListOfMerchantStores(merchantId,1);
        if (stores.isEmpty()) {
            throwInvalidEntityId();
        }
        return merchantToMerchantEntity(stores.get(0),merchantProfile);
    }

    @Override
    public Entity visitNone(String entityId) {
        return null;
    }

    @Override
    public Entity visitStore(String entityId) {
        String[] data = entityId.split("_", 2);
        if (data.length != 2) {
            throwInvalidEntityId();
        }
        String merchantId = data[0];
        String storeId = data[1];
        PhysicalStore store = merchantService.getStoreDetails(merchantId,storeId);
        MerchantProfile merchantProfile = merchantService.getMerchantDetails(merchantId);
        List<String> sectors = atlasService.getSectorIdByLatLong(store.getLatitude(), store.getLongitude());
        return storeToStoreEntity(store,merchantProfile, sectors);
    }

    @Override
    public Entity visitTask(String entityId) {
        Optional<StoredTaskInstance> storedTaskInstanceOptional = taskInstanceRepository.get(entityId);
        if (!storedTaskInstanceOptional.isPresent()) {
            throwInvalidEntityId();
        }
        return storedTaskInstanceOptional.map(EntityTransformationUtils::taskToTaskEntity)
                .orElseThrow(()->LegionException.error(CoreErrorCode.NOT_FOUND));
    }

    @Override
    public Entity visitVpa(String entityId) {
        CompetitionQrResponse vpaDetails = intelService.getVpaDetails(entityId);
        return vpaToVpaEntity(vpaDetails);
    }

    @Override
    public Entity visitExternal(String entityId) {
        return externalEntityService.get(ExternalEntityFetchByIdRequest.builder()
                .externalEntityId(entityId)
                .build());
    }

    @Override
    public Entity visitAgent(String entityId) {
        AgentProfile agentProfile = profileCRUDService.getAgentProfile(entityId);
        return agentToAgentEntity(agentProfile);
    }

    @Override
    public Entity visitPhoneNumber(String entityId) {
        return PhoneNumberEntity.builder().phoneNumber(entityId).build();
    }
}
