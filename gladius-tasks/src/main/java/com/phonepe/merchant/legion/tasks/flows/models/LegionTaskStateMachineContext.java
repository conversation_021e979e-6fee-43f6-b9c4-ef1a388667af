package com.phonepe.merchant.legion.tasks.flows.models;

import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.flows.LegionFlowType;
import com.phonepe.merchant.legion.core.flows.models.LegionStateMachineContext;
import com.phonepe.merchant.legion.core.utils.SerDe;
import io.github.fsm.models.entities.Event;
import io.github.fsm.models.entities.State;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Optional;

public class LegionTaskStateMachineContext extends LegionStateMachineContext {

    @Getter
    @Setter
    private StoredTaskInstance storedTaskInstance;

    @Getter
    @Setter
    private transient DiscoveryTaskInstance discoveryTaskInstance;

    @Builder
    public LegionTaskStateMachineContext(State from, State to, Event causedEvent,
                                         StoredTaskInstance storedTaskInstance) {
        super(from, to, causedEvent);
        this.storedTaskInstance = storedTaskInstance;
    }

    @Override
    public LegionFlowType getType() {
        return LegionFlowType.TASK_POINT_FLOW;
    }

    @Override
    public String getActionId() {
        return storedTaskInstance.getActionId();
    }

    @Override
    public String getTaskDefinitionId() {
        return null != storedTaskInstance ? storedTaskInstance.getTaskDefinitionId() : null;
    }

    @Override
    public String getTaskInstanceId() {
        return null != storedTaskInstance ? storedTaskInstance.getTaskInstanceId() : null;
    }

    @Override
    public String getCampaignId() {
        return null != storedTaskInstance ? storedTaskInstance.getCampaignId() : null;
    }

    @Override
    public String getCurActor() {
        return null != storedTaskInstance ? storedTaskInstance.getCurActor() : null;
    }


    @Override
    public String getEntityId() {
        return null != storedTaskInstance ? storedTaskInstance.getEntityId() : null;
    }

    @Override
    public String getEntityType() {return null != storedTaskInstance ? storedTaskInstance.getEntityType().name() : null;}

    @Override
    public String getLeadIntent() {

        if (storedTaskInstance == null) {
            return null;
        }
        TaskInstanceMeta instanceMeta = SerDe.readValue(
                storedTaskInstance.getInstanceMeta(),
                TaskInstanceMeta.class
        );

        if (instanceMeta == null || CollectionUtils.isEmpty(instanceMeta.getTaskMetaList())) {
            return null;
        }
        return instanceMeta.getTaskMetaList().stream()
                .filter(item -> TaskMetaType.LEAD_INTENT.equals(item.getType()))
                .findFirst()
                .flatMap(item -> Optional.ofNullable(item.getValue()))
                .map(Object::toString)
                .orElse(null);
    }


}
