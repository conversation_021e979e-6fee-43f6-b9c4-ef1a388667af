package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.request.ActionDetails;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.CategoryDetails;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.tasks.cache.ActionDetailsCache;
import com.phonepe.merchant.legion.tasks.services.CategoryInsightService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class CategoryInsightServiceImpl implements CategoryInsightService {

    private final ChimeraLiteRepository chimeraRepository;
    private final ActionDetailsCache actionDetailsCache;
    private static final String CATEGORY_CHIMERA_KEY = "category_details_config";

    @Override
    public CategoryDetails fetchCategoryDetails() {
        return chimeraRepository.getChimeraConfig(CATEGORY_CHIMERA_KEY, CategoryDetails.class);
    }

    @Override
    public List<ActionDetails> fetchAllActionsOfCategory(Category category) {
        try {
            return actionDetailsCache.get(category);
        } catch (Exception e) {
            log.error("Error occurred while loading action details for category: {}", category);
            throw LegionException.error(CoreErrorCode.FAILED_TO_FETCH_ACTION_DETAILS, Map.of("message",
                    String.format("Failed to fetch action details for category %s", category.name())));
        }
    }

}
