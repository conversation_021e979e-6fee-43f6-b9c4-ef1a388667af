package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.tasks.EntityType;

import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MID_SID_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_STORE_MAPPED;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;

@ActionVerifierMarker(name = MID_SID_VERIFIER)
public class MidSidFromTaskMetaVerifier extends ActionVerifier {

    private final MerchantService merchantService;

    @Inject
    public MidSidFromTaskMetaVerifier(MerchantService merchantService) {
        this.merchantService = merchantService;
    }

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.PHONE_NUMBER) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        return VerifierResponse.builder()
                .verified(true)
                .context(context)
                .build();
    }

    @Override
    public boolean validateTaskCreation(CreateTaskInstanceRequest instanceRequest, TaskActionInstance actionInstance) {
        String merchantId = getMetaValue(instanceRequest, TaskMetaType.MID);
        String storeId = getMetaValue(instanceRequest, TaskMetaType.SID);
        if (merchantId == null || merchantId.isBlank() || storeId == null || storeId.isBlank()) {
            return false; //nothing to validate here
        }
        try {
            PhysicalStore store = merchantService.getStoreDetails(merchantId, storeId);
            //TO-DO: Remove location set from validation
            instanceRequest.setTransactionLocation(EsLocationRequest.builder()
                    .lat(store.getLatitude())
                    .lon(store.getLongitude())
                    .build()
            );
            return true;
        } catch (Exception ex) {
            throw LegionException.error(
                    INVALID_STORE_MAPPED,
                    Map.of(MESSAGE, "Store doesn't exist for MID: " + merchantId + ", SID: " + storeId + ". Lead cannot be created.")
            );
        }
    }

    private String getMetaValue(CreateTaskInstanceRequest request, TaskMetaType type) {
        if (request.getTaskInstanceMeta() == null) {
            return null;
        }
        Object value = request.getTaskInstanceMeta().getMetaValueObject(type);
        if (value instanceof String) {
            return (String) value;
        }
        return null;
    }
}
