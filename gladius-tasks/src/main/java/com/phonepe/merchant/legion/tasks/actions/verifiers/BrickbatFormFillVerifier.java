package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.flipkart.foxtrot.common.query.Filter;
import com.flipkart.foxtrot.common.query.general.EqualsFilter;
import com.flipkart.foxtrot.common.query.general.InFilter;
import com.flipkart.foxtrot.common.query.numeric.LessEqualFilter;
import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.core.FoxtrotRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.BrickbatFormFillVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.services.FoxtrotService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import edu.emory.mathcs.backport.java.util.Arrays;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_FORM_FILL_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_TABLE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EVENT_TYPE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.FOXTROT_TIME_KEY;

@ActionVerifierMarker(name = BRICKBAT_FORM_FILL_VERIFIER)
public class BrickbatFormFillVerifier extends ActionVerifier {

    private final FoxtrotService foxtrotService;
    private static final List<Object> EVENT_LIST = Arrays.asList(new String[]{"SURVEY_SUBMITTED","CREATE_FEEDBACK"});
    private static final String TASK_INSTANCE_ID_FIELD = "eventData.userId";


    @Inject
    public BrickbatFormFillVerifier(FoxtrotService foxtrotService) {
        this.foxtrotService = foxtrotService;
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        BrickbatFormFillVerifierConfig config = (BrickbatFormFillVerifierConfig)verificationConfig;
        VerifierResponse verifierResponse = VerifierResponse.builder()
                .context(context)
                .verified(null)
                .build();

        ArrayList<Filter> filters = new ArrayList<>();
        filters.add(EqualsFilter.builder()
                .field(TASK_INSTANCE_ID_FIELD)
                .value(taskCompleteRequest.getStoredTaskInstance().getTaskInstanceId())
                .build());
        filters.add(InFilter.builder()
                .field(EVENT_TYPE)
                .values(EVENT_LIST)
                .build());
        filters.add(LessEqualFilter.builder()
                .field(FOXTROT_TIME_KEY)
                .value(Calendar.getInstance().getTimeInMillis())
                .temporal(true)
                .build());
        FoxtrotRequest foxtrotRequest = FoxtrotRequest.builder()
                .table(BRICKBAT_TABLE)
                .filters(filters)
                .build();

        if (foxtrotService.getFoxtrotEventCount(foxtrotRequest) == 0)
            verifierResponse.setVerified(false);
        else if(config.isSuccessOnResponseFound())
            verifierResponse.setVerified(true);

        return verifierResponse;
    }
}
