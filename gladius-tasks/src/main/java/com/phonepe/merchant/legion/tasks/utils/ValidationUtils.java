package com.phonepe.merchant.legion.tasks.utils;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.LeadIntent;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.platform.atlas.model.fence.Polygon;
import com.phonepe.platform.atlas.model.fence.Shape;
import com.phonepe.platform.atlas.model.fence.ShapeType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_STATE;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.SECTOR_NOT_POLYGON;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.STEP_KEY_NAME;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidationUtils {

    private static final String MESSAGE = "message";
    private static final List<LegionTaskStateMachineState> SCHEDULING_ALLOWED_STATES = List.of(LegionTaskStateMachineState.SELF_ASSIGNED, LegionTaskStateMachineState.BOUNDED_ASSIGNED, LegionTaskStateMachineState.STARTED, LegionTaskStateMachineState.AVAILABLE);
    private static final List<LegionTaskStateMachineState> COMPLETION_ALLOWED = Arrays.asList(LegionTaskStateMachineState.SELF_ASSIGNED, LegionTaskStateMachineState.BOUNDED_ASSIGNED, LegionTaskStateMachineState.STARTED, LegionTaskStateMachineState.AVAILABLE);

    public static boolean isNotNullOrEmpty (List<String> list) {
        return (!Objects.isNull(list) && !list.isEmpty());
    }

    public static void validateSubsteps(List<Object> substeps) {
        if (substeps != null) {
            substeps.forEach(substep -> {
                Map<String, Object> step = (Map<String, Object>) substep;
                if (!step.containsKey(STEP_KEY_NAME)) {
                    throw LegionException.error(LegionTaskErrorCode.BAD_REQUEST,
                            Map.of(MESSAGE, "\"" + STEP_KEY_NAME + "\" is a mandatory field"));
                } else {
                    try {
                        Integer.parseInt((String) step.get(STEP_KEY_NAME));
                    } catch (NumberFormatException e) {
                        throw LegionException.error(LegionTaskErrorCode.BAD_REQUEST,
                                Map.of(MESSAGE, "\"" + STEP_KEY_NAME + "\" must be an integer"));
                    }
                }
            });
        }
    }

    public static void validateTaskDefinitionMeta(TaskDefinitionMeta taskDefinitionMeta) {
        if (taskDefinitionMeta == null) {
            return;
        }
        List<Object> substeps = taskDefinitionMeta.getSubsteps();
        validateSubsteps(substeps);
    }

    public static void validateTaskDefinition(TaskDefinitionInstance taskDefinitionInstance) {
        validateTaskDefinitionMeta(taskDefinitionInstance.getTaskDefinitionMeta());
    }

    public static void validateTaskMetaAttribute(CreateTaskInstanceRequest request, TaskMetaType attributeType) {
        boolean isAttributePresent = request.getTaskInstanceMeta().getTaskMetaList().stream().anyMatch(o -> o.getType().equals(attributeType));
        if (!isAttributePresent)
            throw LegionException.error(CoreErrorCode.TASK_ATTRIBUTE_ABSENT, Map.of(MESSAGE, "Device Id or Workflow Id is not present"));
    }


    public static void validateTaskScheduling(DiscoveryTaskInstance taskInstance) {

        if (!SCHEDULING_ALLOWED_STATES.contains(taskInstance.getTaskState())) {
            throw LegionException.error(INVALID_STATE);
        }
    }

    public static void validateTaskScheduling(DiscoveryTaskInstance discoveryTaskInstance, long scheduledAt) {
        Date currentTime = new Date();
        if (scheduledAt < currentTime.getTime()) {
            throw LegionException.error(LegionTaskErrorCode.TIME_SMALLER_THAN_CURRENT_TIME);
        }
        validateTaskScheduling(discoveryTaskInstance);
    }

    public static List<List<Double>> validateSectorPolygon(Shape shape) {
        if (shape.getType() != ShapeType.Polygon) {
            throw LegionException.error(SECTOR_NOT_POLYGON);
        }
        return ((Polygon) shape).getCoordinates().get(0);
    }

    public static List<LeadIntent> getAllowedIntents(List<ActionToRemarkConfig> leadUpdation, DiscoveryTaskInstance taskInstance) {
        Optional<ActionToRemarkConfig> leadCreationConfig = leadUpdation
                .stream()
                .filter(config -> config.getActionId().equals(taskInstance.getActionId()))
                .findFirst();
        List<LeadIntent> allowedLeadIntents = new ArrayList<>();
        if (leadCreationConfig.isPresent()) {
            allowedLeadIntents = leadCreationConfig.get()
                    .getConfig()
                    .stream()
                    .filter(IntentWithRemarks::getAllowMarkTaskComplete)
                    .map(IntentWithRemarks::getIntent)
                    .toList();
        }
        return allowedLeadIntents;
    }

    public static void validateForceCompletionRequest(StoredTaskInstance storedTaskInstance, String actor, LegionService legionService) {
        if ( !COMPLETION_ALLOWED.contains(storedTaskInstance.getCurState())){
            throw LegionException.error(LegionTaskErrorCode.INVALID_STATE);
        }

        if (storedTaskInstance.getDueDate().before(new Date(System.currentTimeMillis()))) {
            throw LegionException.error(LegionTaskErrorCode.TASK_EXPIRED);
        }
        log.info("Validating Actor for forceTaskCompletion {}", actor);
        legionService.getAgentProfile(actor);
    }
}
