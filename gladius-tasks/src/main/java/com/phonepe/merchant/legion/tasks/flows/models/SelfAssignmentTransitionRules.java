package com.phonepe.merchant.legion.tasks.flows.models;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelfAssignmentTransitionRules {

    private boolean selfAssignedToSelfAssignedAllowed;

    private boolean startedToSelfAssignedAllowed;

    private boolean locationValidationRequired;

    public boolean accept(LegionTaskStateMachineState state) {
        return switch (state) {
            case SELF_ASSIGNED -> isSelfAssignedToSelfAssignedAllowed();
            case STARTED -> isStartedToSelfAssignedAllowed();
            default -> false;
        };
    }
}
