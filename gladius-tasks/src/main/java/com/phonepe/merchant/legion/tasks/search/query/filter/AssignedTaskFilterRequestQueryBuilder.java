package com.phonepe.merchant.legion.tasks.search.query.filter;

import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.NotEqualsFilter;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.time.Clock;
import java.util.List;

import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

@Singleton
public class AssignedTaskFilterRequestQueryBuilder extends BaseTaskFilterRequestQueryBuilder<AssignedLocationTaskFilterRequest> {

    public AssignedTaskFilterRequestQueryBuilder() {
        super(Clock.systemDefaultZone());
    }

    protected AssignedTaskFilterRequestQueryBuilder(Clock clock) {
        super(clock);
    }

    @Override
    public BoolQueryBuilder enrichFilters(String actor, AssignedLocationTaskFilterRequest request) {
        List<Filter> filters = getBaseFilters();
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, actor));
        filters.add(new NotEqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, EntityType.PHONE_NUMBER.name()));
        return getBoolQuery(filters);
    }
}
