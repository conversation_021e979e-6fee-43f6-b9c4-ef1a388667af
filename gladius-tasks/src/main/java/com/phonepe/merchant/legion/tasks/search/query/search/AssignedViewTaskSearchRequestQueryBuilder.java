package com.phonepe.merchant.legion.tasks.search.query.search;

import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.NotEqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.predicate.ORFilter;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.models.merchants.tasks.EntityType;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;

import static com.phonepe.merchant.legion.tasks.utils.CommonUtils.enrichRequestWithNeedScheduledTaskFilters;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getTaskStatusFilter;

@Singleton
public class AssignedViewTaskSearchRequestQueryBuilder
        implements TaskSearchRequestQueryBuilder<AssignedViewTaskFetchRequest> {

    private final List<LegionTaskStateMachineState> assignedStates;

    @Inject
    public AssignedViewTaskSearchRequestQueryBuilder() {
        this.assignedStates = LegionTaskStateMachineState.getAssignedStates();
    }

    @Override
    public BoolQueryBuilder buildQuery(String agentId, AssignedViewTaskFetchRequest request) {
        enrichAssignedRequest(agentId, request.getFilters(), request.getAssignedTo());
        request.getFilters().add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE, true));
        request.getFilters().add(new NotEqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, EntityType.PHONE_NUMBER.name()));

        if (request.getNeedScheduledTasks() != null && request.getNeedScheduledTasks()) {
            enrichRequestWithNeedScheduledTaskFilters(request.getFilters(), request.getStartDate(), request.getEndDate());
        }
        return getBoolQuery(request.getFilters());
    }

    private void enrichAssignedRequest(String actor, List<Filter> filters, String assignedTo) {
        //set value of actor in request
        actor = ObjectUtils.firstNonNull(assignedTo, actor);

        //error thrown is 500, should be 400?
        Preconditions.checkNotNull(actor, "assignedTo cannot be null in assigned view");

        //fetch tasks assigned to or completed by a particular agent
        ORFilter orFilter = new ORFilter(List.of(
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, actor),
                new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.COMPLETED_BY, actor)
        ));
        filters.add(orFilter);

        //apply default task status and duration filters for assigned
        filters.add(getTaskStatusFilter(assignedStates));
    }

}
