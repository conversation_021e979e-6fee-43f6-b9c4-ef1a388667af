package com.phonepe.merchant.legion.tasks.utils;

import com.phonepe.merchant.gladius.models.entitystore.AgentEntity;
import com.phonepe.merchant.gladius.models.entitystore.AgentEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntity;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.MerchantEntity;
import com.phonepe.merchant.gladius.models.entitystore.MerchantEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntity;
import com.phonepe.merchant.gladius.models.entitystore.PhoneNumberEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.SectorEntity;
import com.phonepe.merchant.gladius.models.entitystore.SectorEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.SourceMeta;
import com.phonepe.merchant.gladius.models.entitystore.StoreEntity;
import com.phonepe.merchant.gladius.models.entitystore.StoreEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.TaskEntity;
import com.phonepe.merchant.gladius.models.entitystore.TaskEntityMeta;
import com.phonepe.merchant.gladius.models.entitystore.VpaEntity;
import com.phonepe.merchant.gladius.models.entitystore.VpaEntityMeta;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.models.merchants.tasks.TaskMerchantMeta;
import com.phonepe.models.merchants.tasks.TaskStoreMeta;
import com.phonepe.models.merchants.tasks.TaskVpaMeta;
import com.phonepe.platform.atlas.model.fence.Polygon;
import com.phonepe.platform.atlas.model.fence.Shape;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType.STORE_NAME;

public class EntityTransformationUtils {

    private static Map<EntityType, SourceMeta> entitySourceMetaMapping;

    private EntityTransformationUtils() {

    }

    public static void populateEntitySourceMetaMapping(Map<EntityType, SourceMeta> entitySourceMetaMapping) {
        EntityTransformationUtils.entitySourceMetaMapping = entitySourceMetaMapping;
    }

    public static EntityMeta entityToEntityMeta(Entity entity) {
        EntityMeta entityMeta = entity.getType().accept(new EntityType.EntityTypeVisitor<EntityMeta, Entity>() {
            @Override
            public EntityMeta visitUser(Entity data) {
                return null;
            }

            @Override
            public EntityMeta visitSector(Entity data) {
                return new SectorEntityMeta((SectorEntity)data);
            }

            @Override
            public EntityMeta visitMerchant(Entity data) {
                return new MerchantEntityMeta((MerchantEntity) data);
            }

            @Override
            public EntityMeta visitNone(Entity data) {
                return null;
            }

            @Override
            public EntityMeta visitStore(Entity data) {
                return new StoreEntityMeta((StoreEntity)data);
            }

            @Override
            public EntityMeta visitTask(Entity entity) {
                return new TaskEntityMeta((TaskEntity)entity);
            }

            @Override
            public EntityMeta visitVpa(Entity entity) {
                return new VpaEntityMeta((VpaEntity)entity);
            }

            @Override
            public EntityMeta visitExternal(Entity entity) {
                return new ExternalEntityMeta((ExternalEntity)entity);
            }

            @Override
            public EntityMeta visitAgent(Entity entity) {
                return new AgentEntityMeta((AgentEntity)entity);
            }

            @Override
            public EntityMeta visitPhoneNumber(Entity entity) {
                return new PhoneNumberEntityMeta((PhoneNumberEntity)entity);
            }
        },entity);
        if (entityMeta != null) {
            entityMeta.setSourceMeta(entitySourceMetaMapping.get(entity.getType()));
        }
        return entityMeta;
    }

    public static EntityMeta copyEntityMeta(EntityMeta entityMeta) {
        if (entityMeta == null) {
            return null;
        }
        return entityMeta.getEntityType().accept(new EntityType.EntityTypeVisitor<EntityMeta, EntityMeta>() {
            @Override
            public EntityMeta visitUser(EntityMeta data) {
                return null;
            }

            @Override
            public EntityMeta visitSector(EntityMeta data) {
                return new SectorEntityMeta((SectorEntityMeta) data);
            }

            @Override
            public EntityMeta visitMerchant(EntityMeta data) {
                return new MerchantEntityMeta((MerchantEntityMeta) data);
            }

            @Override
            public EntityMeta visitNone(EntityMeta data) {
                return null;
            }

            @Override
            public EntityMeta visitStore(EntityMeta data) {
                return new StoreEntityMeta((StoreEntityMeta) data);
            }

            @Override
            public EntityMeta visitTask(EntityMeta data) {
                return new TaskEntityMeta((TaskEntityMeta) data);
            }

            @Override
            public EntityMeta visitVpa(EntityMeta data) {
                return new VpaEntityMeta((VpaEntityMeta) data);
            }

            @Override
            public EntityMeta visitExternal(EntityMeta data) {
                return new ExternalEntityMeta((ExternalEntityMeta) data);
            }

            @Override
            public EntityMeta visitAgent(EntityMeta data) {
                return new AgentEntityMeta((AgentEntityMeta) data);
            }

            @Override
            public EntityMeta visitPhoneNumber(EntityMeta data) {
                return new PhoneNumberEntityMeta((PhoneNumberEntityMeta) data);
            }
        },entityMeta);
    }

    @SuppressWarnings("java:S1874")
    public static StoreEntity storeToStoreEntity(PhysicalStore store, MerchantProfile merchantProfile, List<String> sectors) {
        return StoreEntity.builder()
                .storeName(store.getName())
                .phoneNumber(store.getPhoneNumber())
                .displayName(store.getDisplayName())
                .storeId(store.getStoreId())
                .merchantId(store.getMerchantId())
                .polygonIds((sectors == null) ? new ArrayList<>() : new ArrayList<>(sectors))
                .createdAt(store.getCreatedAt())
                .updatedAt(store.getUpdatedAt())
                .location(EsLocationRequest.builder()
                        .lat(store.getLatitude())
                        .lon(store.getLongitude())
                        .build())
                .businessUnit(merchantProfile.getBusinessUnit())
                .subCategory(merchantProfile.getSubCategory())
                .category(merchantProfile.getCategory())
                .superCategory(merchantProfile.getSuperCategory())
                .build();
    }

    @SuppressWarnings("java:S1874")
    public static MerchantEntity merchantToMerchantEntity(PhysicalStore store, MerchantProfile merchantProfile) {
        return MerchantEntity.builder()
                .phoneNumber(merchantProfile.getPhoneNumber())
                .location(EsLocationRequest.builder()
                        .lat(store.getLatitude())
                        .lon(store.getLongitude())
                        .build())
                .merchantId(merchantProfile.getMerchantId())
                .superCategory(merchantProfile.getSuperCategory())
                .subCategory(merchantProfile.getSubCategory())
                .category(merchantProfile.getCategory())
                .displayName(merchantProfile.getDisplayName())
                .fullName(merchantProfile.getFullName())
                .pincode(merchantProfile.getPincode())
                .logoUrl(merchantProfile.getLogoUrl())
                .email(merchantProfile.getEmail())
                .polygonIds((store.getSectors() == null)?new ArrayList<>():new ArrayList<>(store.getSectors()))
                .businessUnit(merchantProfile.getBusinessUnit())
                .businessGroupId(merchantProfile.getBusinessGroupId())
                .updatedAt(merchantProfile.getUpdatedAt())
                .createdAt(merchantProfile.getCreatedAt())
                .brandId(merchantProfile.getBrandId())
                .address(merchantProfile.getAddress())
                .build();
    }

    public static MerchantEntity taskMerchantMetaToMerchantEntity(TaskMerchantMeta merchantMeta) {
        return MerchantEntity.builder()
                .displayName(merchantMeta.getDisplayName())
                .merchantId(merchantMeta.getMerchantId())
                .category(merchantMeta.getDisplayCategory())
                .phoneNumber(merchantMeta.getPhoneNumber())
                .build();
    }

    public static TaskEntity taskToTaskEntity(StoredTaskInstance storedTaskInstance) {
        return TaskEntity.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .completedBy(storedTaskInstance.getCurActor())
                .completedOn(storedTaskInstance.getUpdatedAt())
                .build();
    }

    public static StoreEntity taskStoreMetaToStoreEntity(TaskStoreMeta storeMeta) {
        return StoreEntity.builder()
                .storeName(storeMeta.getDisplayName())
                .storeId(storeMeta.getStoreId())
                .merchantId(storeMeta.getMerchantId())
                .category(storeMeta.getDisplayCategory())
                .phoneNumber(storeMeta.getPhoneNumber())
                .build();
    }

    public static VpaEntity taskVpaMetaToVpaEntity(TaskVpaMeta vpaMeta) {
        return VpaEntity.builder()
                .vpaId(vpaMeta.getVpaId())
                .shopName(vpaMeta.getShopName())
                .providerName(vpaMeta.getProviderName())
                .build();
    }

    public static TaskEntity discoveryTaskInstanceToTaskEntity(DiscoveryTaskInstance taskInstance) {
        return TaskEntity.builder()
                .taskInstanceId(taskInstance.getTaskInstanceId())
                .build();
    }

    /**
     *
     * @param sectorId
     * @param fence
     * @return
     *
     */
    public static SectorEntity sectorToSectorEntity(String sectorId, Shape fence) {
        Polygon polygon = (Polygon) fence;
        List<List<List<Double>>> coordinates = polygon.getCoordinates();
        List<List<Double>> polygonCoordinates = coordinates.get(0);

        double lonTotal = 0;
        double latTotal = 0;
        for(List<Double> latLong : polygonCoordinates){
            lonTotal  += latLong.get(0);
            latTotal  += latLong.get(1);
        }
        return SectorEntity.builder()
                .sectorId(sectorId)
                .location(EsLocationRequest.builder()
                        .lat(latTotal/polygonCoordinates.size())
                        .lon(lonTotal/polygonCoordinates.size()).build())
                .build();
    }

    public static VpaEntity vpaToVpaEntity(CompetitionQrResponse vpa) {
        List<String> sectors = vpa.getSectorIds();
        return VpaEntity.builder()
                .location(EsLocationRequest.builder()
                        .lon((double) vpa.getLongitude())
                        .lat((double) vpa.getLatitude())
                        .build())
                .shopName(vpa.getBusinessName())
                .providerName(vpa.getProviderName())
                .polygonIds(sectors)
                .vpaId(vpa.getVpa())
                .build();
    }

    public static AgentEntity agentToAgentEntity(AgentProfile agentProfile) {
        return AgentEntity.builder()
                .active(agentProfile.isActive())
                .addresses(agentProfile.getAddresses())
                .agentId(agentProfile.getAgentId())
                .managerId(agentProfile.getManagerId())
                .agentName(agentProfile.getName())
                .emailId(agentProfile.getEmailId())
                .lastActiveDate(agentProfile.getLastActiveDate())
                .phoneNumber(agentProfile.getPhoneNumber())
                .polygonIds(agentProfile.getSectors())
                .role(agentProfile.getAgentType())
                .build();
    }

    public static PhoneNumberEntity phoneNumberTaskToPhoneNumberEntity(DiscoveryTaskInstance taskInstance) {
        Object storeNameObject = taskInstance.getTaskInstanceMeta().getTaskMetaAsMap().get(STORE_NAME);
        if (storeNameObject == null) {
            return null;
        }
        return PhoneNumberEntity.builder()
                .name(storeNameObject.toString())
                .phoneNumber(taskInstance.getEntityId())
                .build();
    }

}
