package com.phonepe.merchant.legion.tasks.search.response.filter;

import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.filters.FilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilterOptions;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filters.ViewWiseFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.FilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFilterOptionsV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.ViewWiseFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.legion.tasks.utils.CommonUtils;

import java.util.List;
import java.util.Map;


@Singleton
public class GenerateAssignedViewWiseFilters extends BaseViewWiseFilters {

    @Override
    public ViewWiseFilters generateViewWiseFilters(TaskFilters taskFilters,
                                                   Map<String, List<FilterOptions>> filterOptions,
                                                   TaskSearchRequestType type) {
        List<TaskFilterOptions> assignedViewFilters = getFilterOptions(filterOptions,
                taskFilters.getAssignedFilterOptions().getFilters());
        return ViewWiseFilters.builder()
                .maxDate(CommonUtils.calculateMaxDate())
                .minDate(CommonUtils.calculateMinDate())
                .filters(assignedViewFilters).build();
    }

    @Override
    public ViewWiseFiltersV2 generateViewWiseFiltersV2(TaskFiltersV2 taskFilters, Map<String, List<FilterOptionsV2>> filterOptions, TaskSearchRequestType type) {
        List<TaskFilterOptionsV2> assignedViewFilters = getFilterOptionsV2(filterOptions,
                taskFilters.getAssignedFilterOptions().getFilters());
        return ViewWiseFiltersV2.builder()
                .maxDate(CommonUtils.calculateMaxDate())
                .minDate(CommonUtils.calculateMinDate())
                .filters(assignedViewFilters).build();
    }

}
