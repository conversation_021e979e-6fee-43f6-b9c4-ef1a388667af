package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAttributeCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Optional;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import killswitch.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/v1/task/attribute")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Task Attribute Related APIs")
@SecurityRequirement(name = AUTH_NAME)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
public class TaskAttributeResource {

    private final TaskAttributeService taskAttributeService;

    @POST
    @Timed
    @Path("/create")
    @ExceptionMetered
    @Authorize(value = "createAttribute")
    @RolesAllowed(value = "createAttribute")
    @Operation(summary = "Create Or Update Task Attribute")
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public GenericResponse<TaskAttributeInstance> createOrUpdate(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
                                                                 @NotNull @Valid TaskAttributeCreateRequest request,
                                                                 @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                                 @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                                 @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        TaskAttributeInstance response = taskAttributeService.saveOrUpdate(request, actor);
        return GenericResponse.<TaskAttributeInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }

    @GET
    @Timed
    @ExceptionMetered
    @Path("/{taskAttributeValue}")
    @RolesAllowed(value = "fetchAttributes")
    @Operation(summary = "Fetch Task Attribute")
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskAttributeInstance> getById(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @PathParam("taskAttributeValue") @NotNull final String taskAttributeValue) {
        TaskAttributeInstance response = taskAttributeService.getFromDB(taskAttributeValue);
        return GenericResponse.<TaskAttributeInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }
}
