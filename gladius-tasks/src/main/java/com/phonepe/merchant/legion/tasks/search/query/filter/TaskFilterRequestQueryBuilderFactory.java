package com.phonepe.merchant.legion.tasks.search.query.filter;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedSectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoverySectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryHotspotTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedHotspotTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.EscalationViewTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.LeadViewTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.TaskFilterRequest;
import com.phonepe.merchant.legion.tasks.bindings.filter.AssignedTaskFilterRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.AssignedSectorTaskFilterRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.DiscoveryTaskFilterRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.DiscoverySectorFilterQueryGeneratorProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.EscalationViewFilterQueryGeneratorProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.LeadViewFilterQueryGeneratorProvider;
import com.phonepe.merchant.legion.tasks.search.query.HotspotRequestQueryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilder;

@Slf4j
@Singleton
public class TaskFilterRequestQueryBuilderFactory {

    private final TaskFilterRequestQueryBuilder<AssignedLocationTaskFilterRequest> assignedViewFilterGenerator;
    private final TaskFilterRequestQueryBuilder<DiscoveryLocationTaskFilterRequest> discoveryViewFilterGenerator;
    private final TaskFilterRequestQueryBuilder<AssignedSectorTaskFilterRequest> sectorAssignedViewFilterGenerator;
    private final TaskFilterRequestQueryBuilder<DiscoverySectorTaskFilterRequest> sectorDiscoveryViewFilterGenerator;
    private final TaskFilterRequestQueryBuilder<EscalationViewTaskFilterRequest> escalationViewFilterGenerator;
    private final HotspotRequestQueryBuilder hotspotRequestQueryBuilder;
    private final TaskFilterRequestQueryBuilder<LeadViewTaskFilterRequest> leadViewFilterGenerator;


    @Inject
    public TaskFilterRequestQueryBuilderFactory(
            @AssignedTaskFilterRequestQueryBuilderProvider
            TaskFilterRequestQueryBuilder<AssignedLocationTaskFilterRequest> assignedViewFilterGenerator,
            @DiscoveryTaskFilterRequestQueryBuilderProvider
            TaskFilterRequestQueryBuilder<DiscoveryLocationTaskFilterRequest> discoveryViewFilterGenerator,
            @AssignedSectorTaskFilterRequestQueryBuilderProvider
            TaskFilterRequestQueryBuilder<AssignedSectorTaskFilterRequest> sectorAssignedViewFilterGenerator,
            @DiscoverySectorFilterQueryGeneratorProvider
            TaskFilterRequestQueryBuilder<DiscoverySectorTaskFilterRequest> sectorDiscoveryViewFilterGenerator,
            @EscalationViewFilterQueryGeneratorProvider
            TaskFilterRequestQueryBuilder<EscalationViewTaskFilterRequest> escalationViewFilterGenerator,
            HotspotRequestQueryBuilder hotspotRequestQueryBuilder,
            @LeadViewFilterQueryGeneratorProvider
            TaskFilterRequestQueryBuilder<LeadViewTaskFilterRequest> leadViewFilterGenerator
            ) {
        this.assignedViewFilterGenerator = assignedViewFilterGenerator;
        this.discoveryViewFilterGenerator = discoveryViewFilterGenerator;
        this.sectorAssignedViewFilterGenerator = sectorAssignedViewFilterGenerator;
        this.sectorDiscoveryViewFilterGenerator = sectorDiscoveryViewFilterGenerator;
        this.escalationViewFilterGenerator = escalationViewFilterGenerator;
        this.hotspotRequestQueryBuilder = hotspotRequestQueryBuilder;
        this.leadViewFilterGenerator = leadViewFilterGenerator;
    }

    public QueryBuilder enrichFilter(String actor, TaskFilterRequest request) {
        return request.getTaskSearchRequestType().accept(new TaskSearchRequestType.TaskSearchRequestTypeVisitor<>() {

            @Override
            public QueryBuilder visitAssignedRequest(TaskFilterRequest payload) {
                AssignedLocationTaskFilterRequest assignedLocationTaskFilterRequest = (AssignedLocationTaskFilterRequest) payload;
                return assignedViewFilterGenerator.enrichFilters(actor, assignedLocationTaskFilterRequest);
            }

            @Override
            public QueryBuilder visitDiscoveryRequest(TaskFilterRequest payload) {
                DiscoveryLocationTaskFilterRequest discoveryLocationDiscoveryTaskFilterRequest = (DiscoveryLocationTaskFilterRequest) payload;
                return discoveryViewFilterGenerator.enrichFilters(actor, discoveryLocationDiscoveryTaskFilterRequest);
            }

            @Override
            public QueryBuilder visitSectorAssignedRequest(TaskFilterRequest payload) {
                AssignedSectorTaskFilterRequest assignedSectorTaskAssignedFilterRequest = (AssignedSectorTaskFilterRequest) payload;
                return sectorAssignedViewFilterGenerator.enrichFilters(actor, assignedSectorTaskAssignedFilterRequest);
            }

            @Override
            public QueryBuilder visitSectorDiscoveryRequest(TaskFilterRequest payload) {
                DiscoverySectorTaskFilterRequest discoverySectorTaskDiscoveryFilterRequest = (DiscoverySectorTaskFilterRequest) payload;
                return sectorDiscoveryViewFilterGenerator.enrichFilters(actor, discoverySectorTaskDiscoveryFilterRequest);
            }

            @Override
            public QueryBuilder viewEscalatedView(TaskFilterRequest payload) {
                EscalationViewTaskFilterRequest escalationViewTaskFilterRequest = (EscalationViewTaskFilterRequest) payload;
                return escalationViewFilterGenerator.enrichFilters(actor, escalationViewTaskFilterRequest);
            }

            @Override
            public QueryBuilder viewHotspotAssignedView(TaskFilterRequest payload) {
                return hotspotRequestQueryBuilder.buildQuery(actor, ((AssignedHotspotTaskFilterRequest) payload).getHotspotId()).must(
                        visitSectorAssignedRequest(payload));
            }
            @Override
            public QueryBuilder viewHotspotDiscoveryView(TaskFilterRequest payload) {
                return hotspotRequestQueryBuilder.buildQuery(actor, ((DiscoveryHotspotTaskFilterRequest) payload).getHotspotId()).must(
                        visitSectorDiscoveryRequest(payload));
            }

            @Override
            public QueryBuilder visitLeadRequest(TaskFilterRequest payload) {
                LeadViewTaskFilterRequest leadViewTaskFilterRequest = (LeadViewTaskFilterRequest) payload;
                return leadViewFilterGenerator.enrichFilters(actor, leadViewTaskFilterRequest);
            }
        }, request);

    }

}
