package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.SeerService;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_TASK_CLOSURE_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;

@Slf4j
@ActionVerifierMarker(name = EDC_TASK_CLOSURE_VERIFIER)
public class EdcTaskClosureVerifier extends ActionVerifier {

    private final SeerService seerService;

    @Inject
    public EdcTaskClosureVerifier(SeerService seerService) {
        this.seerService = seerService;
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        String taskInstanceId = taskCompleteRequest.getTaskInstanceId();
        Boolean verified = seerService.getTaskClosureVerificationStatus(taskInstanceId);
        return VerifierResponse.builder()
                .verified(verified)
                .context(context)
                .build();
    }

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.STORE) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }

}
