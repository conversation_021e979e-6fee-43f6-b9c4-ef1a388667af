package com.phonepe.merchant.legion.tasks.utils;

import com.phonepe.merchant.gladius.models.tasks.request.CreateCommentRequest;
import com.phonepe.merchant.gladius.models.tasks.response.CommentsOnTaskResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommentsOnTaskTransformationUtils {

    public static CommentsOnTaskResponse toCommentsOnTaskResponse(StoredCommentsOnTask storedCommentsOnTask, AgentProfile agentProfile) {
        return CommentsOnTaskResponse.builder()
                .commentId(storedCommentsOnTask.getCommentId())
                .taskInstanceId(storedCommentsOnTask.getTaskInstanceId())
                .content(storedCommentsOnTask.getContent())
                .createdAt(storedCommentsOnTask.getCreatedAt())
                .commenterId(storedCommentsOnTask.getCreatedBy())
                .commenterAgentType(agentProfile.getAgentType())
                .commenterName(agentProfile.getName())
                .build();
    }

    public static StoredCommentsOnTask toStoredCommentsOnTask(CreateCommentRequest createCommentRequest, String commentId, String actor) {
        return StoredCommentsOnTask.builder()
                .commentId(commentId)
                .createdBy(actor)
                .updatedBy(actor)
                .taskInstanceId(createCommentRequest.getTaskInstanceId())
                .content(createCommentRequest.getContent())
                .build();
    }
}
