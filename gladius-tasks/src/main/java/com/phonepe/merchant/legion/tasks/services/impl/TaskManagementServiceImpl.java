package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.common.base.MoreObjects;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.TaskSchedulingType;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskMarkAvailableRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.UserTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskCreationConfigParameters;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskParams;
import com.phonepe.merchant.gladius.models.tasks.utils.UserTaskCreationConfig;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.DateUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.flows.TaskEngine;
import com.phonepe.merchant.legion.tasks.flows.TransitionValidator;
import com.phonepe.merchant.legion.tasks.flows.models.Wrapper;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;
import static com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils.buildTaskParams;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SuppressWarnings("java:S3776")
public class TaskManagementServiceImpl implements TaskManagementService {

    private final TaskEngine taskEngine;
    private final TaskInstanceRepository taskInstanceRepository;
    private final TransitionValidator transitionValidator;
    private final ValidationService validationService;
    private final TaskInstanceManagementService taskInstanceManagementService;
    private final UserTaskCreationConfig userTaskCreationConfig;
    private final Miscellaneous miscellaneous;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;


    @Override
    public StoredTaskInstance command(TaskCommandRequest request, String userId) {
        return request.accept(new TaskCommandRequest.TaskCommandRequestVisitor<StoredTaskInstance>() {
            @Override
            @MonitoredFunction(method = "command.createTaskRequest")
            public StoredTaskInstance visit(TaskCreateRequest data) {
                data.getTaskInstance().setCreatedBy(
                        MoreObjects.firstNonNull(data.getTaskInstance().getCreatedBy(), userId));
                foxtrotEventIngestionService.ingestTaskCreationRequestReceivedEvent(data);
                validateRescheduleDate(data.getRescheduleAt());
                StoredTaskInstance storedTaskInstance = taskEngine.create(data.getTaskInstance());
                foxtrotEventIngestionService.ingestTaskCreationSuccessEvent(data, storedTaskInstance);
                if (EntityType.PHONE_NUMBER.equals(storedTaskInstance.getEntityType())) {
                    storedTaskInstance = handlePhoneNumberEntityBoundedAssignment(storedTaskInstance);
                }
                if (data.isMarkAvailable()) {
                    storedTaskInstance = handleMarkAvailable(data, storedTaskInstance);
                }
                if (data.getRescheduleAt() != null) {
                    handleReschedule(data, storedTaskInstance, userId);
                }
                if (StringUtils.isNotEmpty(data.getCommentContent())) {
                    handleComment(data, storedTaskInstance, userId);
                }
                return storedTaskInstance;
            }


            // It will mark tasks as available only if it's not rescheduled -- removed this check 8/05/23 for unassign feature
            @Override
            @MonitoredFunction(method = "command.markAvailableTaskRequest")
            public StoredTaskInstance visit(TaskMarkAvailableRequest data) {
                data.setMarkedBy(MoreObjects.firstNonNull(data.getMarkedBy(), userId));
                StoredTaskInstance storedTaskInstance = fetchTaskInstance(data.getTaskInstanceId());
                return taskEngine.markAvailable(storedTaskInstance, data);
            }

            @Override
            @MonitoredFunction(method = "command.assignTaskRequest")
            public StoredTaskInstance visit(TaskAssignRequest data) {
                try {
                    log.info("Received request to assign taskInstanceId {} for agentId {} ", data.getTaskInstanceId(), data.getAssignedTo());
                    if (data.getAssignedTo() != null && data.getAssignedTo().isEmpty()) {
                        throw LegionException.error(LegionTaskErrorCode.BAD_REQUEST,
                                Map.of(MESSAGE, "assignedTo cannot be empty"));
                    }
                    data.setAssignedTo(MoreObjects.firstNonNull(data.getAssignedTo(), userId));
                    StoredTaskInstance storedTaskInstance = fetchTaskInstance(data.getTaskInstanceId());
                    return taskEngine.assignTask(storedTaskInstance, data);
                } catch (LegionException e) {
                    log.error("Error occurred while assigning task for taskInstanceId {} for agentId {}", data.getTaskInstanceId(), data.getAssignedTo(), e);
                    throw e;
                } catch (Exception e) {
                    log.error("Error occurred while assigning task for taskInstanceId {} for agentId {}", data.getTaskInstanceId(), data.getAssignedTo(), e);
                    throw LegionException.error(LegionTaskErrorCode.BAD_REQUEST, Map.of(MESSAGE, e));
                }
            }

            @Override
            @MonitoredFunction(method = "command.taskCompletionRequest")
            public StoredTaskInstance visit(TaskCompleteRequest data) {
                data.setCompletedBy(MoreObjects.firstNonNull(data.getCompletedBy(), userId));
                StoredTaskInstance storedTaskInstance = fetchTaskInstance(data.getTaskInstanceId());
                return taskEngine.verificationInit(storedTaskInstance, data);
            }

            @Override
            @MonitoredFunction(method = "command.taskUpdationRequest")
            public StoredTaskInstance visit(TaskUpdateRequest data) {
                Wrapper<StoredTaskInstance> wrapper = new Wrapper<>();
                taskInstanceRepository.update(data.getTaskInstanceId(), storedTaskInstance -> {
                    storedTaskInstance.setInstanceMeta(SerDe.writeValueAsBytes(
                            data.getTaskInstanceMeta()
                    ));
                    wrapper.setData(storedTaskInstance);
                    return storedTaskInstance;
                });
                return wrapper.getData();
            }

            @Override
            @MonitoredFunction(method = "command.startTaskRequest")
            public StoredTaskInstance visit(TaskStartRequest data) {
                data.setStartedBy(MoreObjects.firstNonNull(userId, data.getStartedBy()));
                StoredTaskInstance storedTaskInstance = fetchTaskInstance(data.getTaskInstanceId());
                return taskEngine.markTaskStarted(storedTaskInstance, data);
            }

            @Override
            @MonitoredFunction(method = "command.leadTaskCreationRequest")
            public StoredTaskInstance visit(UserTaskCreationRequest data) {
                foxtrotEventIngestionService.ingestLeadGenRequestEvent(data);
                TaskParams taskParams = buildTaskParams(data, Objects.requireNonNull(getTaskCreationParams(data)));

                validateRescheduleAt(data.getRescheduleAt());
                getAndValidateDuplicateTasks(taskParams, data.getEntityType());

                CreateTaskInstanceRequest createTaskInstanceRequest = TaskInstanceTransformationUtils.toCreateTaskInstanceRequest(
                        userId, taskParams, data.getRemark(), data.getLeadIntent());
                StoredTaskInstance storedTaskInstance = taskEngine.create(createTaskInstanceRequest);
                foxtrotEventIngestionService.ingestUserLeadTaskCreationSuccessEvent(data, storedTaskInstance);
                storedTaskInstance = fetchTaskInstance(storedTaskInstance.getTaskInstanceId());
                taskEngine.markAvailable(storedTaskInstance,
                        TaskMarkAvailableRequest.builder()
                                .markedBy(userId)
                                .build());
                taskInstanceManagementService.rescheduleTask(TaskSchedulingPayload.builder()
                        .scheduleType(TaskSchedulingType.SCHEDULE)
                        .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                        .rescheduleAt(data.getRescheduleAt())
                        .build(), userId);
                if (StringUtils.isNotEmpty(data.getCommentContent())) {
                    taskInstanceManagementService.createCommentOnTask(userId, storedTaskInstance.getTaskInstanceId(), data.getCommentContent());
                }
                return storedTaskInstance;
            }
        });
    }

    private void validateRescheduleAt(long rescheduleAt) {
        Date rescheduleDate = new Date(rescheduleAt);
        Date maxPossibleRescheduleDate = DateUtils.addDaysToDate(
                new Date(),
                miscellaneous.getMaxPossibleRescheduleOffsetInDays()
        );

        if(rescheduleDate.after(maxPossibleRescheduleDate)) {
            throw LegionException.error(LegionTaskErrorCode.RESCHEDULE_DATE_EXCEEDS_LIMIT);
        }
    }

    private void validateRescheduleDate(Long rescheduleAt) {
        if (rescheduleAt != null) {
            validateRescheduleAt(rescheduleAt);
        }
    }

    private TaskCreationConfigParameters getTaskCreationParams(UserTaskCreationRequest data) {
        try {
            for (ActionToRemarkConfig config : LeadManagementConfiguration.getLeadCreation()) {
                if (config.getTaskType() == data.getUserGenTaskType()) {
                    return userTaskCreationConfig.getTaskCreationParametersList()
                            .stream()
                            .filter(utc -> (utc.getTaskType() == data.getUserGenTaskType()))
                            .findFirst()
                            .orElseThrow();
                }
            }
        } catch (Exception e) {
            log.error("You can only update an existing lending task, new leads can not be created");
            throw LegionException.error(LegionTaskErrorCode.TASK_CREATION_NOT_ALLOWED);
        }
        return null;
    }

    private void getAndValidateDuplicateTasks(TaskParams params, EntityType entityType) {

        TaskDefinitionInstance taskDefinitionInstance = validationService.validateAndGetTaskDefinition(params.getDefinitionId());
        Campaign campaign = validationService.validateAndGetCampaign(params.getCampaignId());

        List<DiscoveryTaskInstance> duplicateTasks = transitionValidator.getDuplicateTasks(
                entityType,
                params.getEntityId(),
                taskDefinitionInstance.getActionId(),
                campaign);

        for (DiscoveryTaskInstance task : duplicateTasks) {
            if (task.getTaskState().isDiscoverableState() || task.getTaskState().isAssignedState()) {
                throw LegionException.error(
                        task.getTaskState().isDiscoverableState()
                                ? LegionTaskErrorCode.DISCOVERABLE_TASK_ALREADY_PRESENT
                                : LegionTaskErrorCode.ASSIGNED_TASK_ALREADY_PRESENT,
                        Map.of("taskInstanceId", task.getTaskInstanceId()));
            }
        }
    }

    @Override
    public StoredTaskInstance deleteTask(ClientTaskDeleteRequest request) {
        StoredTaskInstance storedTaskInstance = fetchTaskInstance(request.getTaskInstanceId());
        return taskEngine.markTaskDeleted(storedTaskInstance, request);
    }

    @Override
    public StoredTaskInstance verifyTask(TaskManualVerificationRequest request) {
        StoredTaskInstance storedTaskInstance = fetchTaskInstance(request.getTaskInstanceId());
        return taskEngine.manualVerify(storedTaskInstance, request);
    }

    @Override
    public StoredTaskInstance expireTask(TaskExpireRequest request) {
        StoredTaskInstance storedTaskInstance = fetchTaskInstance(request.getTaskInstanceId());
        return taskEngine.markTaskExpired(storedTaskInstance, request);
    }

    @Override
    public StoredTaskInstance fetchTaskInstance(String taskInstanceId) {
        return taskInstanceRepository.get(taskInstanceId)
                .orElseThrow(() -> LegionException.error(CoreErrorCode.NOT_FOUND, Map.of(MESSAGE, "Invalid Task Instance Id")));
    }

    @Override
    public StoredTaskInstance verify(TaskCompleteRequest request) {
        StoredTaskInstance storedTaskInstance = fetchTaskInstance(request.getTaskInstanceId());
        request.setStoredTaskInstance(storedTaskInstance);
        return taskEngine.verificationStep(request);
    }

    private StoredTaskInstance handleMarkAvailable(TaskCreateRequest data, StoredTaskInstance instance) {
        StoredTaskInstance refreshed = fetchTaskInstance(instance.getTaskInstanceId());
        return taskEngine.markAvailable(
                refreshed,
                TaskMarkAvailableRequest.builder()
                        .markedBy(data.getTaskInstance().getCreatedBy())
                        .build()
        );
    }

    private StoredTaskInstance handlePhoneNumberEntityBoundedAssignment(StoredTaskInstance instance) {
        StoredTaskInstance refreshed = fetchTaskInstance(instance.getTaskInstanceId());
        return taskEngine.assignTask(
                refreshed,
                TaskAssignRequest.builder()
                        .assignedTo(refreshed.getCreatedBy())
                        .taskInstanceId(refreshed.getTaskInstanceId())
                        .build()
        );
    }

    private void handleReschedule(TaskCreateRequest data, StoredTaskInstance instance, String userId) {
        taskInstanceManagementService.rescheduleTask(
                TaskSchedulingPayload.builder()
                        .scheduleType(TaskSchedulingType.SCHEDULE)
                        .taskInstanceId(instance.getTaskInstanceId())
                        .rescheduleAt(data.getRescheduleAt())
                        .build(),
                userId
        );
    }

    private void handleComment(TaskCreateRequest data, StoredTaskInstance instance, String userId) {
        taskInstanceManagementService.createCommentOnTask(
                userId,
                instance.getTaskInstanceId(),
                data.getCommentContent()
        );
    }
}
