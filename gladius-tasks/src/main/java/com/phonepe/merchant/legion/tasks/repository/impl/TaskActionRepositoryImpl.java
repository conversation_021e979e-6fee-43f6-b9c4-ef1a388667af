package com.phonepe.merchant.legion.tasks.repository.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.legion.core.commands.LookupAccessCommands;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.functionmetrics.MonitoredFunction;
import org.hibernate.criterion.DetachedCriteria;

import java.util.List;
import java.util.Optional;
import java.util.function.UnaryOperator;

@Singleton
public class TaskActionRepositoryImpl implements TaskActionRepository {

    private final LookupAccessCommands<StoredTaskAction> accessor;
    @Inject
    public TaskActionRepositoryImpl(LookupDao<StoredTaskAction> lookupDao,
                                    FoxtrotEventIngestionService eventIngestionService) {
        this.accessor = new LookupAccessCommands<>(lookupDao,eventIngestionService);
    }

    @Override
    public StoredTaskAction save(StoredTaskAction storedTaskAction) {
        Optional<StoredTaskAction> saveStoredTaskAction = accessor.save(storedTaskAction);
        if (! saveStoredTaskAction.isPresent()) {
            throw LegionException.error(CoreErrorCode.DAO_ERROR);
        }
        return saveStoredTaskAction.get();
    }

    @Override
    @MonitoredFunction
    public Optional<StoredTaskAction> get(String actionId) {
        return accessor.get(actionId);
    }

    @Override
    public boolean update(String actionId, UnaryOperator<StoredTaskAction> mutator) {
        return accessor.update(actionId, mutator);
    }

    @Override
    public List<StoredTaskAction> getAllActions() {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(StoredTaskAction.class);
        return accessor.find(detachedCriteria);
    }
}
