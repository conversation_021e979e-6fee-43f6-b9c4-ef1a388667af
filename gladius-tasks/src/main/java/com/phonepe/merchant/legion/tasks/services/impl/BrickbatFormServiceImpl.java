package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.survey.request.FeedbackRequest;
import com.phonepe.merchant.gladius.models.survey.request.FormConfigRequest;
import com.phonepe.merchant.gladius.models.survey.response.FeedbackInstance;
import com.phonepe.merchant.gladius.models.survey.response.FormConfigInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.FormsDataLoader;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.models.BrickbatFeedbackFlatResponse;
import com.phonepe.merchant.legion.core.models.BrickbatFlatFeedbackRequest;
import com.phonepe.merchant.legion.core.models.BrickbatFormAndFeedbackResponse;
import com.phonepe.merchant.legion.core.models.BrickbatFormsRequestV2;
import com.phonepe.merchant.legion.core.models.BrickbatSubmitFeedbackRequest;
import com.phonepe.merchant.legion.core.models.FeedbackQuestionResponseAttributes;
import com.phonepe.merchant.legion.core.models.FormConfig;
import com.phonepe.merchant.legion.core.models.FormType;
import com.phonepe.merchant.legion.core.models.StoreData;
import com.phonepe.merchant.legion.core.services.BrickbatService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.services.FeedbackService;
import com.phonepe.merchant.legion.tasks.services.FormConfigService;
import com.phonepe.merchant.legion.tasks.utils.DistanceCalculatorUtils;
import com.phonepe.merchant.legion.tasks.services.BrickbatFormService;
import com.phonepe.models.common.Location;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.PhysicalStore;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.brickbat.models.feedback.StoredFeedback;
import com.phonepe.platform.brickbat.models.feedback.StoredSurvey;
import com.phonepe.platform.brickbat.models.question.FeedbackReasonQuestion;
import com.phonepe.platform.brickbat.models.question.MCQQuestion;
import com.phonepe.platform.brickbat.models.question.Question;
import com.phonepe.platform.brickbat.models.question.TextQuestion;
import com.phonepe.platform.brickbat.models.question.options.Option;
import com.phonepe.platform.brickbat.models.question.questionresponse.DateQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.EntityRatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.FeedbackReasonQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQGridQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.MCQQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.QuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.QuestionResponseVisitor;
import com.phonepe.platform.brickbat.models.question.questionresponse.RatingQuestionResponse;
import com.phonepe.platform.brickbat.models.question.questionresponse.TextQuestionResponse;
import com.phonepe.platform.brickbat.models.user.request.CreateFeedbackRequestV2;
import com.phonepe.platform.brickbat.models.user.response.CreateFeedbackResponse;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ANCHOR_END;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ANCHOR_START;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.PHONE_NUMBER_REGEX;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.ALL_BU_ALL_AGENTS;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.FEEDBACK_REQUEST_CONTEXT_ASSET_ID;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.FEEDBACK_REQUEST_CONTEXT_FORM_TYPE;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toCreateFeedbackAndAuditFailureFoxtrotEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toGetBrickbatFormAndFeedbackFailureFoxtrotEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toGetFlatMerchantStoreFeedbackFailureFoxtrotEvent;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class BrickbatFormServiceImpl implements BrickbatFormService {

    private final LegionService legionService;
    private final MerchantService merchantService;
    private final FeedbackService feedbackService;
    private final FormConfigService formConfigService;
    private final BrickbatService brickbatService;
    private final FoxtrotEventExecutor eventExecutor;
    private final Miscellaneous miscellaneous;

    public String validateLocation(FormConfig formConfig, Location agentLocation, Location storeLocation) {

        if (Boolean.TRUE.equals(formConfig.getLocationValidation()) && (DistanceCalculatorUtils.getDistanceFromLocationInMeters(agentLocation, storeLocation) > formConfig.getRadius())) {
            throw LegionException.error(LegionTaskErrorCode.DISTANCE_TOO_FAR);
        }

        return formConfig.getUrl();

    }

    @SuppressWarnings("java:S1874")
    private String getFormForStore(FormType formType, AgentProfile agentProfile , StoreData storeData, Location location) {
        PhysicalStore store = merchantService.getStoreDetails(storeData.getMerchantId(), storeData.getStoreId());
        MerchantProfile merchant = merchantService.getMerchantFactDetails(storeData.getMerchantId());
        Location storeLocation = Location.builder().latitude(store.getLatitude()).longitude(store.getLongitude()).build();

        //try to get with key agentType:businessUnit
        String key = agentProfile.getAgentType() + ":" + merchant.getBusinessUnit();
        FormConfig formConfig = FormsDataLoader.getFormConfig(formType, key);
        if (formConfig != null) {
            return validateLocation(formConfig, location, storeLocation);
        }

        //try to get with key agentType
        key = agentProfile.getAgentType().toString();
        formConfig = FormsDataLoader.getFormConfig(formType, key);
        if (formConfig != null) {
            return validateLocation(formConfig, location, storeLocation);
        }

        //try to get with key businessUnit
        key = merchant.getBusinessUnit().toString();
        formConfig = FormsDataLoader.getFormConfig(formType, key);
        if (formConfig != null) {
            return validateLocation(formConfig, location, storeLocation);
        }

        //try to get with key ALL_BU_ALL_AGENTS
        key = ALL_BU_ALL_AGENTS;
        formConfig = FormsDataLoader.getFormConfig(formType, key);
        if (formConfig != null) {
            return validateLocation(formConfig, location, storeLocation);
        }

        return null;

    }

    private String getFormWithoutStore(FormType formType, AgentProfile agentProfile) {

        //try to get with key agentType
        String key = agentProfile.getAgentType().toString();
        FormConfig formConfig= FormsDataLoader.getFormConfig(formType, key);
        if (formConfig != null) {
            return formConfig.getUrl();
        }

        //try to get with key ALL_BU_ALL_AGENTS
        key = ALL_BU_ALL_AGENTS;
        formConfig = FormsDataLoader.getFormConfig(formType, key);
        if(formConfig != null) {
            return formConfig.getUrl();
        }

        return null;
    }

    @MonitoredFunction
    public String getFormUrl(FormType formType, String agentId, StoreData storeData, Location location) {

        AgentProfile profile = legionService.getAgentProfile(agentId);
        String formUrl = (storeData != null) ? getFormForStore(formType, profile, storeData, location) : getFormWithoutStore(formType, profile);

        if(formUrl == null) {
            throw LegionException.error(LegionTaskErrorCode.FORM_NOT_AVAILABLE);
        }
        return formUrl;
    }

    @SuppressWarnings("java:S1874")
    private FormConfig getFormConfigFromConfigurationFile(FormType formType, AgentProfile agentProfile , StoreData storeData) {
        if (Objects.isNull(agentProfile)) {
            throw LegionException.error(LegionTaskErrorCode.INVALID_AGENT_ID);
        }
        List<String> keys = new ArrayList<>();
        if (Objects.nonNull(storeData)) {
            MerchantProfile merchant = merchantService.getMerchantFactDetails(storeData.getMerchantId());
            keys.add(String.join(":", agentProfile.getAgentType().name(), merchant.getBusinessUnit().name()));
            keys.add(agentProfile.getAgentType().name());
            keys.add(merchant.getBusinessUnit().name());
        } else {
            keys.add(agentProfile.getAgentType().name());
        }
        keys.add(ALL_BU_ALL_AGENTS);
        for (String key : keys) {
            FormConfig formConfig = FormsDataLoader.getFormConfig(formType, key);
            if (formConfig != null) { return formConfig; }
        }
        throw LegionException.error(LegionTaskErrorCode.FORM_FOR_FORM_TYPE_NOT_AVAILABLE);
    }

    private String getFormUrlFromConfigurationFile(FormConfig formConfigFromConfigurationFile, StoreData storeData, Location location) {
        String brickbatFormUrl;
        if (Objects.nonNull(storeData)) {
            if (Objects.isNull(location)) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_LOCATION_CHECK_PARAMS);
            }
            PhysicalStore store = merchantService.getStoreDetails(storeData.getMerchantId(), storeData.getStoreId());
            Location storeLocation = Location.builder().latitude(store.getLatitude()).longitude(store.getLongitude()).build();
            brickbatFormUrl =  validateLocation(formConfigFromConfigurationFile, location, storeLocation);
        } else {
            brickbatFormUrl = formConfigFromConfigurationFile.getUrl();
        }
        if (Objects.isNull(brickbatFormUrl)) {
            throw LegionException.error(LegionTaskErrorCode.FORM_FOR_FORM_TYPE_NOT_AVAILABLE);
        }
        return brickbatFormUrl;
    }

    private List<FeedbackQuestionResponseAttributes> getFlatFeedbackQuestionResponses(StoredFeedback storedFeedback) {
        StoredSurvey storedSurvey = (StoredSurvey) storedFeedback;
        Map<String, QuestionResponse> map = storedSurvey.getUserResponses();
        String questionIds = String.join(",", map.keySet());
        List<Question> questionsList = brickbatService.getQuestionsFromQuestionIds(questionIds).getData();
        Map<String, Question> questionMap = questionsList.stream()
                .filter(question -> map.containsKey(question.getQuestionId()))
                .collect(Collectors.toMap(Question::getQuestionId, Function.identity()));
        List<FeedbackQuestionResponseAttributes> result = new ArrayList<>();
        for (Map.Entry<String, QuestionResponse> responseEntry : map.entrySet()) {
            Question question = questionMap.get(responseEntry.getKey());
            FeedbackQuestionResponseAttributes questionResponseAttributes = responseEntry.getValue().accept(new QuestionResponseVisitor<FeedbackQuestionResponseAttributes>() {
                @Override
                public FeedbackQuestionResponseAttributes visit(MCQQuestionResponse mcqQuestionResponse) {
                    String mcqResponseOption = mcqQuestionResponse.getAnswers().get(0).getKey();
                    MCQQuestion mcqQuestion = (MCQQuestion) question;
                    Optional<Option> mcqQuestionOption = mcqQuestion.getOptions().values().stream().filter(mcqOption -> mcqOption.getKey().equals(mcqResponseOption)).findFirst();

                    return FeedbackQuestionResponseAttributes.builder()
                            .displayName(mcqQuestion.getTitle())
                            .value(mcqQuestionOption.isPresent() ? mcqQuestionOption.get().getDisplayText() : "")
                            .build();
                }

                @Override
                public FeedbackQuestionResponseAttributes visit(TextQuestionResponse textQuestionResponse) {
                    Matcher m = (Pattern.compile(PHONE_NUMBER_REGEX)).matcher(textQuestionResponse.getText());
                    String questionResponse;
                    TextQuestion textQuestion = (TextQuestion) question;
                    if (m.matches()) {
                        questionResponse = ANCHOR_START + textQuestionResponse.getText() + "\'>" + textQuestionResponse.getText() + ANCHOR_END;
                    } else {
                        questionResponse = textQuestionResponse.getText();
                    }
                    return FeedbackQuestionResponseAttributes.builder()
                            .value(questionResponse)
                            .displayName(textQuestion.getTitle()).build();
                }

                @Override
                public FeedbackQuestionResponseAttributes visit(FeedbackReasonQuestionResponse feedbackReasonQuestionResponse) {
                    return FeedbackQuestionResponseAttributes.builder()
                            .value(feedbackReasonQuestionResponse.getFeedbackReasons())
                            .displayName(((FeedbackReasonQuestion) question).getDefaultTitle()).build();
                }

                @Override
                public FeedbackQuestionResponseAttributes visit(EntityRatingQuestionResponse entityRatingQuestionResponse) {
                    return FeedbackQuestionResponseAttributes.builder()
                            .value(entityRatingQuestionResponse.getRating())
                            .displayName(question.getTitle()).build();
                }

                @Override
                public FeedbackQuestionResponseAttributes visit(RatingQuestionResponse ratingQuestionResponse) {
                    return FeedbackQuestionResponseAttributes.builder()
                            .value(ratingQuestionResponse.getRating())
                            .displayName(question.getTitle()).build();
                }

                @Override
                public FeedbackQuestionResponseAttributes visit(MCQGridQuestionResponse mcqGridQuestionResponse) {
                    return FeedbackQuestionResponseAttributes.builder()
                            .value(mcqGridQuestionResponse.getAnswers())
                            .displayName(question.getTitle()).build();
                }

                @Override
                public FeedbackQuestionResponseAttributes visit(DateQuestionResponse dateQuestionResponse) {
                    return FeedbackQuestionResponseAttributes.builder()
                            .value(new Date(dateQuestionResponse.getEpoch()))
                            .displayName(question.getTitle()).build();
                }
            });

            result.add(questionResponseAttributes);
        }
        return result;
    }

    private void validateLocationBeforeFeedbackSubmit(double radius, Location agentLocation, Location storeLocation) {
        if (DistanceCalculatorUtils.getDistanceFromLocationInMeters(agentLocation, storeLocation) > radius) {
            throw LegionException.error(LegionTaskErrorCode.DISTANCE_TOO_FAR);
        }
    }

    private void validateLocationParamsInBrickbatSubmitFeedbackRequest(BrickbatSubmitFeedbackRequest request) {
        if (request.isLocationValidation() && (Objects.isNull(AuthUserDetails.getLocationFromClientContext(request.getClientContext()))
                    || Objects.isNull(request.getMerchant()) || StringUtils.isEmpty(request.getMerchant().getMerchantId())
                    || StringUtils.isEmpty(request.getMerchant().getStoreId()))) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_LOCATION_CHECK_PARAMS);
            }

    }

    private void performLocationValidationChecksForFeedbackSubmitRequest(BrickbatSubmitFeedbackRequest request) {
        validateLocationParamsInBrickbatSubmitFeedbackRequest(request);
        if (request.isLocationValidation()) {
            PhysicalStore store = merchantService.getStoreDetails(request.getMerchant().getMerchantId(), request.getMerchant().getStoreId());
            Location storeLocation = Location.builder().latitude(store.getLatitude()).longitude(store.getLongitude()).build();
            double formSubmitLocationRadiusInMeters = miscellaneous.getFormSubmitLocationRadiusInMeters();
            validateLocationBeforeFeedbackSubmit(formSubmitLocationRadiusInMeters,
                    AuthUserDetails.getLocationFromClientContext(request.getClientContext()), storeLocation);
        }
    }

    private GenericResponse<CreateFeedbackResponse> createFeedbackInBrickbat(List<CreateFeedbackRequestV2> requests,
                                                                             FormType formType, String assetId) {
        for (CreateFeedbackRequestV2 item : requests) {
            if (Objects.isNull(item.getContext())) {
                item.setContext(new HashMap<>());
            }
            item.getContext().put(FEEDBACK_REQUEST_CONTEXT_FORM_TYPE, formType);
            item.getContext().put(FEEDBACK_REQUEST_CONTEXT_ASSET_ID, assetId);
        }
        String endUserIdRequiredForBrickbat = Objects.nonNull(requests.get(0)) ? requests.get(0).getUserId() : "";
        return brickbatService.createSurveyBulkForAgentUser(requests, endUserIdRequiredForBrickbat);
    }

    @Override
    @MonitoredFunction
    public BrickbatFormAndFeedbackResponse getBrickbatFormAndFeedback(BrickbatFormsRequestV2 request, String agentId, Location location) {
        try {
            AgentProfile profile = legionService.getAgentProfile(agentId);
            FormConfig formConfigFromConfigurationFile = getFormConfigFromConfigurationFile(request.getFormType(), profile, request.getMerchant());
            String brickbatFormUrl = getFormUrlFromConfigurationFile(formConfigFromConfigurationFile, request.getMerchant(), location);
            boolean isFeedbackAuditConfiguredInConfigurationFile = formConfigFromConfigurationFile.isFeedbackAuditConfigured();
            FormConfigInstance formConfigFromCache = formConfigService.getFormConfigFromCache(request.getFormType().name());
            if (!isFeedbackAuditConfiguredInConfigurationFile || Objects.isNull(formConfigFromCache)) {
                return BrickbatFormAndFeedbackResponse.builder()
                        .feedbackAuditConfigured(false)
                        .brickbatFormUrl(brickbatFormUrl).build();
            }
            if (Objects.isNull(request.getMerchant()) || StringUtils.isEmpty(request.getMerchant().getMerchantId())
                    || StringUtils.isEmpty(request.getMerchant().getStoreId())) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_ASSET_OR_MERCHANT_STORE_FOR_CONFIGURED_FORM);
            }
            String assetId = String.join("_", request.getMerchant().getMerchantId(), request.getMerchant().getStoreId());
            FeedbackInstance feedbackInstance = feedbackService.getFeedback(request.getFormType().name(), assetId);
            if (Objects.nonNull(feedbackInstance)) {
                GenericResponse<StoredFeedback> storedFeedbackGenericResponse = brickbatService.getSurveyResult(feedbackInstance.getFeedbackId());
                if (Objects.nonNull(storedFeedbackGenericResponse.getData())) {
                    StoredSurvey storedSurvey = (StoredSurvey) storedFeedbackGenericResponse.getData();
                    Map<String, QuestionResponse> map = storedSurvey.getUserResponses();
                    return BrickbatFormAndFeedbackResponse.builder()
                            .userId(agentId)
                            .feedbackAuditConfigured(true)
                            .existingFeedbackPresent(true)
                            .brickbatFormUrl(brickbatFormUrl)
                            .formAssetType(formConfigFromCache.getFormAssetType())
                            .feedbackUserResponse(new ArrayList<>(map.values())).build();
                }
            }
            return BrickbatFormAndFeedbackResponse.builder()
                    .userId(agentId)
                    .feedbackAuditConfigured(true)
                    .existingFeedbackPresent(false)
                    .brickbatFormUrl(brickbatFormUrl)
                    .formAssetType(formConfigFromCache.getFormAssetType()).build();
        } catch (Exception e) {
            eventExecutor.ingest(toGetBrickbatFormAndFeedbackFailureFoxtrotEvent(request, e));
            throw e;
        }
    }

    @Override
    @MonitoredFunction
    public BrickbatFeedbackFlatResponse getFlatMerchantStoreFeedback(BrickbatFlatFeedbackRequest request) {
        try {
            if (Objects.isNull(request.getFormType())) {
                throw LegionException.error(LegionTaskErrorCode.FORM_FOR_FORM_TYPE_NOT_AVAILABLE);
            }
            FormConfigInstance formConfigFromCache = formConfigService.getFormConfigFromCache(request.getFormType().name());
            if (Objects.isNull(formConfigFromCache)) {
                return BrickbatFeedbackFlatResponse.builder()
                        .feedbackAuditConfigured(false).build();
            }
            if (Objects.isNull(request.getMerchant()) || StringUtils.isEmpty(request.getMerchant().getMerchantId())
                    || StringUtils.isEmpty(request.getMerchant().getStoreId())) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_ASSET_OR_MERCHANT_STORE_FOR_CONFIGURED_FORM);
            }
            String assetId = String.join("_", request.getMerchant().getMerchantId(), request.getMerchant().getStoreId());
            FeedbackInstance feedbackInstance = feedbackService.getFeedback(request.getFormType().name(), assetId);
            if (Objects.isNull(feedbackInstance)) {
                return BrickbatFeedbackFlatResponse.builder()
                        .feedbackAuditConfigured(true)
                        .existingFeedbackPresent(false).build();
            }
            GenericResponse<StoredFeedback> storedFeedbackGenericResponse = brickbatService.getSurveyResult(feedbackInstance.getFeedbackId());
            if (Objects.isNull(storedFeedbackGenericResponse.getData())) {
                throw LegionException.error(LegionTaskErrorCode.UNABLE_TO_RETRIEVE_FORM_DATA_FROM_BRICKBAT);
            }
            return BrickbatFeedbackFlatResponse.builder()
                    .feedbackAuditConfigured(true)
                    .existingFeedbackPresent(true)
                    .feedbackQuestionResponses(getFlatFeedbackQuestionResponses(storedFeedbackGenericResponse.getData())).build();
        } catch (Exception e) {
            eventExecutor.ingest(toGetFlatMerchantStoreFeedbackFailureFoxtrotEvent(request, e));
            throw e;
        }
    }

    @Override
    @MonitoredFunction
    public CreateFeedbackResponse createFeedbackAndAudit(BrickbatSubmitFeedbackRequest request, String agentId) {
        try {
            performLocationValidationChecksForFeedbackSubmitRequest(request);
            FormConfigInstance formConfigFromCache = null;
            String assetId = request.generateAssetId();
            if (Objects.nonNull(request.getFormType())) {
                formConfigFromCache = formConfigService.getFormConfigFromCache(request.getFormType().name());
                if (Objects.nonNull(formConfigFromCache) && StringUtils.isEmpty(assetId)) {
                    throw LegionException.error(LegionTaskErrorCode.INVALID_ASSET_OR_MERCHANT_STORE_FOR_CONFIGURED_FORM);
                }
            }
            GenericResponse<CreateFeedbackResponse> response = createFeedbackInBrickbat(request.getFeedbackRequests(), request.getFormType(), assetId);
            if (Objects.isNull(request.getFormType()) || Objects.isNull(formConfigFromCache)) { return response.getData(); }
            String brickbatFeedbackId = response.getData().getCreateFeedbackStatus().entrySet().iterator().next().getValue().getMessage();
            FeedbackRequest feedbackRequest = FeedbackRequest.builder()
                    .formType(formConfigFromCache.getFormType())
                    .feedbackAssetId(assetId)
                    .feedbackId(brickbatFeedbackId)
                    .campaignId(formConfigFromCache.getCampaignId()).build();
            feedbackService.saveOrUpdateFeedback(feedbackRequest, agentId);
            return response.getData();
        } catch (Exception e) {
            eventExecutor.ingest(toCreateFeedbackAndAuditFailureFoxtrotEvent(request, e));
            throw e;
        }
    }

    @Override
    @MonitoredFunction
    public FormConfigInstance createFormConfig(FormConfigRequest request, String actor) {
        return formConfigService.saveOrUpdateFormConfig(request, actor);
    }
}
