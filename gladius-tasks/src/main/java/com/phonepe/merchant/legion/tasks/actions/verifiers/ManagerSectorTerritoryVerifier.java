package com.phonepe.merchant.legion.tasks.actions.verifiers;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.VerifierResponse;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionVerifierMarker;
import com.phonepe.models.merchants.tasks.EntityType;

import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MANAGER_SECTOR_TERRITORY_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.AGENT_PROFILE_NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_VERIFIER;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.MANAGER_PROFILE_NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.NO_MANAGER_SECTORS;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.TASK_CREATION_OUTSIDE_MANAGER_TERRITORY;

@ActionVerifierMarker(name = MANAGER_SECTOR_TERRITORY_VERIFIER)
public class ManagerSectorTerritoryVerifier extends ActionVerifier {

    private final LegionService legionService;
    private final AtlasService atlasService;

    @Inject
    public ManagerSectorTerritoryVerifier(LegionService legionService, AtlasService atlasService) {
        this.legionService = legionService;
        this.atlasService = atlasService;
    }

    @Override
    public void validate(EntityType entityType, VerificationConfig verificationConfig) {
        if (entityType != EntityType.PHONE_NUMBER) {
            throw LegionException.error(INVALID_TASK_VERIFIER,
                    Map.of("details", entityType.name() + " cannot be used with this verifier"));
        }
    }

    @Override
    public VerifierResponse verify(TaskCompleteRequest taskCompleteRequest, VerificationConfig verificationConfig, Map<String, Object> context) {
        return VerifierResponse.builder()
                .verified(true)
                .context(context)
                .build();
    }


    @Override
    public boolean validateTaskCreation(CreateTaskInstanceRequest instanceRequest, TaskActionInstance actionInstance) {
        String createdBy = instanceRequest.getCreatedBy();
        AgentProfile profile = legionService.getAgentProfile(createdBy);
        if (profile == null) {
            throw LegionException.error(AGENT_PROFILE_NOT_FOUND);
        }

        AgentProfile managerProfile = legionService.getAgentProfile(profile.getManagerId());
        if (managerProfile == null) {
            throw LegionException.error(MANAGER_PROFILE_NOT_FOUND);
        }

        List<String> managerSectors = managerProfile.getSectors();
        if (managerSectors == null || managerSectors.isEmpty()) {
            throw LegionException.error(NO_MANAGER_SECTORS);
        }

        List<String> requestedSectors = atlasService.getSectorIdByLatLong(
                instanceRequest.getTransactionLocation().getLat(),
                instanceRequest.getTransactionLocation().getLon()
        );

        if (requestedSectors.stream().noneMatch(managerSectors::contains)) {
            throw LegionException.error(TASK_CREATION_OUTSIDE_MANAGER_TERRITORY);
        }

        return true;
    }
}
