package com.phonepe.merchant.legion.tasks.eventbasedlogic;

import com.phonepe.growth.neuron.pulse.Signal;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NeuronUtils {


    public static final String TAGGING_MXN_TO_EXTERNAL_ENTITY = "TAGGING_MXN_TO_EXTERNAL_ENTITY";
    public static final String TAGGING_STORE_TO_EXTERNAL_ENTITY = "TAGGING_STORE_TO_EXTERNAL_ENTITY";
    public static final String QC_TASK_CREATION_ENTITY = "QC_TASK_CREATION_ENTITY";
    public static final String GLADIUS_TASK_CREATION_ENTITY = "GLADIUS_TASK_CREATION_ENTITY";


    public static Map<String,String> getKeyValueMap(List<Signal> signalsList) {
        Map<String,String> map = new HashMap<>();
        signalsList.forEach(signal -> map.put(signal.getName(),signal.getValue().toString()));
        return map;
    }

}
